import { Hono } from 'hono';
import { economists } from '@/server/db/schema';
import { getDbFromContext } from '../data';
import { requireAuth } from '../auth/middleware';

const economistsRouter = new Hono();

// GET /api/economists - Get all economists
economistsRouter.get('/', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				success: false,
				error: 'Database connection not available'
			}, 503);
		}

		const allEconomists = await db.select().from(economists);

		return c.json({
			success: true,
			data: allEconomists,
			message: 'Economists fetched successfully',
		});
	} catch (error) {
		console.error('Error fetching economists:', error);

		return c.json({
			success: false,
			error: 'Failed to fetch economists',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

// POST /api/economists - Create new economist (requires auth)
economistsRouter.post('/', requireAuth(), async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				success: false,
				error: 'Database connection not available'
			}, 503);
		}

		const economistData = await c.req.json();

		const economist = {
			name: economistData.name || 'Dr. Test Ekonomi',
			bio: economistData.bio || 'Test economist for development',
			youtubeChannelId: economistData.youtubeChannelId || `test${Date.now()}`,
			youtubeChannelName: economistData.youtubeChannelName || 'Test Channel',
			youtubeChannelUrl: economistData.youtubeChannelUrl || 'https://youtube.com/test',
			avatarUrl: economistData.avatarUrl || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
			subscriberCount: economistData.subscriberCount || 1000,
			isVerified: economistData.isVerified ?? false,
			isActive: economistData.isActive ?? true,
		};

		const result = await db.insert(economists).values(economist).returning();

		return c.json({
			success: true,
			data: result[0],
			message: 'Economist created successfully',
		}, 201);
	} catch (error) {
		console.error('Error creating economist:', error);

		return c.json({
			success: false,
			error: 'Failed to create economist',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

export default economistsRouter;
