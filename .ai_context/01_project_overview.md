# 01_project_overview.md

1\. Project Vision

The project is a web platform designed to provide a **"YouTube economist trust score,"** offering users a clear understanding of economist accuracy based on a **timeline showing their past predictions.** It will present, analyze, and rank YouTube economy economists by leveraging pre-processed data derived from their video content. Initially focusing on Turkish-language economists, the platform aims to expand to other languages in the future. All primary content and UI will be in Turkish.

The core of the platform's functionality relies on a database that is populated by an external system. This database will contain:

- Transcripts of YouTube videos from a curated list of approximately 20-30 economists.
- Extracted buy/sell advice for various investment instruments (stocks, cryptocurrencies, ETFs, etc.), including the timing of the advice and the economist's predicted timeframe for the outcome.
- Other structured data and summaries from the video content.

This platform will then:

- Enable users to access and view this detailed information.
- Continuously track and evaluate the accuracy of the stored predictions on a daily basis by comparing them against actual market performance.
- Present users with detailed summaries of videos, the advice given, and any other significant takeaways, including direct links to relevant segments within the YouTube videos (assuming these links are part of the provided data).
- Generate rankings of economists based on their prediction accuracy over different time periods.
- Provide dedicated pages for individual investment instruments (e.g., \$GOLD), which will display:
  &#x20; \* Aggregated sentiment (positive/negative views) from economists over various periods (last week, month, year), based on the provided data.
  &#x20; \* Historical accuracy of economists concerning that specific instrument.
  &#x20; \* Trend analysis of sentiment and interest in the instrument over time.
- Utilize a robust tagging and taxonomy system (expected to be part of the incoming data structure) for all video content to enable detailed historical data analysis and searching.

The platform will use a scoring system, for example, awarding points for accurate predictions and correct timing, and deducting points for incorrect advice, based on its ongoing analysis.

## 2. Target Users

The primary target users are Turkish YouTube watchers who are interested in economy and politics videos and are actively seeking reliable investment advice and analysis.

## 3. Core Problem it Solves

The platform addresses the following key problems for its target users:

- **Information Overload & Complexity:** It's challenging and time-consuming for individuals to follow numerous economists and synthesize their diverse, often conflicting, opinions. This platform will provide a centralized place for structured information.
- **Lack of Verifiable History & Trust:** There's often no easy way to verify economists' past predictions or track their long-term accuracy, making it difficult to assess their reliability and trustworthiness. This platform provides transparency based on the historical data it analyzes.
- **Time-Saving Analysis & Specific Inquiry:** Users need a way to quickly understand opinions and access detailed analyses. The chatbot feature will further allow them to get answers to specific questions about video content or economist history efficiently. Discoverability of trending topics is also enhanced through dynamic navigation.

## 4. Key Features (High-Level)

- **Subscription Model:**
  &#x20; \* Public access to limited recent data (e.g., economists' accuracy rankings for the last month, basic trust scores).
  &#x20; \* Paid subscription for access to comprehensive historical data, detailed prediction timelines, advanced chatbot features, and more in-depth analysis.
- **User Accounts/Authentication:** User registration, login, profile management, and subscription management.
- **Economist Profile Pages:** Dedicated pages displaying information, their "trust score," historical performance (as calculated by this platform), a timeline of past predictions, and links to all analyzed videos/predictions for each economist.
- **"Trending" Section & Dynamic Navigation:**
  &#x20; \* A dedicated section for the most-watched or impactful economy-related videos/topics.
  &#x20; \* **The top navigation bar will feature "Trending" as a fixed item, with subsequent navigation links dynamically populated based on the most discussed topics/investment instruments of the week.**
- **Video Detail Pages:**
  &#x20; \* Display of full video transcript (provided from the database).
  &#x20; \* Display of AI-generated summary of the video content (provided from the database).
  &#x20; \* Clearly presented claims, predictions, and investment advice (from the database).
  &#x20; \* **Platform-generated historical accuracy check** for the claims made in the video by comparing predictions to market data.
  &#x20; \* Display of analysis of commentary on current political events and market conditions relevant to the advice (provided from the database).
  &#x20; \* Comprehensive tags for data mapping, filtering, and searching (utilizing tags from the database).
- **Ranking Dashboards & "Trust Score" Listings:** Interactive dashboards showing economist accuracy rankings and trust scores (calculated by this platform) across various customizable timeframes (e.g., weekly, monthly, quarterly, yearly).
- **Investment Instrument Pages:**
  &#x20; \* Graphs and data on the most discussed investment tools (e.g., "most mentioned stocks last week"), based on database information.
  &#x20; \* Trend analysis for specific instruments, showing shifts in economist sentiment and interest over time, based on platform analysis of historical data.
- **Advanced Search and Filtering:** Robust capabilities to search and filter content by economist, investment instrument, date range, keywords, tags, prediction accuracy, trust score, etc.
- **Interactive Chatbot (Q\&A):**
  &#x20; \* **Unpaid/Not Logged-in Users:** Access to a chatbot that can answer a predefined set of 5-10 questions about videos or economists, with responses drawn from a cached data source.
  &#x20; \* **Paid/Subscribed Users:** Ability to ask free-form questions to the chatbot. The chatbot will utilize an LLM and a vector database (containing indexed website content, video analyses, economist data, etc.) to generate answers.
- **User Comments Section:** Logged-in users can add comments and engage in discussions under video analysis pages (potentially using a third-party system like Comentario).
- **Notification System (Future Enhancement):** Users can opt-in for notifications (e.g., push, email) for new video analyses becoming available for followed economists, significant prediction outcomes determined by the platform, or major shifts in rankings/trust scores.
