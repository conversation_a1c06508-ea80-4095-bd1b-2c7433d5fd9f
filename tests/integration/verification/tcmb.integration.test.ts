import { describe, it, expect, beforeAll } from 'vitest';
import { fetchTcmbData } from '../../../src/server/services/verification/tcmb';

// This test will only run if TCMB_API_KEY is set in the environment
const apiKey = process.env.TCMB_API_KEY;

describe.skipIf(!apiKey)('TCMB Integration', () => {
  beforeAll(() => {
    if (!apiKey) {
      console.log('⚠️ TCMB_API_KEY not found in environment, skipping TCMB integration tests');
    } else {
      console.log('✅ TCMB_API_KEY found, running TCMB integration tests');
    }
  });

  it('fetches USD/TRY exchange rate for a real date range', async () => {
    // Use the exact date range from the working curl example
    const startDateStr = '01-10-2017';
    const endDateStr = '01-11-2017';

    console.log(`Fetching TCMB data from ${startDateStr} to ${endDateStr}`);

    const data = await fetchTcmbData({
      series: 'TP.DK.USD.A', // Use the series from working example
      startDate: startDateStr,
      endDate: endDateStr,
    }, apiKey!);

    expect(Array.isArray(data)).toBe(true);
    expect(data.length).toBeGreaterThan(0);
    expect(data[0]).toHaveProperty('Tarih');
    expect(data[0]).toHaveProperty('TP_DK_USD_A');

    // Find the first non-null entry for validation
    const validEntry = data.find(item => item['TP_DK_USD_A'] !== null);
    expect(validEntry).toBeDefined();
    expect(typeof validEntry!['TP_DK_USD_A']).toBe('string'); // TCMB returns string values
    const valueStr = validEntry!['TP_DK_USD_A'];
    expect(parseFloat(typeof valueStr === 'string' ? valueStr : String(valueStr))).toBeGreaterThan(0);

    console.log(`✅ Received ${data.length} exchange rate records`);
    console.log(`Sample data: ${JSON.stringify(data[0], null, 2)}`);
    console.log(`Valid entry example: ${JSON.stringify(validEntry, null, 2)}`);
  });
});
