/**
 * Videos Service
 *
 * Main service for videos domain. Provides high-level business logic operations
 * for videos, combining queries and formatters to deliver clean APIs.
 */

import type { DbConnection } from '@/server/db';
import type { QueryOptions, ServiceResult } from '../shared/types';

// Import queries from service layer
import {
	findRecentVideosWithEconomist,
	findVideosByEconomist,
	findVideoById,
	findPredictionsForVideos,
	findVideosWithPredictionCounts,
	countVideos,
} from './queries';

// Import formatters
import {
	formatVideo,
	formatVideoWithPredictions,
	formatVideoForHomepage,
	formatVideoListItem,
	formatVideoForSearch,
	formatVideoForSitemap,
	formatVideoForRSS,
	type FormattedVideo,
	type VideoForHomepage,
	type VideoWithPredictions,
	type VideoForBuild,
} from './formatters';

// ============================================================================
// HIGH-LEVEL SERVICE FUNCTIONS
// ============================================================================

/**
 * Get recent videos for homepage
 */
export async function getRecentVideos(db: DbConnection, limit = 6): Promise<VideoForHomepage[]> {
	console.log('getRecentVideos called with limit:', limit);

	const recentVideos = await findRecentVideosWithEconomist(db, limit);
	console.log('Found videos:', recentVideos.length);

	if (recentVideos.length === 0) {
		return [];
	}

	// Fetch predictions for these videos
	const videoIds = recentVideos.map((v) => v.videos.id).filter((id) => id !== null) as number[];
	const videoPredictions = await findPredictionsForVideos(db, videoIds);

	console.log('Found predictions:', videoPredictions.length);

	// Format the data for the frontend
	return recentVideos.map((videoData) => {
		const videoPreds = videoPredictions.filter((p) => p.videoId === videoData.videos.id);
		return {
			...formatVideoForHomepage(videoData),
			predictions: videoPreds.map((p) => ({
				id: p.id,
				title: p.title,
				description: p.description,
				category: p.category,
				predictionType: p.predictionType,
				targetValue: p.targetValue,
				targetDate: p.targetDate,
				confidenceLevel: p.confidenceLevel,
				status: p.status,
				accuracyScore: p.accuracyScore,
			})),
		};
	});
}

/**
 * Get video by ID with full details
 */
export async function getVideoById(
	db: DbConnection,
	id: number
): Promise<VideoWithPredictions | null> {
	const videoData = await findVideoById(db, id);
	if (!videoData) {
		return null;
	}

	const predictions = await findPredictionsForVideos(db, [id]);
	return formatVideoWithPredictions(videoData.videos, videoData.economists, predictions);
}

/**
 * Get videos by economist
 */
export async function getVideosByEconomist(
	db: DbConnection,
	economistId: number,
	options: QueryOptions = {}
): Promise<FormattedVideo[]> {
	const videos = await findVideosByEconomist(db, economistId, options);
	return videos.map((video) => formatVideo(video));
}

/**
 * Get videos with pagination
 */
export async function getVideosWithPagination(
	db: DbConnection,
	options: QueryOptions = {}
): Promise<ServiceResult<FormattedVideo>> {
	const { page = 1, pageSize = 20 } = options;
	const offset = (page - 1) * pageSize;

	const [videosData, total] = await Promise.all([
		findVideosWithPredictionCounts(db, { ...options, limit: pageSize, offset }),
		countVideos(db, options),
	]);

	const totalPages = Math.ceil(total / pageSize);

	return {
		data: videosData.map((data) => formatVideo(data.video, data.economist)),
		pagination: {
			page,
			pageSize,
			total,
			totalPages,
			hasNext: page < totalPages,
			hasPrev: page > 1,
		},
	};
}

/**
 * Get videos for search
 */
export async function getVideosForSearch(db: DbConnection) {
	const videosData = await findRecentVideosWithEconomist(db, 1000);
	return videosData.map((data) => formatVideoForSearch(data.videos, data.economists));
}

/**
 * Get videos for sitemap
 */
export async function getVideosForSitemap(db: DbConnection) {
	const videosData = await findRecentVideosWithEconomist(db, 1000);
	return videosData.map((data) => formatVideoForSitemap(data.videos));
}

/**
 * Get videos for RSS feed
 */
export async function getVideosForRSS(db: DbConnection, limit = 50) {
	const videosData = await findRecentVideosWithEconomist(db, limit);
	return videosData.map((data) => formatVideoForRSS(data.videos, data.economists));
}

// ============================================================================
// VIDEOS SERVICE OBJECT
// ============================================================================

export const VideosService = {
	// High-level functions
	getRecentVideos,
	getVideoById,
	getVideosByEconomist,
	getVideosWithPagination,
	getVideosForSearch,
	getVideosForSitemap,
	getVideosForRSS,

	// Query functions (re-exported for convenience)
	findRecentVideosWithEconomist,
	findVideosByEconomist,
	findVideoById,
	findPredictionsForVideos,
	findVideosWithPredictionCounts,
	countVideos,

	// Formatter functions (re-exported for convenience)
	formatVideo,
	formatVideoWithPredictions,
	formatVideoForHomepage,
	formatVideoListItem,
	formatVideoForSearch,
	formatVideoForSitemap,
	formatVideoForRSS,
};

export default VideosService;

// Re-export types
export type { FormattedVideo, VideoForHomepage, VideoWithPredictions, VideoForBuild };
