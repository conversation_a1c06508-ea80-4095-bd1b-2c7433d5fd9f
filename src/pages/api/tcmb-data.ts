import type { APIRoute } from 'astro';
import { fetchTcmbData } from '@/server/services/verification/tcmb';

// Simple in-memory cache for TCMB data (consider Redis for production)
interface CacheEntry {
  data: unknown;
  timestamp: number;
}

const cache = new Map<string, CacheEntry>();
const CACHE_DURATION = 4 * 60 * 60 * 1000; // 4 hours in milliseconds

export const GET: APIRoute = async ({ url }) => {
  try {
    const apiKey = import.meta.env.TCMB_API_KEY;
    if (!apiKey) {
      return new Response(JSON.stringify({
        status: 'error',
        message: 'TCMB API key not configured'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get query parameters
    const searchParams = url.searchParams;
    const series = searchParams.get('series') || 'TP.DK.USD.A';
    const days = parseInt(searchParams.get('days') || '30');

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    // Format dates as DD-MM-YYYY
    const formatTcmbDate = (date: Date): string => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    };

    const startDateStr = formatTcmbDate(startDate);
    const endDateStr = formatTcmbDate(endDate);

    // Create cache key
    const cacheKey = `${series}_${startDateStr}_${endDateStr}`;

    // Check cache first
    const cached = cache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
      return new Response(JSON.stringify({
        status: 'success',
        data: cached.data,
        cached: true,
        lastUpdated: new Date(cached.timestamp).toISOString()
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch fresh data
    const rawData = await fetchTcmbData({
      series,
      startDate: startDateStr,
      endDate: endDateStr
    }, apiKey);

    // Process and filter data
    const processedData = rawData
      .filter(item => item[series.replace(/\./g, '_')] !== null)
      .map(item => {
        const valueStr = item[series.replace(/\./g, '_')];
        return {
          date: item.Tarih,
          value: parseFloat(typeof valueStr === 'string' ? valueStr : String(valueStr)),
          timestamp: item.UNIXTIME?.$numberLong ? parseInt(item.UNIXTIME.$numberLong) : null
        };
      })
      .sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));

    // Calculate some basic statistics
    const values = processedData.map(item => item.value);
    const latest = processedData[processedData.length - 1];
    const previous = processedData[processedData.length - 2];
    const change = latest && previous ? latest.value - previous.value : 0;
    const changePercent = latest && previous ? ((change / previous.value) * 100) : 0;

    const result = {
      series,
      dateRange: { start: startDateStr, end: endDateStr },
      data: processedData,
      statistics: {
        count: processedData.length,
        latest: latest?.value || null,
        change: change,
        changePercent: changePercent,
        min: Math.min(...values),
        max: Math.max(...values),
        average: values.reduce((a, b) => a + b, 0) / values.length
      }
    };

    // Cache the result
    cache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });

    return new Response(JSON.stringify({
      status: 'success',
      data: result,
      cached: false,
      lastUpdated: new Date().toISOString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('TCMB API Error:', error);
    return new Response(JSON.stringify({
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
