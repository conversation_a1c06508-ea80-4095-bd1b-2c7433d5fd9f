import { Hono } from 'hono';
import {
  requireAuth,
  requireAdmin,
  requireModerator,
  requireEditor,
  requirePaidUser
} from '@/server/auth/middleware';
import type { AuthContext } from '@/server/auth';
import health from './health';
import articles from './articles';
import auth from '../auth/routes';

// Import all the migrated API routers
import economists from './economists/economists';
import predictions from './predictions/predictions';
import predictionVerificationQueue from './predictions/verification-queue';
import predictionVerifyBulk from './predictions/verify-bulk';
import predictionVerifyAutomated from './predictions/verify-automated';
import predictionScheduler from './predictions/scheduler';
import predictionVerify from './predictions/verify';
import statsPlatform from './stats/platform';
import videos from './videos';

const app = new Hono<{ Variables: { auth: AuthContext } }>().basePath('/api/v1');

// Mount health check routes
app.route('/health', health);

// Mount articles routes
app.route('/articles', articles);

// Mount authentication routes
app.route('/auth', auth);

// Mount API routes
app.route('/economists', economists);
app.route('/predictions', predictions);
app.route('/predictions/verification-queue', predictionVerificationQueue);
app.route('/predictions/verify-bulk', predictionVerifyBulk);
app.route('/predictions/verify-automated', predictionVerifyAutomated);
app.route('/predictions/scheduler', predictionScheduler);
app.route('/predictions/:id/verify', predictionVerify);
app.route('/stats/platform', statsPlatform);
app.route('/videos', videos);

// Protected routes for authenticated users
app.get('/profile', requireAuth(), (c) => {
	const auth = c.var.auth;
	return c.json({
		success: true,
		data: { user: auth.user, roles: auth.roles },
		message: 'Profile retrieved successfully'
	});
});

// Premium/Paid user routes
app.get('/premium/*', requirePaidUser(), (c) => {
	return c.json({
		success: true,
		message: 'Premium content access granted'
	});
});

// Admin routes
app.get('/admin/*', requireAdmin(), (c) => {
	return c.json({
		success: true,
		message: 'Admin access granted'
	});
});

// Moderator routes
app.get('/moderation/*', requireModerator(), (c) => {
	return c.json({
		success: true,
		message: 'Moderation access granted'
	});
});

// Editor routes
app.get('/editor/*', requireEditor(), (c) => {
	return c.json({
		success: true,
		message: 'Editor access granted'
	});
});

export default app;
