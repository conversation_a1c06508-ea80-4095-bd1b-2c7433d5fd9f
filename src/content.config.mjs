// Content configuration for Astro
// This file defines content collections for cached database data

import { defineCollection, z } from 'astro:content';

// Schema for homepage data cache
const homepageCollection = defineCollection({
  type: 'data',
  schema: z.object({
    $schema: z.string(),
    data: z.object({
      topEconomists: z.array(z.object({
        id: z.number(),
        name: z.string(),
        youtubeChannelName: z.string(),
        totalPredictions: z.number(),
        accuracyScore: z.number(),
      })),
      platformStats: z.object({
        economistsCount: z.number(),
        predictionsCount: z.number(),
        videosCount: z.number(),
        accuracyPercentage: z.number(),
      }),
      recentVideos: z.array(z.unknown()).optional(),
      isStatic: z.boolean(),
    }),
    fetchedAt: z.string(),
    environment: z.string(),
    optimized: z.boolean().optional(),
  }),
});

// Schema for predictions data cache
const predictionsCollection = defineCollection({
  type: 'data',
  schema: z.object({
    $schema: z.string(),
    data: z.object({
      predictions: z.array(z.object({
        id: z.number(),
        title: z.string(),
        description: z.string(),
        category: z.string(),
        predictionType: z.string(),
        targetValue: z.string().nullable(),
        targetDate: z.string().nullable(),
        confidenceLevel: z.number().nullable(),
        status: z.string(),
        accuracyScore: z.number().nullable(),
        economistId: z.number(),
        videoId: z.number().nullable(),
        economistName: z.string().nullable(),
        createdAt: z.string(),
        updatedAt: z.string(),
        isActive: z.boolean(),
      })),
      totalCount: z.number(),
    }),
    fetchedAt: z.string(),
    environment: z.string(),
  }),
});

// Schema for videos data cache
const videosCollection = defineCollection({
  type: 'data',
  schema: z.object({
    $schema: z.string(),
    data: z.object({
      videos: z.array(z.object({
        id: z.number(),
        title: z.string(),
        youtubeVideoId: z.string(),
        thumbnailUrl: z.string().nullable(),
        duration: z.number().nullable(),
        viewCount: z.number(),
        publishedAt: z.string(),
        economistId: z.number(),
        economistName: z.string().nullable(),
        economistAvatarUrl: z.string().nullable(),
        isActive: z.boolean(),
        createdAt: z.string().nullable(),
        updatedAt: z.string().nullable(),
      })),
      totalCount: z.number(),
    }),
    fetchedAt: z.string(),
    environment: z.string(),
  }),
});

// Schema for economists data cache
const economistsCollection = defineCollection({
  type: 'data',
  schema: z.object({
    $schema: z.string(),
    data: z.object({
      economists: z.array(z.object({
        id: z.number(),
        name: z.string(),
        youtubeChannelName: z.string(),
        totalPredictions: z.number(),
        correctPredictions: z.number(),
        accuracyScore: z.number(),
        trustScore: z.number(),
        avatarUrl: z.string().nullable(),
        bio: z.string(),
        subscriberCount: z.number(),
      })),
      totalCount: z.number(),
    }),
    fetchedAt: z.string(),
    environment: z.string(),
    optimized: z.boolean().optional(),
  }),
});

// Schema for articles data cache (for SSG build data)
const articlesCollection = defineCollection({
  type: 'data',
  schema: z.object({
    $schema: z.string(),
    data: z.object({
      articles: z.array(z.object({
        id: z.number(),
        title: z.string(),
        excerpt: z.string(),
        content: z.string().nullable(),
        author: z.string(),
        publishedAt: z.string(), // ISO string from build data
        imageUrl: z.string().nullable(),
        category: z.string(),
        readTime: z.string(),
        slug: z.string(),
        metaDescription: z.string().nullable(),
        tags: z.array(z.string()),
        isActive: z.boolean(),
        isCurated: z.boolean(),
        createdAt: z.string(), // ISO string from build data
        updatedAt: z.string(), // ISO string from build data
      })),
      totalCount: z.number(),
    }),
    fetchedAt: z.string(),
    environment: z.string(),
    optimized: z.boolean().optional(),
  }),
});

export const collections = {
  homepage: homepageCollection,
  predictions: predictionsCollection,
  videos: videosCollection,
  economists: economistsCollection,
  articles: articlesCollection,
};
