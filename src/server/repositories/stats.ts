/**
 * Statistics Repository
 *
 * Data access layer for platform statistics and aggregations.
 * Contains both Drizzle ORM queries and raw SQL for stats.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import type { DbConnection } from '@/server/db';
import { economists, predictions, videos } from '@/server/db';
import { eq, count, avg, inArray } from 'drizzle-orm';
import { execSync } from 'child_process';

// ============================================================================
// RAW SQL QUERIES (for build scripts and external tools)
// ============================================================================

export const STATS_SQL = {
	platformStats: `
    SELECT
      (SELECT COUNT(*) FROM economists WHERE is_active = 1) as economistsCount,
      (SELECT COUNT(*) FROM predictions) as predictionsCount,
      (SELECT COUNT(*) FROM videos) as videosCount,
      (SELECT ROUND(AVG(accuracy_score) * 100) FROM economists WHERE accuracy_score IS NOT NULL) as accuracyPercentage
  `
		.replace(/\s+/g, ' ')
		.trim(),
} as const;

// ============================================================================
// DRIZZLE ORM QUERIES
// ============================================================================

/**
 * Get comprehensive platform statistics
 */
export async function getPlatformStatistics(db: DbConnection) {
	// Execute all stat queries in parallel for better performance
	const [economistsResult, predictionsResult, videosResult, verifiedResult, accuracyResult] =
		await Promise.all([
			db.select({ count: count() }).from(economists).where(eq(economists.isActive, true)),
			db.select({ count: count() }).from(predictions),
			db.select({ count: count() }).from(videos),
			db
				.select({ count: count() })
				.from(predictions)
				.where(inArray(predictions.status, ['verified_correct', 'verified_incorrect'])),
			db
				.select({ value: avg(predictions.accuracyScore) })
				.from(predictions)
				.where(inArray(predictions.status, ['verified_correct', 'verified_incorrect'])),
		]);

	return {
		economistsCount: economistsResult[0]?.count || 0,
		predictionsCount: predictionsResult[0]?.count || 0,
		videosCount: videosResult[0]?.count || 0,
		verifiedCount: verifiedResult[0]?.count || 0,
		accuracyPercentage: Math.round(parseFloat(accuracyResult[0]?.value || '0') * 100),
	};
}

/**
 * Get economist-specific statistics
 */
export async function getEconomistStatistics(db: DbConnection, economistId: number) {
	const stats = await db
		.select({
			total: count(),
			verified: count(predictions.verifiedAt),
			correct: count(eq(predictions.status, 'verified_correct')),
		})
		.from(predictions)
		.where(eq(predictions.economistId, economistId));

	return stats[0] || { total: 0, verified: 0, correct: 0 };
}

/**
 * Get category-wise prediction statistics
 */
export async function getCategoryStatistics(db: DbConnection) {
	const categoryStats = await db
		.select({
			category: predictions.category,
			total: count(),
			verified: count(predictions.verifiedAt),
			correct: count(eq(predictions.status, 'verified_correct')),
		})
		.from(predictions)
		.groupBy(predictions.category);

	return categoryStats.map((stat) => ({
		...stat,
		accuracyRate: stat.verified > 0 ? Math.round((stat.correct / stat.verified) * 100) : 0,
	}));
}

// ============================================================================
// RAW SQL EXECUTION (for build scripts)
// ============================================================================

/**
 * Execute raw SQL query for statistics (used by build scripts)
 */
export function executeStatsSQL(query: string, dbName: string, isLocal = false): unknown {
	const localFlag = isLocal ? '--local' : '--remote';
	const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

	try {
		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Stats SQL execution failed:', error);
		return [];
	}
}

/**
 * Get platform statistics using raw SQL (for build-time)
 */
export function getPlatformStatsSQL(dbName: string, isLocal = false) {
	return executeStatsSQL(STATS_SQL.platformStats, dbName, isLocal);
}

// ============================================================================
// DATA FORMATTERS
// ============================================================================

/**
 * Format platform statistics for API responses
 */
export function formatPlatformStats(stats: any) {
	return {
		economistsCount: stats.economistsCount || 0,
		predictionsCount: stats.predictionsCount || 0,
		videosCount: stats.videosCount || 0,
		verifiedCount: stats.verifiedCount || 0,
		accuracyPercentage: stats.accuracyPercentage || 75, // Default fallback
		lastUpdated: new Date().toISOString(),
	};
}

/**
 * Format economist statistics
 */
export function formatEconomistStats(stats: any) {
	const { total, verified, correct } = stats;
	return {
		totalPredictions: total,
		verifiedPredictions: verified,
		correctPredictions: correct,
		accuracyRate: verified > 0 ? Math.round((correct / verified) * 100) : 0,
		pendingPredictions: total - verified,
	};
}

/**
 * Format category statistics
 */
export function formatCategoryStats(categoryStats: any[]) {
	return categoryStats.map((stat) => ({
		category: stat.category,
		totalPredictions: stat.total,
		verifiedPredictions: stat.verified,
		correctPredictions: stat.correct,
		accuracyRate: stat.accuracyRate,
		pendingPredictions: stat.total - stat.verified,
	}));
}

// ============================================================================
// CALCULATION HELPERS
// ============================================================================

/**
 * Calculate accuracy score for a prediction
 */
export function calculateAccuracyScore(
	prediction: any,
	actualValue: number,
	status: string
): number {
	if (status !== 'verified_correct' && status !== 'verified_incorrect') {
		return 0;
	}

	if (status === 'verified_incorrect') {
		return 0;
	}

	// For correct predictions, calculate based on how close the prediction was
	try {
		const targetValue = JSON.parse(prediction.targetValue);
		if (typeof targetValue === 'number') {
			const difference = Math.abs(targetValue - actualValue);
			const relativeDifference = difference / Math.abs(actualValue);

			// Score from 0.5 to 1.0 based on accuracy
			return Math.max(0.5, 1.0 - relativeDifference);
		}
	} catch {
		// If we can't parse the target value, just return 1.0 for correct predictions
		return 1.0;
	}

	return 1.0;
}

/**
 * Calculate trust score for an economist
 */
export function calculateTrustScore(economistStats: any): number {
	const { total, verified, correct } = economistStats;

	if (total === 0) return 0;

	// Base score on verification rate and accuracy
	const verificationRate = verified / total;
	const accuracyRate = verified > 0 ? correct / verified : 0;

	// Weight verification rate and accuracy equally
	return Math.round((verificationRate * 0.5 + accuracyRate * 0.5) * 100) / 100;
}

// ============================================================================
// BUILD-TIME FUNCTIONS
// ============================================================================

/**
 * Get platform stats for build scripts
 */
export function getPlatformStatsBuildData(dbName: string, isLocal = false) {
	const localFlag = isLocal ? '--local' : '--remote';
	const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${STATS_SQL.platformStats}"`;

	try {
		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		const result = parsed[0].results[0];

		return {
			economistsCount: result.economistsCount || 0,
			predictionsCount: result.predictionsCount || 0,
			videosCount: result.videosCount || 0,
			accuracyPercentage: result.accuracyPercentage || 0,
		};
	} catch (error) {
		console.error('Stats SQL execution failed:', error);
		return {
			economistsCount: 0,
			predictionsCount: 0,
			videosCount: 0,
			accuracyPercentage: 0,
		};
	}
}
