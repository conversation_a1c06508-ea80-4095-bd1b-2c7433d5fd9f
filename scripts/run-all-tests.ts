#!/usr/bin/env tsx

/**
 * Comprehensive Test Runner with Server Management
 *
 * This script manages the dev server for all test types that need it:
 * - Unit tests (no server needed)
 * - Integration tests (need server)
 * - E2E tests (need server)
 */

import {
  ensureDevServer,
  cleanupIfStartedByScript,
  runCommand,
  execAsync
} from './test-utils';

async function runAllTests(): Promise<number> { // Return exit code
  const PORT = 4321;
  let overallSuccess = true;

  try {
    // Step 1: Run unit tests first (no server needed)
    console.log('🧪 Running unit tests...');
    const unitTestResult = await runCommand('npm', ['run', 'test:unit']);
    if (unitTestResult !== 0) {
      overallSuccess = false;
      console.log('❌ Unit tests failed, but continuing with other tests...');
    }

    // Step 2: Build project
    console.log('📦 Building project...');
    await execAsync('npm run build');
    console.log('✅ Build completed');

    // Step 3: Ensure dev server is running (use existing or start new)
    console.log('🚀 Ensuring dev server is available for integration and e2e tests...');
    await ensureDevServer(PORT);

    // Step 4: Setup test users
    console.log('👥 Setting up test users...');
    await execAsync('npm run db:test-users:local');
    console.log('✅ Test users created');

    // Step 5: Run integration tests
    console.log('🔧 Running integration tests...');
    const integrationTestResult = await runCommand('npm', ['run', 'test:integration:raw']);
    if (integrationTestResult !== 0) {
      overallSuccess = false;
      console.log('❌ Integration tests failed, but continuing with e2e tests...');
    }

    // Step 6: Run e2e tests
    console.log('🎭 Running e2e tests...');
    const e2eTestResult = await runCommand('npx', ['playwright', 'test']);
    if (e2eTestResult !== 0) {
      overallSuccess = false;
    }

    // Final result
    if (overallSuccess) {
      console.log('🎉 All tests passed successfully!');
    } else {
      console.log('❌ Some tests failed');
    }

    return overallSuccess ? 0 : 1;
  } catch (error) {
    console.error('❌ Test runner failed:', error);
    return 1;
  } finally {
    // Only cleanup if we started the server
    cleanupIfStartedByScript();
  }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, cleaning up and exiting...');
  cleanupIfStartedByScript();
  process.exit(1);
});

runAllTests()
  .then(exitCode => {
    process.exit(exitCode);
  })
  .catch((error: unknown) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
