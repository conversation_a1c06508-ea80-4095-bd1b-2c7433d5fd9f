import { SignJWT, jwtVerify } from 'jose';
import { users } from '@/server/db/tables/users';
import { eq } from 'drizzle-orm';
import type { User } from '@/types';
import { TextEncoder } from 'util';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-change-in-production'
);

export type UserRole =
  | 'ROLE_ANONYMOUS'
  | 'ROLE_GUEST'
  | 'ROLE_FREE_USER'
  | 'ROLE_PAID_USER'
  | 'ROLE_CONTRIBUTOR'
  | 'ROLE_MODERATOR'
  | 'ROLE_EDITOR'
  | 'ROLE_ANALYST'
  | 'ROLE_ADMIN';

export interface JWTPayload {
  userId: number;
  email: string;
  username?: string;
  roles: UserRole[];
  iat: number;
  exp: number;
}

export interface AuthContext {
  user: User | null;
  roles: UserRole[];
  isAuthenticated: boolean;
}

/**
 * Generate JWT token for authenticated user
 */
export async function generateToken(user: User): Promise<string> {
  const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
    userId: user.id,
    email: user.email,
    username: user.username ?? undefined,
    roles: mapDatabaseRoleToJWTRoles(user.role),
  };

  const token = await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(JWT_SECRET);

  return token;
}

/**
 * Verify and decode JWT token
 */
export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload as unknown as JWTPayload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

/**
 * Extract token from Authorization header or cookies
 */
export function extractToken(request: Request): string | null {
  // Try Authorization header first
  const authHeader = request.headers.get('Authorization');
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try HTTPOnly cookie
  const cookieHeader = request.headers.get('Cookie');
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    return cookies['auth-token'] || null;
  }

  return null;
}

/**
 * Get user authentication context from request
 */
export async function getAuthContext(request: Request, db: ReturnType<typeof import('drizzle-orm/d1').drizzle>): Promise<AuthContext> {
  const token = extractToken(request);

  if (!token) {
    return {
      user: null,
      roles: ['ROLE_ANONYMOUS'],
      isAuthenticated: false,
    };
  }

  const payload = await verifyToken(token);
  if (!payload) {
    return {
      user: null,
      roles: ['ROLE_ANONYMOUS'],
      isAuthenticated: false,
    };
  }

  try {
    // Fetch fresh user data only if db is available (runtime context)
    const user = await db.select().from(users).where(eq(users.id, payload.userId)).limit(1);

    if (!user.length || !user[0].isActive) {
      return {
        user: null,
        roles: ['ROLE_ANONYMOUS'],
        isAuthenticated: false,
      };
    }

    return {
      user: user[0] as User,
      roles: payload.roles,
      isAuthenticated: true,
    };
  } catch (error) {
    // If database access fails (e.g., during build time), return anonymous
    console.warn('Database access failed in auth context:', error);
    return {
      user: null,
      roles: ['ROLE_ANONYMOUS'],
      isAuthenticated: false,
    };
  }
}

/**
 * Map database role to JWT roles array
 */
function mapDatabaseRoleToJWTRoles(dbRole: string): UserRole[] {
  const baseRoles: UserRole[] = ['ROLE_FREE_USER']; // All authenticated users get this

  switch (dbRole) {
    case 'admin':
      return ['ROLE_ADMIN', 'ROLE_MODERATOR', 'ROLE_EDITOR', 'ROLE_ANALYST', ...baseRoles];
    case 'moderator':
      return ['ROLE_MODERATOR', 'ROLE_EDITOR', ...baseRoles];
    case 'editor':
      return ['ROLE_EDITOR', ...baseRoles];
    case 'analyst':
      return ['ROLE_ANALYST', ...baseRoles];
    case 'contributor':
      return ['ROLE_CONTRIBUTOR', ...baseRoles];
    case 'user':
    default:
      return baseRoles;
  }
}

/**
 * Check if user has required role
 */
export function hasRole(userRoles: UserRole[], requiredRole: UserRole): boolean {
  return userRoles.includes(requiredRole);
}

/**
 * Check if user has any of the required roles
 */
export function hasAnyRole(userRoles: UserRole[], requiredRoles: UserRole[]): boolean {
  return requiredRoles.some(role => userRoles.includes(role));
}

/**
 * Role hierarchy check - higher roles include permissions of lower roles
 */
export function hasRoleOrHigher(userRoles: UserRole[], requiredRole: UserRole): boolean {
  const roleHierarchy: Record<UserRole, number> = {
    'ROLE_ANONYMOUS': 0,
    'ROLE_GUEST': 1,
    'ROLE_FREE_USER': 2,
    'ROLE_PAID_USER': 3,
    'ROLE_CONTRIBUTOR': 4,
    'ROLE_ANALYST': 5,
    'ROLE_EDITOR': 6,
    'ROLE_MODERATOR': 7,
    'ROLE_ADMIN': 8,
  };

  const userMaxLevel = Math.max(...userRoles.map(role => roleHierarchy[role] || 0));
  const requiredLevel = roleHierarchy[requiredRole] || 0;

  return userMaxLevel >= requiredLevel;
}
