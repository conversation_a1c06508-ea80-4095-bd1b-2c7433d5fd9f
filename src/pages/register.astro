---
export const prerender = false;

import Layout from '../layouts/Layout.astro';

// Redirect if already authenticated
const authContext = ((Astro.locals as any).auth) || { user: null, isAuthenticated: false };
if (authContext.isAuthenticated && authContext.user) {
  return Astro.redirect('/dashboard');
}

const title = '<PERSON>ıt Ol - Türk Ekonomistler Doğruluk Takipçisi';
---

<Layout title={title}>
  <main class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8 transition-colors duration-200">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="flex justify-center">
        <div class="w-12 h-12 bg-amber-600 dark:bg-amber-500 rounded-lg flex items-center justify-center">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 616 6H2a6 6 0 616-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
          </svg>
        </div>
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
        Hesap oluşturun
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
        Zaten hesabınız var mı?
        <a href="/login" class="font-medium text-amber-600 dark:text-amber-400 hover:text-amber-500 dark:hover:text-amber-300 transition-colors">
          Giriş yapın
        </a>
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10 border border-gray-200 dark:border-gray-700 transition-colors duration-200">
        <form id="register-form" class="space-y-6">
          <div id="error-message" class="hidden bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"></span>
          </div>

          <div>
            <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Kullanıcı adı
            </label>
            <div class="mt-1">
              <input
                id="username"
                name="username"
                type="text"
                autocomplete="username"
                required
                class="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm transition-colors duration-200"
                placeholder="kullaniciadi"
              />
            </div>
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              3-30 karakter, harf, rakam ve alt çizgi kullanabilirsiniz.
            </p>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              E-posta adresi
            </label>
            <div class="mt-1">
              <input
                id="email"
                name="email"
                type="email"
                autocomplete="email"
                required
                class="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm transition-colors duration-200"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Şifre
            </label>
            <div class="mt-1">
              <input
                id="password"
                name="password"
                type="password"
                autocomplete="new-password"
                required
                class="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm transition-colors duration-200"
                placeholder="••••••••"
              />
            </div>
            <div class="mt-1">
              <div class="flex text-xs text-gray-500 dark:text-gray-400 space-x-2">
                <span id="length-check" class="flex items-center">
                  <svg class="w-3 h-3 mr-1 text-gray-400 dark:text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                  En az 8 karakter
                </span>
              </div>
            </div>
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Şifre onayı
            </label>
            <div class="mt-1">
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                autocomplete="new-password"
                required
                class="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm transition-colors duration-200"
                placeholder="••••••••"
              />
            </div>
          </div>

          <div class="flex items-center">
            <input
              id="terms"
              name="terms"
              type="checkbox"
              required
              class="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
            />
            <label for="terms" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
              <a href="/terms" class="text-amber-600 dark:text-amber-400 hover:text-amber-500 dark:hover:text-amber-300 transition-colors">Kullanım koşullarını</a> ve
              <a href="/privacy" class="text-amber-600 dark:text-amber-400 hover:text-amber-500 dark:hover:text-amber-300 transition-colors">gizlilik politikasını</a> kabul ediyorum.
            </label>
          </div>

          <div>
            <button
              type="submit"
              id="submit-btn"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 dark:bg-amber-500 hover:bg-amber-700 dark:hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <span id="submit-text">Hesap Oluştur</span>
              <svg id="loading-spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
          </div>
        </form>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300 dark:border-gray-600" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">veya</span>
            </div>
          </div>

          <div class="mt-6">
            <a
              href="/"
              class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-500 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
            >
              Ana sayfaya dön
            </a>
          </div>
        </div>
      </div>
    </div>
  </main>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('register-form') as HTMLFormElement;
      const errorMessage = document.getElementById('error-message');
      const submitBtn = document.getElementById('submit-btn') as HTMLButtonElement;
      const submitText = document.getElementById('submit-text');
      const loadingSpinner = document.getElementById('loading-spinner');
      const passwordInput = document.getElementById('password') as HTMLInputElement;
      const confirmPasswordInput = document.getElementById('confirmPassword') as HTMLInputElement;
      const lengthCheck = document.getElementById('length-check');

      function showError(message: string) {
        if (errorMessage) {
          errorMessage.querySelector('span')!.textContent = message;
          errorMessage.classList.remove('hidden');
        }
      }

      function hideError() {
        if (errorMessage) {
          errorMessage.classList.add('hidden');
        }
      }

      function setLoading(loading: boolean) {
        if (submitBtn && submitText && loadingSpinner) {
          submitBtn.disabled = loading;
          if (loading) {
            submitText.textContent = 'Hesap oluşturuluyor...';
            loadingSpinner.classList.remove('hidden');
          } else {
            submitText.textContent = 'Hesap Oluştur';
            loadingSpinner.classList.add('hidden');
          }
        }
      }

      function updatePasswordValidation() {
        const password = passwordInput.value;
        const isLengthValid = password.length >= 8;

        if (lengthCheck) {
          const icon = lengthCheck.querySelector('svg');
          if (isLengthValid) {
            lengthCheck.classList.add('text-green-600');
            lengthCheck.classList.remove('text-gray-500');
            if (icon) {
              icon.classList.add('text-green-600');
              icon.classList.remove('text-gray-400');
            }
          } else {
            lengthCheck.classList.add('text-gray-500');
            lengthCheck.classList.remove('text-green-600');
            if (icon) {
              icon.classList.add('text-gray-400');
              icon.classList.remove('text-green-600');
            }
          }
        }
      }

      function validatePasswords() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;

        if (confirmPassword && password !== confirmPassword) {
          confirmPasswordInput.setCustomValidity('Şifreler eşleşmiyor');
        } else {
          confirmPasswordInput.setCustomValidity('');
        }
      }

      passwordInput.addEventListener('input', updatePasswordValidation);
      passwordInput.addEventListener('input', validatePasswords);
      confirmPasswordInput.addEventListener('input', validatePasswords);

      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        hideError();

        const formData = new FormData(form);
        const data = {
          username: formData.get('username') as string,
          email: formData.get('email') as string,
          password: formData.get('password') as string,
          confirmPassword: formData.get('confirmPassword') as string,
        };

        // Client-side validation
        if (data.password !== data.confirmPassword) {
          showError('Şifreler eşleşmiyor.');
          return;
        }

        if (data.password.length < 8) {
          showError('Şifre en az 8 karakter olmalıdır.');
          return;
        }

        setLoading(true);

        try {
          const response = await fetch('/api/v1/auth/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          });

          const result = await response.json() as { status: string; message?: string };

          if (response.ok && result.status === 'success') {
            // Redirect to dashboard or show success message
            window.location.href = '/dashboard';
          } else {
            showError(result.message || 'Kayıt olurken bir hata oluştu.');
          }
        } catch (error) {
          console.error('Register error:', error);
          showError('Bağlantı hatası. Lütfen tekrar deneyin.');
        } finally {
          setLoading(false);
        }
      });
    });
  </script>
</Layout>
