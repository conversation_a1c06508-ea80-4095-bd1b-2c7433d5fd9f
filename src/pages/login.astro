---
import Layout from '../layouts/Layout.astro';

// Disable prerendering to allow server-side authentication check
export const prerender = false;

// Redirect if already authenticated
const authContext = ((Astro.locals as any).auth) || { user: null, isAuthenticated: false };
if (authContext.isAuthenticated && authContext.user) {
  return Astro.redirect('/dashboard');
}

const title = '<PERSON><PERSON>ş Yap - Türk Ekonomistler Doğruluk Takipçisi';
---

<Layout title={title}>
  <main class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8 transition-colors duration-200">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="flex justify-center">
        <div class="w-12 h-12 bg-amber-600 dark:bg-amber-500 rounded-lg flex items-center justify-center">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
        Hesabınıza giriş yapın
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-300">
        Hesabınız yok mu?
        <a href="/register" class="font-medium text-amber-600 dark:text-amber-400 hover:text-amber-500 dark:hover:text-amber-300 transition-colors">
          Ücretsiz hesap oluşturun
        </a>
      </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10 border border-gray-200 dark:border-gray-700 transition-colors duration-200">
        <form id="login-form" class="space-y-6">
          <div id="error-message" class="hidden bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"></span>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              E-posta adresi
            </label>
            <div class="mt-1">
              <input
                id="email"
                name="email"
                type="email"
                autocomplete="email"
                required
                class="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm transition-colors duration-200"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Şifre
            </label>
            <div class="mt-1">
              <input
                id="password"
                name="password"
                type="password"
                autocomplete="current-password"
                required
                class="appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-amber-500 focus:border-amber-500 sm:text-sm transition-colors duration-200"
                placeholder="••••••••"
              />
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                class="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                Beni hatırla
              </label>
            </div>

            <div class="text-sm">
              <a href="/forgot-password" class="font-medium text-amber-600 dark:text-amber-400 hover:text-amber-500 dark:hover:text-amber-300 transition-colors">
                Şifrenizi mi unuttunuz?
              </a>
            </div>
          </div>

          <div>
            <button
              type="submit"
              id="submit-btn"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 dark:bg-amber-500 hover:bg-amber-700 dark:hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <span id="submit-text">Giriş Yap</span>
              <svg id="loading-spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
          </div>
        </form>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300 dark:border-gray-600" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">veya</span>
            </div>
          </div>

          <div class="mt-6">
            <a
              href="/"
              class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-500 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
            >
              Ana sayfaya dön
            </a>
          </div>
        </div>
      </div>
    </div>
  </main>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('login-form') as HTMLFormElement;
      const errorMessage = document.getElementById('error-message');
      const submitBtn = document.getElementById('submit-btn') as HTMLButtonElement;
      const submitText = document.getElementById('submit-text');
      const loadingSpinner = document.getElementById('loading-spinner');

      function showError(message: string) {
        if (errorMessage) {
          errorMessage.querySelector('span')!.textContent = message;
          errorMessage.classList.remove('hidden');
        }
      }

      function hideError() {
        if (errorMessage) {
          errorMessage.classList.add('hidden');
        }
      }

      function setLoading(loading: boolean) {
        if (submitBtn && submitText && loadingSpinner) {
          submitBtn.disabled = loading;
          if (loading) {
            submitText.textContent = 'Giriş yapılıyor...';
            loadingSpinner.classList.remove('hidden');
          } else {
            submitText.textContent = 'Giriş Yap';
            loadingSpinner.classList.add('hidden');
          }
        }
      }

      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        hideError();
        setLoading(true);

        const formData = new FormData(form);
        const data = {
          email: formData.get('email') as string,
          password: formData.get('password') as string,
        };

        try {
          const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          });

          const result = await response.json() as { status: string; message?: string };

          if (response.ok && result.status === 'success') {
            // Redirect to dashboard or intended page
            const urlParams = new URLSearchParams(window.location.search);
            const redirect = urlParams.get('redirect') || '/dashboard';
            window.location.href = redirect;
          } else {
            showError(result.message || 'Giriş yapılırken bir hata oluştu.');
          }
        } catch (error) {
          console.error('Login error:', error);
          showError('Bağlantı hatası. Lütfen tekrar deneyin.');
        } finally {
          setLoading(false);
        }
      });
    });
  </script>
</Layout>
