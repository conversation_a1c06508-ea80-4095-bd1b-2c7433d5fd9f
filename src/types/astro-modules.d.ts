/**
 * TypeScript declarations for Astro module resolution
 * This helps VS Code understand path aliases and Astro-specific imports
 */

// Astro component declarations
declare module '*.astro' {
	const Component: any;
	export default Component;
}

// Path alias declarations for better IDE support
declare module '@/*' {
	const content: any;
	export default content;
}

declare module '@/components/*' {
	const Component: any;
	export default Component;
}

declare module '@/layouts/*' {
	const Layout: any;
	export default Layout;
}

declare module '@/features/*' {
	const Feature: any;
	export default Feature;
}

declare module '@/server/*' {
	const ServerModule: any;
	export default ServerModule;
}

declare module '@/services/*' {
	const Service: any;
	export default Service;
}

declare module '@/stores/*' {
	const Store: any;
	export default Store;
}

declare module '@/utils/*' {
	const Util: any;
	export default Util;
}

// Astro-specific module declarations
declare module 'astro:content' {
	export function getCollection(collection: string): Promise<any[]>;
	export function getEntry(collection: string, id: string): Promise<any>;
	export function getEntries(entries: Array<{ collection: string; id: string }>): Promise<any[]>;
	export function getDataEntryById(collection: string, id: string): Promise<any>;
	export function getEntryBySlug(collection: string, slug: string): Promise<any>;
	export function render(entry: any): Promise<{ Content: any; headings: any[]; remarkPluginFrontmatter: any }>;
	
	// Collection types
	export interface CollectionEntry<T = any> {
		id: string;
		slug: string;
		body: string;
		collection: string;
		data: T;
		render(): Promise<{ Content: any; headings: any[]; remarkPluginFrontmatter: any }>;
	}
}

declare module 'astro:assets' {
	export function getImage(options: any): Promise<any>;
	export const Image: any;
}

// Additional Astro modules
declare module 'astro/client' {
	// Astro client types
}

declare module 'astro/config' {
	export function defineConfig(config: any): any;
}

// Vite-specific declarations for better compatibility
declare module 'virtual:*' {
	const content: any;
	export default content;
}

// Environment variable declarations
declare module 'process' {
	global {
		namespace NodeJS {
			interface ProcessEnv {
				NODE_ENV: 'development' | 'production' | 'test';
				DATABASE_URL?: string;
				CLOUDFLARE_D1_DATABASE_ID?: string;
				JWT_SECRET?: string;
				JWT_EXPIRES_IN?: string;
				LLM_PROVIDER?: 'openai' | 'anthropic' | 'cloudflare';
				OPENAI_API_KEY?: string;
				ANTHROPIC_API_KEY?: string;
				CLOUDFLARE_ACCOUNT_ID?: string;
				CLOUDFLARE_API_TOKEN?: string;
				VECTORIZE_INDEX_NAME?: string;
				LOGFLARE_API_KEY?: string;
				LOGFLARE_SOURCE_TOKEN?: string;
				API_BASE_URL?: string;
				CORS_ORIGINS?: string;
				RATE_LIMIT_WINDOW_MS?: string;
				RATE_LIMIT_MAX_REQUESTS?: string;
			}
		}
	}
}
