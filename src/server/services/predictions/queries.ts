/**
 * Predictions Queries
 *
 * SQL queries and database operations for predictions.
 * Contains both raw SQL for build scripts and Drizzle ORM queries.
 */

import type { DbConnection } from '@/server/db';
import { predictions, economists, videos } from '@/server/db';
import { eq, desc, and, inArray } from 'drizzle-orm';
import type { QueryOptions } from '../shared/types';
import { execSync } from 'child_process';

// ============================================================================
// RAW SQL QUERIES (for build scripts and external tools)
// ============================================================================

export const PREDICTIONS_SQL = {
	/**
	 * Get all predictions with economist and video info
	 */
	all: (limit?: number) =>
		`
    SELECT
      p.id,
      p.title,
      p.description,
      p.category,
      p.prediction_type as predictionType,
      p.target_value as targetValue,
      p.target_date as targetDate,
      p.confidence_level as confidenceLevel,
      p.status,
      p.accuracy_score as accuracyScore,
      p.verification_method as verificationMethod,
      p.verification_source as verificationSource,
      p.verified_at as verifiedAt,
      p.actual_value as actualValue,
      p.accuracy,
      p.extracted_at as extractedAt,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      e.id as economistId,
      e.name as economistName,
      e.avatar_url as economistAvatarUrl,
      e.youtube_channel_name as economistChannelName,
      v.id as videoId,
      v.title as videoTitle,
      v.youtube_video_id as youtubeVideoId
    FROM predictions p
    INNER JOIN economists e ON p.economist_id = e.id
    LEFT JOIN videos v ON p.video_id = v.id
    WHERE e.is_active = 1
    ORDER BY p.created_at DESC
    ${limit ? `LIMIT ${limit}` : ''}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	/**
	 * Get predictions by status
	 */
	byStatus: (status: string, limit?: number) =>
		`
    SELECT
      p.id,
      p.title,
      p.description,
      p.category,
      p.prediction_type as predictionType,
      p.target_value as targetValue,
      p.target_date as targetDate,
      p.confidence_level as confidenceLevel,
      p.status,
      p.accuracy_score as accuracyScore,
      p.extracted_at as extractedAt,
      e.id as economistId,
      e.name as economistName,
      e.avatar_url as economistAvatarUrl
    FROM predictions p
    INNER JOIN economists e ON p.economist_id = e.id
    WHERE e.is_active = 1 AND p.status = '${status}'
    ORDER BY p.target_date ASC
    ${limit ? `LIMIT ${limit}` : ''}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	/**
	 * Get predictions by economist
	 */
	byEconomist: (economistId: number, limit?: number) =>
		`
    SELECT
      p.id,
      p.title,
      p.description,
      p.category,
      p.prediction_type as predictionType,
      p.target_value as targetValue,
      p.target_date as targetDate,
      p.confidence_level as confidenceLevel,
      p.status,
      p.accuracy_score as accuracyScore,
      p.extracted_at as extractedAt
    FROM predictions p
    WHERE p.economist_id = ${economistId}
    ORDER BY p.created_at DESC
    ${limit ? `LIMIT ${limit}` : ''}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	/**
	 * Get verification queue (pending predictions)
	 */
	verificationQueue: () =>
		`
    SELECT
      p.id,
      p.title,
      p.description,
      p.category,
      p.prediction_type as predictionType,
      p.target_value as targetValue,
      p.target_date as targetDate,
      p.confidence_level as confidenceLevel,
      p.extracted_at as extractedAt,
      e.id as economistId,
      e.name as economistName,
      e.avatar_url as economistAvatarUrl,
      v.title as videoTitle,
      v.youtube_video_id as youtubeVideoId
    FROM predictions p
    INNER JOIN economists e ON p.economist_id = e.id
    LEFT JOIN videos v ON p.video_id = v.id
    WHERE e.is_active = 1 AND p.status = 'pending'
    ORDER BY p.target_date ASC
  `
			.replace(/\s+/g, ' ')
			.trim(),

	/**
	 * Count predictions by status
	 */
	countByStatus: () =>
		`
    SELECT
      status,
      COUNT(*) as count
    FROM predictions p
    INNER JOIN economists e ON p.economist_id = e.id
    WHERE e.is_active = 1
    GROUP BY status
  `
			.replace(/\s+/g, ' ')
			.trim(),
};

// ============================================================================
// DRIZZLE ORM QUERIES
// ============================================================================

/**
 * Find predictions with optional filtering
 */
export async function findPredictions(db: DbConnection, options: QueryOptions = {}) {
	const { limit = 20, status, category, economistId } = options;

	// Build conditions array
	const conditions = [eq(economists.isActive, true)];

	if (status) {
		conditions.push(eq(predictions.status, status as never));
	}

	if (category) {
		conditions.push(eq(predictions.category, category));
	}

	if (economistId) {
		conditions.push(eq(predictions.economistId, economistId));
	}

	return await db
		.select()
		.from(predictions)
		.innerJoin(economists, eq(predictions.economistId, economists.id))
		.leftJoin(videos, eq(predictions.videoId, videos.id))
		.where(and(...conditions))
		.orderBy(desc(predictions.createdAt))
		.limit(limit);
}

/**
 * Find prediction by ID
 */
export async function findPredictionById(db: DbConnection, id: number) {
	const result = await db
		.select()
		.from(predictions)
		.innerJoin(economists, eq(predictions.economistId, economists.id))
		.leftJoin(videos, eq(predictions.videoId, videos.id))
		.where(and(eq(predictions.id, id), eq(economists.isActive, true)))
		.limit(1);

	return result[0] || null;
}

/**
 * Find predictions by economist
 */
export async function findPredictionsByEconomist(
	db: DbConnection,
	economistId: number,
	options: QueryOptions = {}
) {
	const { limit = 20, status } = options;

	// Build conditions array
	const conditions = [eq(predictions.economistId, economistId)];

	if (status) {
		conditions.push(eq(predictions.status, status as never));
	}

	return await db
		.select()
		.from(predictions)
		.where(and(...conditions))
		.orderBy(desc(predictions.createdAt))
		.limit(limit);
}

/**
 * Find verification queue (pending predictions)
 */
export async function findVerificationQueue(db: DbConnection, limit = 50) {
	return await db
		.select()
		.from(predictions)
		.innerJoin(economists, eq(predictions.economistId, economists.id))
		.leftJoin(videos, eq(predictions.videoId, videos.id))
		.where(and(eq(economists.isActive, true), eq(predictions.status, 'pending')))
		.orderBy(predictions.targetDate)
		.limit(limit);
}

/**
 * Find predictions for videos
 */
export async function findPredictionsForVideos(db: DbConnection, videoIds: number[]) {
	if (videoIds.length === 0) return [];

	return await db.select().from(predictions).where(inArray(predictions.videoId, videoIds));
}

/**
 * Count predictions with filters
 */
export async function countPredictions(db: DbConnection, options: QueryOptions = {}) {
	const { status, category, economistId } = options;

	// Build conditions array
	const conditions = [eq(economists.isActive, true)];

	if (status) {
		conditions.push(eq(predictions.status, status as never));
	}

	if (category) {
		conditions.push(eq(predictions.category, category));
	}

	if (economistId) {
		conditions.push(eq(predictions.economistId, economistId));
	}

	const result = await db
		.select({ count: predictions.id })
		.from(predictions)
		.innerJoin(economists, eq(predictions.economistId, economists.id))
		.where(and(...conditions));

	return result.length;
}

// ============================================================================
// SQL EXECUTION HELPER
// ============================================================================

/**
 * Execute raw SQL query for predictions (used by build scripts)
 */
export function executePredictionSQL(query: string, dbName: string, isLocal = false): unknown {
	try {
		const localFlag = isLocal ? '--local' : '--remote';
		const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Prediction SQL execution failed:', error);
		return [];
	}
}
