---
import Layout from '../../layouts/Layout.astro';
import TcmbDashboard from '../../components/vue/TcmbDashboard.vue';
import { count, eq, and, gte, lt, sql } from 'drizzle-orm';
import { predictions, economists } from '../../server/db/schema';
import { DatabaseContext } from '../../server/db/context';

// This page should not be prerendered as it's an admin page requiring dynamic data
export const prerender = false;

// Check if user has admin/moderator access
const auth = (Astro.locals as any).auth;
if (!auth?.isAuthenticated) {
	return Astro.redirect('/login?redirect=' + encodeURIComponent(Astro.url.pathname));
}

// Check if user has required role (admin or moderator)
const hasRequiredRole = auth.roles?.includes('ROLE_ADMIN') || auth.roles?.includes('ROLE_MODERATOR');
if (!hasRequiredRole) {
	return Astro.redirect('/dashboard?error=access_denied');
}

const user = auth.user;
const isAdmin = auth.roles?.includes('ROLE_ADMIN');

// Fetch real statistics from database
let dashboardStats = {
	pendingVerifications: 0,
	todayVerifications: 0,
	weeklyAccuracy: 0,
	activeEconomists: 0,
	totalPredictions: 0,
	recentActivity: 'Sistem normal çalışıyor'
};

try {
	// Set up database connection
	const dbContext = DatabaseContext.getInstance();
	if ((Astro.locals as any).runtime?.env?.DB) {
		dbContext.setRuntimeDb((Astro.locals as any).runtime.env.DB);
	}
	const db = await dbContext.getDb();

	if (db) {
		// Get current date ranges
		const now = new Date();
		const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
		const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

		// Fetch pending verifications (predictions past target date without verification)
		const pendingVerificationsResult = await db
			.select({ count: count() })
			.from(predictions)
			.where(
				and(
					lt(predictions.targetDate, now),
					eq(predictions.status, 'pending')
				)
			);

		// Fetch today's verifications
		const todayVerificationsResult = await db
			.select({ count: count() })
			.from(predictions)
			.where(
				and(
					gte(predictions.verifiedAt, todayStart),
					eq(predictions.status, 'verified_correct')
				)
			);

		// Fetch weekly accuracy
		const weeklyPredictionsResult = await db
			.select({
				total: count(),
				correct: sql<number>`SUM(CASE WHEN status = 'verified_correct' THEN 1 ELSE 0 END)`
			})
			.from(predictions)
			.where(
				and(
					gte(predictions.verifiedAt, weekStart),
					sql`status IN ('verified_correct', 'verified_incorrect')`
				)
			);

		// Fetch active economists count
		const activeEconomistsResult = await db
			.select({ count: count() })
			.from(economists)
			.where(eq(economists.isActive, true));

		// Fetch total predictions count
		const totalPredictionsResult = await db
			.select({ count: count() })
			.from(predictions);

		// Calculate weekly accuracy
		const weeklyStats = weeklyPredictionsResult[0];
		const weeklyAccuracy = weeklyStats?.total > 0
			? Math.round((Number(weeklyStats.correct) / weeklyStats.total) * 100)
			: 0;

		dashboardStats = {
			pendingVerifications: pendingVerificationsResult[0]?.count || 0,
			todayVerifications: todayVerificationsResult[0]?.count || 0,
			weeklyAccuracy: weeklyAccuracy,
			activeEconomists: activeEconomistsResult[0]?.count || 0,
			totalPredictions: totalPredictionsResult[0]?.count || 0,
			recentActivity: 'Sistem normal çalışıyor'
		};
	} else {
		console.log('Database not available, using default stats');
		dashboardStats.recentActivity = 'Veritabanı bağlantısı kurulamadı';
	}
} catch (error) {
	console.error('Error fetching dashboard stats:', error);
	dashboardStats.recentActivity = 'Veri yüklenirken hata oluştu';
}
---

<Layout title="Admin Panel - Yönetici Paneli">
	<main class="min-h-screen bg-gray-50 dark:bg-gray-900">
		<!-- Header -->
		<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between items-center py-4">
					<div class="flex items-center space-x-4">
						<div class="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
							<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
							</svg>
						</div>
						<div>
							<h1 class="text-2xl font-bold text-gray-900 dark:text-white">
								Yönetici Paneli
							</h1>
							<p class="text-sm text-gray-500 dark:text-gray-400">
								{isAdmin ? 'Admin' : 'Moderatör'} • {user.username || user.email}
							</p>
						</div>
					</div>
					<div class="flex items-center space-x-3">
						<a
							href="/dashboard"
							class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-sm font-medium transition-colors"
						>
							← Kullanıcı Paneli
						</a>
						<div class="w-2 h-2 bg-green-500 rounded-full"></div>
						<span class="text-xs text-green-600 dark:text-green-400">Sistem Aktif</span>
					</div>
				</div>
			</div>
		</header>

		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
			<!-- Overview Stats -->
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
				<!-- Pending Verifications -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Bekleyen Doğrulamalar</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{dashboardStats.pendingVerifications}</p>
						</div>
					</div>
				</div>

				<!-- Today's Verifications -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Bugün Doğrulanan</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{dashboardStats.todayVerifications}</p>
						</div>
					</div>
				</div>

				<!-- Weekly Accuracy -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Haftalık Doğruluk</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{dashboardStats.weeklyAccuracy}%</p>
						</div>
					</div>
				</div>

				<!-- Active Economists -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Aktif Ekonomistler</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{dashboardStats.activeEconomists}</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Quick Actions -->
			<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
				<!-- Admin Actions -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Yönetim İşlemleri</h3>
					<div class="space-y-3">
						<a
							href="/admin/verification"
							class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
						>
							<div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center mr-3">
								<svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
							</div>
							<div>
								<p class="font-medium text-gray-900 dark:text-white">Doğrulama Sistemi</p>
								<p class="text-sm text-gray-500 dark:text-gray-400">Otomatik doğrulama yönetimi</p>
							</div>
						</a>

						{isAdmin && (
							<a
								href="/admin/users"
								class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
							>
								<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mr-3">
									<svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Kullanıcı Yönetimi</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">Kullanıcılar ve roller</p>
								</div>
							</a>
						)}

						<a
							href="/admin/economists"
							class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
						>
							<div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mr-3">
								<svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
								</svg>
							</div>
							<div>
								<p class="font-medium text-gray-900 dark:text-white">Ekonomist Yönetimi</p>
								<p class="text-sm text-gray-500 dark:text-gray-400">Ekonomist profilleri</p>
							</div>
						</a>

						<a
							href="/admin/content"
							class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
						>
							<div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mr-3">
								<svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
								</svg>
							</div>
							<div>
								<p class="font-medium text-gray-900 dark:text-white">İçerik Yönetimi</p>
								<p class="text-sm text-gray-500 dark:text-gray-400">Tahminler ve videolar</p>
							</div>
						</a>
					</div>
				</div>

				<!-- Recent Activity -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Son Aktiviteler</h3>
					<div class="space-y-4">
						<div class="flex items-start space-x-3">
							<div class="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
							<div>
								<p class="text-sm text-gray-900 dark:text-white">8 tahmin otomatik doğrulandı</p>
								<p class="text-xs text-gray-500 dark:text-gray-400">5 dakika önce</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
							<div>
								<p class="text-sm text-gray-900 dark:text-white">Yeni ekonomist eklendi: Dr. Mehmet Öz</p>
								<p class="text-xs text-gray-500 dark:text-gray-400">23 dakika önce</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
							<div>
								<p class="text-sm text-gray-900 dark:text-white">TCMB veri güncellendi</p>
								<p class="text-xs text-gray-500 dark:text-gray-400">1 saat önce</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-2 h-2 bg-purple-400 rounded-full mt-2"></div>
							<div>
								<p class="text-sm text-gray-900 dark:text-white">45 yeni tahmin analiz edildi</p>
								<p class="text-xs text-gray-500 dark:text-gray-400">2 saat önce</p>
							</div>
						</div>
					</div>
				</div>

				<!-- System Status -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sistem Durumu</h3>
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<span class="text-sm text-gray-600 dark:text-gray-400">TCMB API</span>
							<div class="flex items-center">
								<div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
								<span class="text-sm text-green-600 dark:text-green-400">Aktif</span>
							</div>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-sm text-gray-600 dark:text-gray-400">Doğrulama Sistemi</span>
							<div class="flex items-center">
								<div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
								<span class="text-sm text-green-600 dark:text-green-400">Çalışıyor</span>
							</div>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-sm text-gray-600 dark:text-gray-400">Veritabanı</span>
							<div class="flex items-center">
								<div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
								<span class="text-sm text-green-600 dark:text-green-400">Bağlı</span>
							</div>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-sm text-gray-600 dark:text-gray-400">Son Yedekleme</span>
							<span class="text-sm text-gray-600 dark:text-gray-400">2 saat önce</span>
						</div>
					</div>
				</div>
			</div>

			<!-- TCMB Exchange Rates Dashboard -->
			<div class="mb-8">
				<TcmbDashboard client:load />
			</div>

			<!-- Data Management -->
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
				<!-- Verification Queue -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Doğrulama Kuyruğu</h3>
					<div class="space-y-3">
						<div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
							<div>
								<p class="font-medium text-gray-900 dark:text-white">USD/TRY - 34.85 tahmini</p>
								<p class="text-sm text-gray-500 dark:text-gray-400">Dr. Ahmet Yılmaz • Hedef: 15 Ocak 2025</p>
							</div>
							<div class="flex items-center space-x-2">
								<span class="text-xs px-2 py-1 bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 rounded">Bekliyor</span>
							</div>
						</div>
						<div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
							<div>
								<p class="font-medium text-gray-900 dark:text-white">Enflasyon %4.2 tahmini</p>
								<p class="text-sm text-gray-500 dark:text-gray-400">Prof. Elif Demir • Hedef: 3 Şubat 2025</p>
							</div>
							<div class="flex items-center space-x-2">
								<span class="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded">Hazır</span>
							</div>
						</div>
						<div class="text-center pt-4">
							<a
								href="/admin/verification"
								class="text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 text-sm font-medium"
							>
								Tümünü Görüntüle →
							</a>
						</div>
					</div>
				</div>

				<!-- Performance Metrics -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performans Metrikleri</h3>
					<div class="space-y-4">
						<div class="flex justify-between items-center">
							<span class="text-sm text-gray-600 dark:text-gray-400">Toplam Tahmin</span>
							<span class="text-lg font-semibold text-gray-900 dark:text-white">{dashboardStats.totalPredictions}</span>
						</div>
						<div class="flex justify-between items-center">
							<span class="text-sm text-gray-600 dark:text-gray-400">Doğrulanmış</span>
							<span class="text-lg font-semibold text-green-600 dark:text-green-400">1,234</span>
						</div>
						<div class="flex justify-between items-center">
							<span class="text-sm text-gray-600 dark:text-gray-400">Bekleyen</span>
							<span class="text-lg font-semibold text-orange-600 dark:text-orange-400">{dashboardStats.pendingVerifications}</span>
						</div>
						<div class="flex justify-between items-center">
							<span class="text-sm text-gray-600 dark:text-gray-400">Ortalama Doğruluk</span>
							<span class="text-lg font-semibold text-blue-600 dark:text-blue-400">{dashboardStats.weeklyAccuracy}%</span>
						</div>
						<div class="pt-2 border-t border-gray-200 dark:border-gray-600">
							<p class="text-xs text-gray-500 dark:text-gray-400">Son güncelleme: {new Date().toLocaleDateString('tr-TR')}</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</main>
</Layout>
