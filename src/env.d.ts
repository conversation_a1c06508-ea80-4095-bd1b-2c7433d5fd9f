/// <reference types="astro/client" />
/// <reference types="@cloudflare/workers-types" />

import type { DbConnection } from './server/db';
import type { D1Database, VectorizeIndex } from '@cloudflare/workers-types';

// Vue 3 global compiler macros
declare global {
	const defineProps: typeof import('vue').defineProps
	const defineEmits: typeof import('vue').defineEmits
	const defineExpose: typeof import('vue').defineExpose
	const withDefaults: typeof import('vue').withDefaults
	const defineSlots: typeof import('vue').defineSlots
	const defineModel: typeof import('vue').defineModel
}

// Environment variables for Cloudflare
type CloudflareEnv = {
	DB: D1Database;
	VECTORIZE: VectorizeIndex;
	[key: string]: any;
	// Add other bindings like KV namespaces here if needed
};

declare namespace App {
	interface Locals {
		auth: {
			user: any | null;
			roles: string[];
			isAuthenticated: boolean;
		};
		db: DbConnection;
		env: CloudflareEnv; // Populated by middleware
		// Provided by the @astrojs/cloudflare adapter.
		// Will be available in `astro dev` and during `astro build`.
		runtime?: {
			env: CloudflareEnv;
		};
	}
}

// Add platform property for local development with wrangler
declare module 'astro' {
    interface APIContext {
        platform?: {
            env: CloudflareEnv
        }
    }
}

// Environment variables
interface ImportMetaEnv {
	readonly DATABASE_URL: string;
	readonly CLOUDFLARE_D1_DATABASE_ID: string;
	readonly JWT_SECRET: string;
	readonly JWT_EXPIRES_IN: string;
	readonly LLM_PROVIDER: 'openai' | 'anthropic' | 'cloudflare';
	readonly OPENAI_API_KEY: string;
	readonly ANTHROPIC_API_KEY: string;
	readonly CLOUDFLARE_ACCOUNT_ID: string;
	readonly CLOUDFLARE_API_TOKEN: string;
	readonly VECTORIZE_INDEX_NAME: string;
	readonly LOGFLARE_API_KEY: string;
	readonly LOGFLARE_SOURCE_TOKEN: string;
	readonly NODE_ENV: 'development' | 'production' | 'test';
	readonly API_BASE_URL: string;
	readonly CORS_ORIGINS: string;
	readonly RATE_LIMIT_WINDOW_MS: string;
	readonly RATE_LIMIT_MAX_REQUESTS: string;
}

// API Response Types
export interface ApiResponse<T = any> {
	status: 'success' | 'error' | 'fail';
	data?: T;
	message?: string;
	errorCode?: string;
	details?: any[];
}

export interface PaginationInfo {
	currentPage: number;
	pageSize: number;
	totalItems: number;
	totalPages: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
	pagination?: PaginationInfo;
}

// User Roles
export type UserRole =
	| 'ROLE_ANONYMOUS'
	| 'ROLE_GUEST'
	| 'ROLE_FREE_USER'
	| 'ROLE_PAID_USER'
	| 'ROLE_CONTRIBUTOR'
	| 'ROLE_ADMIN';

// Common Entity Types
export interface BaseEntity {
	id: string;
	createdAt: Date;
	updatedAt: Date;
}

// Export for use in other files
export {};
