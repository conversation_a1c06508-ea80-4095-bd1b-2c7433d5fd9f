import { Hono } from 'hono';
import { createVerificationService } from '@/server/services/verification';
import { VerificationScheduler } from '@/server/services/verification/scheduler';
import { getDbFromContext } from '../../data';
import { requireModerator } from '../../auth/middleware';

// Simple in-memory scheduler instance for development
// In production, you'd want to use a more robust solution like Redis or a proper job queue
let globalSchedulerInstance: VerificationScheduler | null = null;

const app = new Hono();

// Helper function to get or create scheduler instance
function getSchedulerInstance(db: ReturnType<typeof getDbFromContext>): VerificationScheduler {
	if (!globalSchedulerInstance && db) {
		const verificationService = createVerificationService(db);
		globalSchedulerInstance = new VerificationScheduler(verificationService);
	}
	return globalSchedulerInstance!;
}

// GET endpoint to check scheduler status
app.get('/', requireModerator(), async (c) => {
	const db = getDbFromContext(c);
	if (!db) {
		return c.json({
			success: false,
			error: 'Database connection not available'
		}, 503);
	}

	try {
		const scheduler = getSchedulerInstance(db);
		const status = scheduler.getStatus();

		return c.json({
			success: true,
			data: { scheduler: status },
			message: 'Scheduler status retrieved successfully'
		});

	} catch (error) {
		console.error('Error getting scheduler status:', error);

		return c.json({
			success: false,
			error: 'Failed to get scheduler status',
			message: error instanceof Error ? error.message : 'Unknown error'
		}, 500);
	}
});

// POST endpoint to control scheduler
app.post('/', requireModerator(), async (c) => {
	const db = getDbFromContext(c);
	if (!db) {
		return c.json({
			success: false,
			error: 'Database connection not available'
		}, 503);
	}

	try {
		const body = await c.req.json();
		const { action, intervalHours = 6 } = body;

		const scheduler = getSchedulerInstance(db);

		switch (action) {
			case 'start':
				scheduler.start(intervalHours);
				return c.json({
					success: true,
					message: `Verification scheduler started (running every ${intervalHours} hours)`
				});

			case 'stop':
				scheduler.stop();
				return c.json({
					success: true,
					message: 'Verification scheduler stopped'
				});

			case 'run':
				await scheduler.runVerification();
				return c.json({
					success: true,
					message: 'Manual verification run completed'
				});

			case 'status': {
				const status = scheduler.getStatus();
				return c.json({
					success: true,
					data: { scheduler: status },
					message: 'Scheduler status retrieved'
				});
			}

			default:
				return c.json({
					success: false,
					error: 'Invalid action',
					message: 'Action must be one of: start, stop, run, status'
				}, 400);
		}

	} catch (error) {
		console.error('Error controlling scheduler:', error);

		return c.json({
			success: false,
			error: 'Scheduler control failed',
			message: error instanceof Error ? error.message : 'Unknown error'
		}, 500);
	}
});

export default app;
