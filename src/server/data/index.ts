import { initDb, type DbConnection } from '../db';

/**
 * Get database connection from Hono context
 * This works with the Astro middleware that initializes the database
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getDbFromContext(context: any): DbConnection | null {
  // Get the database from the request that was passed through from Astro
  const request = context.req?.raw;
  if (request?.__astroLocals?.db) {
    return request.__astroLocals.db;
  }

  // Fallback: Try to get database from various context sources
  if (context.env?.DB) {
    return initDb(context.env.DB);
  }

  return null;
}

/**
 * Database health check utility
 */
export async function checkDbHealth(db: DbConnection): Promise<boolean> {
  try {
    // Simple query to check if database is accessible
    await db.run('SELECT 1');
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}
