# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
# This application supports 4 environments:
# - LOCAL: Local development using Cloudflare D1 (NODE_ENV=development, via wrangler dev)
# - DEVELOPMENT: Deployed to Cloudflare Workers dev environment with D1
# - STAGING: Deployed to Cloudflare Workers staging environment with D1
# - PRODUCTION: Deployed to Cloudflare Workers production environment with D1

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# LOCAL: Uses Cloudflare D1 via wrangler dev
# Database URL - not used with D1, but kept for compatibility
# DATABASE_URL="file:./local.db"

# DEVELOPMENT/STAGING/PRODUCTION: Environment-specific D1 database IDs
CLOUDFLARE_D1_DEV_DATABASE_ID=your_dev_d1_database_id_here
CLOUDFLARE_D1_STAGING_DATABASE_ID=your_staging_d1_database_id_here
CLOUDFLARE_D1_PROD_DATABASE_ID=your_prod_d1_database_id_here

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN="7d"

# =============================================================================
# LLM CONFIGURATION
# =============================================================================
LLM_PROVIDER=openai # or "cloudflare", "anthropic"
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=""

# =============================================================================
# CLOUDFLARE CONFIGURATION
# =============================================================================
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_API_TOKEN=your_api_token_here

# Vector Database
VECTORIZE_INDEX_NAME=""

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOGFLARE_API_KEY=""
LOGFLARE_SOURCE_TOKEN=""

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment: local, development, staging, production
NODE_ENV=development
API_BASE_URL="http://localhost:4321"
CORS_ORIGINS="http://localhost:4321,http://localhost:3000"

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
RATE_LIMIT_WINDOW_MS="900000" # 15 minutes
RATE_LIMIT_MAX_REQUESTS="100"
