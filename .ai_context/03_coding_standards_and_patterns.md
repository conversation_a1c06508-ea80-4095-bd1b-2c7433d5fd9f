# 03_coding_standards_and_patterns.md

This document outlines the coding standards, formatting guidelines, and architectural patterns to be followed in this project. Adherence to these standards will ensure consistency, readability, and maintainability, especially when working with AI code generation tools.

## 1. Formatting

- **Formatter:** **Prettier** will be used as the primary code formatter.
  &#x20; \* **Configuration:**
  &#x20; \* **Indentation:** Tabs.
  &#x20; \* **Semicolons:** Yes (must be used at the end of statements).
  &#x20; \* **Trailing Commas:** Yes (e.g., `es5` or `all` - to be used in multi-line object literals, arrays, function parameters, etc.).
  &#x20; \* **Print Width:** (e.g., 80 or 100 characters - standard Prettier defaults will be used unless a specific value is set in the config).
  &#x20; \* **Quote Style:** (e.g., single or double quotes - standard Prettier defaults will be used unless a specific style is set, ensure consistency).
  &#x20; \* A `.prettierrc.js` (or similar) configuration file will be present in the project root. The AI should be instructed to respect this configuration.
- **Linter:** **ESLint** will be used for identifying and reporting on patterns in JavaScript/TypeScript.
  &#x20; \* **Configuration:**
  &#x20; \* Will be set up with recommended rules for TypeScript, Vue.js, and Astro.
  &#x20; \* Will be configured to work harmoniously with Prettier (e.g., using `eslint-config-prettier`).
  &#x20; \* An `.eslintrc.js` (or similar) configuration file will be present in the project root.

## 2. Naming Conventions

Consistency in naming is crucial for readability and maintainability.

- **Variables & Functions (local scope, parameters):** `camelCase`
  &#x20; \* Example: `let myVariable = 'test';`, `function calculateValue(valueOne, valueTwo) { /* ... */ }`
- **Classes, Interfaces, Types, Enums, Vue/Astro Components (Types/Constructors/Filenames):** `PascalCase`
  &#x20; \* Component filenames: `MyComponent.astro`, `UserDetails.vue`
  &#x20; \* Type/Interface definitions: `interface IUserService { /* ... */ }`, `type UserProfile = { /* ... */ };`
  &#x20; \* Class definitions: `class DataHandler { /* ... */ }`
- **Constants (globally accessible, unchanging configuration values):** `PascalCase`
  &#x20; \* Example: `const MaxUsers = 100;`, `const ApiBaseUrl = 'https://api.example.com';`
- **File Names (non-component .ts, .js files):** `kebab-case`
  &#x20; \* Example: `user-service.ts`, `api-helpers.js`
- **Folder Names:** `kebab-case`
  &#x20; \* Example: `src/components`, `src/api-routes`, `src/utility-functions`

## 3. Architectural Patterns

The overarching goal is **modularity** and **maintainability**, ensuring files are small and focused on a single responsibility.

- **Backend (Hono on Cloudflare Workers with Drizzle ORM):**
  &#x20; \* **Feature-Based Routing & Structure:**
  &#x20; \* Organize backend code by features (e.g., `src/server/features/users/`, `src/server/features/videos/`).
  &#x20; \* Each feature directory would contain its Hono routes (`*.routes.ts`), service logic (`*.service.ts`), type definitions (`*.types.ts`), and potentially Drizzle schema definitions relevant to that feature if not centralized.
  &#x20; \* Main worker (`src/server/index.ts` or similar) would import and mount these feature-specific Hono route modules.
  &#x20; \* **Service Layer:**
  &#x20; \* Business logic should be encapsulated within "service" functions or classes (e.g., `userService.ts` containing `getUserById`, `createPredictionRecord`, etc.). Hono route handlers should be lean and primarily delegate to these services.
  &#x20; \* Services will handle interactions with Drizzle ORM. They can either use the Drizzle client directly or, for more complex scenarios or to facilitate easier testing, interact with Drizzle via focused repository-like functions.
  &#x20; \* **Dependency Management:** Dependencies (like the Drizzle client or external service clients) will be managed explicitly, likely passed via Hono's context or as parameters to service functions.
  &#x20; \* **Modularity Principle:** Each file (route definitions, service, utility) should be kept concise and focused on a specific aspect of a feature.
- **Frontend (Astro with Vue Islands):**
  &#x20; \* **Directory Structure:**
  &#x20; \* `src/pages/`: Astro files defining routes and page structure.
  &#x20; \* `src/layouts/`: Astro layout components.
  &#x20; \* `src/components/astro/`: Reusable Astro components.
  &#x20; \* `src/components/vue/`: Reusable Vue.js components (used as Astro islands).
  &#x20; \* `src/features/[feature-name]/`: For larger, self-contained frontend features, potentially including:
  &#x20; \* `components/`: Feature-specific Astro and Vue components.
  &#x20; \* `services/`: Frontend services for API calls.
  &#x20; \* `stores/`: State management stores (if applicable).
  &#x20; \* `types.ts`: TypeScript types specific to this feature.
  &#x20; \* `src/services/api/`: General-purpose frontend API client functions.
  &#x20; \* `src/stores/`: Global or shared state stores (e.g., using `Astro.store`).
  &#x20; \* `src/utils/`: Common utility functions.
  &#x20; \* `src/assets/`: Static assets.
  &#x20; \* `src/env.d.ts`, `src/shims-vue.d.ts`: TypeScript declaration files.
  &#x20; \* **Component Design:**
  &#x20; \* Astro components for static content, layout, and server-side logic.
  &#x20; \* Vue components (Astro Islands) for client-side interactivity.
  &#x20; \* Clear props and events for Vue components.
  &#x20; \* Components should adhere to the Single Responsibility Principle.
- **General Principles:**
  &#x20; \* **SOLID:** Adhere to SOLID principles, especially SRP.
  &#x20; \* **DRY (Don't Repeat Yourself):** Abstract common logic.
  &#x20; \* **Separation of Concerns:** Maintain clear boundaries between API logic, business logic, data access, and UI.

## 4. Error Handling

A robust and consistent error handling strategy is essential.

- **Custom Error Classes (Backend):**
  &#x20; \* Define a base `AppError extends Error` class including `statusCode` (HTTP status) and `errorCode` (string literal).
  &#x20; \* Specific error classes (`NotFoundError`, `ValidationError`, `AuthenticationError`, `AuthorizationError`, `ConflictError`) will extend `AppError`.
  &#x20; \* Services throw these custom errors. Hono middleware will catch them and format a standardized JSON error response.
- **Logging:**
  &#x20; \* **Strategy:** Implement comprehensive logging on both backend (Cloudflare Workers) and frontend.
  &#x20; \* **Initial Service:** **Logflare** for initial error tracking and basic logging from Cloudflare Workers.
  &#x20; \* **Long-term Goal:** Plan for and potentially build out a self-hosted LGTM (Loki, Grafana, Tempo, Mimir) stack for more comprehensive observability.
  &#x20; \* Frontend logs will also be directed appropriately (initially to browser console, with plans to integrate with chosen logging solution).
- **Frontend Error Handling & Display:**
  &#x20; \* API call errors caught gracefully.
  &#x20; \* **User Notification:** Non-obtrusive toast notifications for most non-critical errors.
  &#x20; \* **Critical Errors:** Clear, user-friendly messages within the affected UI part. Avoid raw error messages.
  &#x20; \* **Vue Error Boundaries:** Use Vue's `errorCaptured` hook or custom error boundary components.
  &#x20; \* **Client-side Logging:** All caught frontend errors to be logged to the configured logging service.

## 5. State Management (Frontend)

- **Primary Solution:** **`Astro.store`** will be evaluated and used for global or shared state management.
  &#x20; \* Its experimental nature will be monitored.
- **Vue Component State:** Standard Vue reactivity (`ref`, `reactive`), props, and events for local state.
- **Alternative (if&#x20;\*\***`Astro.store`\***\*&#x20;is unsuitable):** **Pinia** or **Nano Stores** will be considered.

## 6. Testing

- **Methodology:** Test-Driven Development (TDD) is the preferred approach where applicable.
- **Testing Frameworks/Libraries:**
  &#x20; \* **Unit & Integration Tests:** **Vitest**.
  &#x20; \* **End-to-End (E2E) Tests:** **Playwright**.
- **Types of Tests to be Implemented:**
  &#x20; \* Unit Tests: For individual functions, modules, Vue components, Astro components (where testable), and service logic.
  &#x20; \* Integration Tests: For interactions between different parts of the application.
  &#x20; \* E2E Tests: Cover critical user pathways.
- **Test File Location & Naming:**
  &#x20; \* A separate top-level `tests/` directory.
  &#x20; \* Sub-directories: `tests/unit/`, `tests/integration/`, `tests/e2e/`.
  &#x20; \* Test files named `[filename].test.ts` or `[filename].spec.ts`.
- **Test Coverage:** Aim for reasonable coverage of critical logic and features.
