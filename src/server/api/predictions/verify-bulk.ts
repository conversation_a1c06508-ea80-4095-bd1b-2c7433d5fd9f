import { Hono } from 'hono';
import { eq, inArray } from 'drizzle-orm';
import { predictions, economists } from '@/server/db';
import { getDbFromContext } from '../../data';
import { requireModerator } from '../../auth/middleware';

const app = new Hono();

// Bulk verification endpoint
app.post('/', requireModerator(), async (c) => {
	const db = getDbFromContext(c);
	if (!db) {
		return c.json({
			success: false,
			error: 'Database connection not available'
		}, 503);
	}

	try {
		const body = await c.req.json();
		const { verifications } = body;

		if (!Array.isArray(verifications) || verifications.length === 0) {
			return c.json({
				success: false,
				error: 'Invalid request body. Expected array of verifications.',
			}, 400);
		}

		// Validate each verification entry
		for (const verification of verifications) {
			if (!verification.id || !verification.status) {
				return c.json({
					success: false,
					error: 'Each verification must have id and status fields.',
				}, 400);
			}

			if (!['verified_correct', 'verified_incorrect', 'expired', 'partially_verified'].includes(verification.status)) {
				return c.json({
					success: false,
					error: `Invalid status: ${verification.status}`,
				}, 400);
			}
		}

		const results = [];
		const errors = [];

		// Process each verification
		for (const verification of verifications) {
			try {
				const predictionId = parseInt(verification.id);

				// Fetch the prediction
				const prediction = await db.select().from(predictions).where(eq(predictions.id, predictionId)).get();

				if (!prediction) {
					errors.push({
						id: predictionId,
						error: 'Prediction not found'
					});
					continue;
				}

				// Calculate accuracy score
				const accuracyScore = calculateAccuracyScore(
					prediction,
					verification.actualValue,
					verification.status
				);

				// Update prediction
				const updatedPrediction = await db
					.update(predictions)
					.set({
						status: verification.status,
						accuracyScore,
						verificationMethod: verification.verificationMethod || 'bulk_verification',
						verificationSource: verification.verificationSource,
						verificationNotes: verification.verificationNotes,
						verifiedAt: new Date(),
						updatedAt: new Date(),
					})
					.where(eq(predictions.id, predictionId))
					.returning()
					.get();

				if (updatedPrediction) {
					results.push({
						id: updatedPrediction.id,
						status: updatedPrediction.status,
						accuracyScore: updatedPrediction.accuracyScore ? Math.round(updatedPrediction.accuracyScore * 100) : null,
						verifiedAt: updatedPrediction.verifiedAt,
					});
				}
			} catch (error) {
				errors.push({
					id: verification.id,
					error: error instanceof Error ? error.message : 'Unknown error'
				});
			}
		}

		// After bulk verification, update economist statistics
		await updateEconomistStatistics(db, verifications.map(v => parseInt(v.id)));

		return c.json({
			success: true,
			data: {
				updated: results,
				errors: errors,
				summary: {
					total: verifications.length,
					successful: results.length,
					failed: errors.length
				}
			},
			message: `Bulk verification completed. ${results.length}/${verifications.length} predictions updated successfully.`
		});

	} catch (error) {
		console.error('Error in bulk verification:', error);
		return c.json({
			success: false,
			error: 'Failed to process bulk verification',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

/**
 * Update economist statistics after verification changes
 */
async function updateEconomistStatistics(db: ReturnType<typeof getDbFromContext>, predictionIds: number[]) {
	if (!db) return;

	try {
		// Get unique economist IDs from the updated predictions
		const updatedPredictions = await db
			.select()
			.from(predictions)
			.where(inArray(predictions.id, predictionIds));

		const uniqueEconomistIds = [...new Set(updatedPredictions.map((p: { economistId: number }) => p.economistId))];

		// Update statistics for each affected economist
		for (const economistId of uniqueEconomistIds) {
			// Get all verified predictions for this economist
			const economistPredictions = await db
				.select()
				.from(predictions)
				.where(eq(predictions.economistId, economistId as number));

			const verifiedPredictions = economistPredictions.filter(p =>
				p.status === 'verified_correct' || p.status === 'verified_incorrect' || p.status === 'partially_verified'
			);

			const correctPredictions = economistPredictions.filter(p => p.status === 'verified_correct');

			// Calculate accuracy scores
			const accuracyScores = verifiedPredictions
				.map(p => p.accuracyScore)
				.filter((score): score is number => score !== null);

			const averageAccuracy = accuracyScores.length > 0
				? accuracyScores.reduce((sum, score) => sum + score, 0) / accuracyScores.length
				: null;

			const predictionAccuracy = verifiedPredictions.length > 0
				? correctPredictions.length / verifiedPredictions.length
				: null;

			// Calculate trust score (weighted average of accuracy metrics)
			let trustScore = null;
			if (averageAccuracy !== null && predictionAccuracy !== null) {
				// Weight: 70% average accuracy score, 30% prediction accuracy rate
				trustScore = (averageAccuracy * 0.7) + (predictionAccuracy * 0.3);
			} else if (averageAccuracy !== null) {
				trustScore = averageAccuracy;
			} else if (predictionAccuracy !== null) {
				trustScore = predictionAccuracy;
			}

			// Update economist statistics
			await db
				.update(economists)
				.set({
					totalPredictions: economistPredictions.length,
					correctPredictions: correctPredictions.length,
					accuracyScore: averageAccuracy,
					trustScore: trustScore,
					updatedAt: new Date(),
				})
				.where(eq(economists.id, economistId));
		}

		console.log(`Updated statistics for ${uniqueEconomistIds.length} economists`);
	} catch (error) {
		console.error('Error updating economist statistics:', error);
	}
}

/**
 * Calculate accuracy score based on prediction type and actual outcome
 */
function calculateAccuracyScore(prediction: Record<string, unknown>, actualValue: number | null, status: string): number | null {
	// Handle simple cases first
	if (status === 'expired') {
		return 0; // Expired predictions get 0 accuracy
	}

	if (status === 'verified_correct') {
		return 1; // Fully correct predictions get 100%
	}

	if (status === 'verified_incorrect') {
		return 0; // Incorrect predictions get 0%
	}

	// Handle partially verified predictions with actual value comparison
	if (status === 'partially_verified' && actualValue !== null && actualValue !== undefined) {
		try {
			const targetValue = prediction.targetValue ? JSON.parse(String(prediction.targetValue)) : null;

			if (!targetValue) {
				return 0.5; // Default partial score if no target value
			}

			// Calculate based on prediction type
			switch (prediction.predictionType) {
				case 'exact_value':
					return calculateExactValueAccuracy(targetValue, actualValue);

				case 'range':
					return calculateRangeAccuracy(targetValue, actualValue);

				case 'increase':
				case 'decrease':
					return calculateDirectionalAccuracy(targetValue, actualValue, prediction.predictionType);

				default:
					return 0.5; // Default partial score for unknown types
			}
		} catch (error) {
			console.error('Error calculating accuracy score:', error);
			return 0.5; // Default partial score on error
		}
	}

	return null; // No score for pending status
}

function calculateExactValueAccuracy(targetValue: Record<string, unknown>, actualValue: number): number {
	const target = Number(targetValue.value);
	if (isNaN(target) || typeof actualValue !== 'number') {
		return 0;
	}

	const percentageError = Math.abs(target - actualValue) / target;
	return Math.max(0, 1 - percentageError);
}

function calculateRangeAccuracy(targetValue: Record<string, unknown>, actualValue: number): number {
	if (!targetValue.min || !targetValue.max || typeof actualValue !== 'number') {
		return 0;
	}

	const min = parseFloat(String(targetValue.min || '0'));
	const max = parseFloat(String(targetValue.max || '0'));

	if (actualValue >= min && actualValue <= max) {
		return 1;
	}

	const rangeSize = max - min;
	const distanceFromRange = actualValue < min ? min - actualValue : actualValue - max;
	const relativeError = distanceFromRange / rangeSize;

	return Math.max(0, 1 - relativeError);
}

function calculateDirectionalAccuracy(targetValue: Record<string, unknown>, actualValue: number, predictionType: string): number {
	if (!targetValue.baseline || typeof actualValue !== 'number') {
		return 0;
	}

	const baseline = parseFloat(String(targetValue.baseline || '0'));
	const isIncrease = actualValue > baseline;
	const predictedIncrease = predictionType === 'increase';

	if (isIncrease === predictedIncrease) {
		if (targetValue.magnitude) {
			const actualMagnitude = Math.abs(actualValue - baseline);
			const targetMagnitude = parseFloat(String(targetValue.magnitude || '0'));
			const magnitudeAccuracy = 1 - Math.abs(actualMagnitude - targetMagnitude) / targetMagnitude;
			return Math.max(0.6, Math.min(1, magnitudeAccuracy));
		}
		return 0.8;
	}

	return 0;
}

export default app;
