// Automated Prediction Verification System
import { predictions, economists } from '@/server/db';
import { drizzle } from 'drizzle-orm/d1';
import { eq, and, lt } from 'drizzle-orm';

// Define types locally since they're not exported from schema
type PredictionStatus = 'pending' | 'partially_verified' | 'verified_correct' | 'verified_incorrect' | 'expired';
type PredictionCategory = 'exchange_rate' | 'inflation' | 'interest_rate' | 'gold' | 'stock_market' | 'crypto';
type Database = ReturnType<typeof drizzle>;

export interface VerificationResult {
  success: boolean;
  predictionId: number;
  actualValue?: number;
  accuracy?: number;
  status: PredictionStatus;
  verificationSource?: string;
  verificationNotes?: string;
  error?: string;
}

export interface PredictionData {
  id: number;
  title: string;
  description: string;
  category: PredictionCategory;
  predictionType: string;
  targetValue: string; // JSON string
  targetDate: Date;
  confidenceLevel: number | null;
  economistId: number;
  videoId: number;
  extractedAt: Date;
  sourceText: string | null;
  status: PredictionStatus;
}

export interface DataSourceConnector {
  name: string;
  categories: PredictionCategory[];
  fetchCurrentValue(_category: PredictionCategory, _params?: Record<string, unknown>): Promise<number | null>;
  isAvailable(): Promise<boolean>;
}

export class PredictionVerificationService {
  private db: Database;

  constructor(db: Database) {
    this.db = db;
  }

  /**
   * Get predictions that are ready for verification
   * (target date has passed and status is still pending)
   */
  async getPredictionsReadyForVerification(): Promise<Array<PredictionData & { economist?: typeof economists.$inferSelect }>> {
    const now = new Date();
    const readyPredictions = await this.db
      .select()
      .from(predictions)
      .innerJoin(economists, eq(predictions.economistId, economists.id))
      .where(
        and(
          eq(predictions.status, 'pending'),
          lt(predictions.targetDate, now)
        )
      );
    return readyPredictions.map((row: unknown) => {
      const typedRow = row as { predictions: typeof predictions.$inferSelect; economists: typeof economists.$inferSelect };
      return {
        ...(typedRow.predictions as PredictionData),
        economist: typedRow.economists
      };
    });
  }

  /**
   * Verify a single prediction (placeholder logic)
   */
  async verifyPrediction(prediction: PredictionData): Promise<VerificationResult> {
    try {
      // Placeholder: always returns pending
      return {
        success: true,
        predictionId: prediction.id,
        status: 'pending',
        verificationNotes: 'Verification service placeholder'
      };
    } catch (error) {
      return {
        success: false,
        predictionId: prediction.id,
        status: 'pending',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Run verification for all ready predictions
   */
  async runAutomatedVerification(): Promise<VerificationResult[]> {
    console.log('Starting automated prediction verification...');
    const readyPredictions = await this.getPredictionsReadyForVerification();
    const results: VerificationResult[] = [];
    for (const prediction of readyPredictions) {
      const result = await this.verifyPrediction(prediction);
      results.push(result);
    }
    console.log(`Verification completed: ${results.length} predictions processed`);
    return results;
  }
}

// Factory/helper functions
export function createVerificationService(db: Database) {
  return new PredictionVerificationService(db);
}

export function getVerificationService(db: Database): PredictionVerificationService {
  return createVerificationService(db);
}

export function createVerificationServiceInstance(db: Database) {
  return new PredictionVerificationService(db);
}
