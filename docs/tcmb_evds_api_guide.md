# TCMB EVDS Web Service Usage Guide (Reference)

This PDF is the official documentation for the TCMB (Central Bank of Turkey) EVDS Web Service API. It describes how to access Turkish economic data (exchange rates, interest rates, inflation, etc.) programmatically.

- **Source:** https://evds2.tcmb.gov.tr/index.php?/evds/userDocs
- **File:** docs/TCMB_EVDS_Web_Servis_Kullanim_Kilavuzu.pdf

Please refer to this document for official API endpoints, authentication, and usage details.

---

# TCMB EVDS API Developer Guide

## Overview
This document provides a practical guide for developers integrating the TCMB (Central Bank of Turkey) EVDS API into the YouTube Economist Trust Score platform. It references the official PDF documentation and provides sample usage for common economic indicators.

## Reference Documentation
- [EVDS Web Servis Kullanım Kılavuzu (PDF)](./EVDS_Web_Servis_Kullanim_Kilavuzu.pdf)

## API Access
- **Base URL:** `https://evds2.tcmb.gov.tr/service/evds/`
- **Authentication:** Requires a free API key (register on the EVDS website)
- **Formats:** JSON, XML, CSV

## Example: Fetching USD/TRY Exchange Rate
```
GET https://evds2.tcmb.gov.tr/service/evds/?series=TP.DK.USD.S.YTL&startDate=01-01-2025&endDate=04-07-2025&type=json&key=YOUR_API_KEY
```

**Sample JSON Response:**
```json
{
  "items": [
    { "Tarih": "04-07-2025", "TP.DK.USD.S.YTL": "39.7485" },
    ...
  ]
}
```

## Common Series Codes
- USD/TRY: `TP.DK.USD.S.YTL`
- EUR/TRY: `TP.DK.EUR.S.YTL`
- Interest Rate: `TP.PPKON.1W`
- See the PDF for more codes.

## Implementation Notes
- Place integration logic in `src/server/services/verification/tcmb.ts`.
- Store API keys in environment variables.
- Normalize and validate all data.
- Add unit tests for all fetch logic.

## Further Reading
- [Official EVDS API Portal](https://evds2.tcmb.gov.tr/)
- [Official PDF Documentation](./EVDS_Web_Servis_Kullanim_Kilavuzu.pdf)

---

Update this guide as the integration evolves or if new endpoints are added.
