import { defineConfig } from 'drizzle-kit';

const nodeEnv = process.env.NODE_ENV || 'development';
const isStaging = nodeEnv === 'staging';
const isProduction = nodeEnv === 'production';

// Determine the environment and database configuration
const getDbConfig = () => {
	// Production environment - use Cloudflare D1
	if (isProduction) {
		return {
			driver: 'd1-http' as const,
			dbCredentials: {
				accountId: process.env.CLOUDFLARE_ACCOUNT_ID!,
				databaseId: process.env.CLOUDFLARE_D1_PROD_DATABASE_ID!,
				token: process.env.CLOUDFLARE_API_TOKEN!,
			},
		};
	}

	// Staging environment - use Cloudflare D1
	if (isStaging) {
		return {
			driver: 'd1-http' as const,
			dbCredentials: {
				accountId: process.env.CLOUDFLARE_ACCOUNT_ID!,
				databaseId: process.env.CLOUDFLARE_D1_STAGING_DATABASE_ID!,
				token: process.env.CLOUDFLARE_API_TOKEN!,
			},
		};
	}

	// Development environment (both local and deployed) - use Cloudflare D1
	return {
		driver: 'd1-http' as const,
		dbCredentials: {
			accountId: process.env.CLOUDFLARE_ACCOUNT_ID!,
			databaseId: process.env.CLOUDFLARE_D1_DEV_DATABASE_ID!,
			token: process.env.CLOUDFLARE_API_TOKEN!,
		},
	};
};

export default defineConfig({
	dialect: 'sqlite',
	schema: './src/server/db/schema.ts',
	out: './drizzle',
	...getDbConfig(),
	verbose: nodeEnv === 'development',
	strict: true,
});
