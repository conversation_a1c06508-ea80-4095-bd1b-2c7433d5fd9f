---
// Navigation header component
import ThemeToggle from '@/components/vue/ThemeToggle.vue';
import MarketTicker from '@/components/vue/MarketTicker.vue';
import Logo from './Logo.astro';
import { Icon } from 'astro-icon/components';

interface Props {
	title?: string;
	currentPath?: string;
}

const { currentPath } = Astro.props;

// Use passed currentPath or fallback to Astro.url.pathname for SSR pages
// Normalize the active path
const normalizedActivePath = (currentPath || Astro.url.pathname || '').toLowerCase().replace(/\/+$/, '') || '/';

// Navigation items with Astro Icon names
const navItems = [
	{ name: '<PERSON>', href: '/', icon: 'mdi:home' },
	{ name: '<PERSON>ünde<PERSON>', href: '/trending', icon: 'mdi:trending-up' },
	{ name: 'Ekonomistler', href: '/economists', icon: 'mdi:account-group' },
	{ name: '<PERSON><PERSON><PERSON><PERSON>', href: '/predictions', icon: 'mdi:crystal-ball' },
	{ name: '<PERSON><PERSON><PERSON><PERSON>', href: '/articles', icon: 'mdi:newspaper-variant-multiple' },
	{ name: '<PERSON><PERSON> ve Ekonomi', href: '/kategori/is-ekonomi', icon: 'mdi:briefcase' },
	{ name: 'Piyasalar', href: '/kategori/piyasalar', icon: 'mdi:chart-bar' },
	{ name: 'Kripto Para', href: '/kategori/kripto', icon: 'mdi:bitcoin' },
	{ name: 'Döviz', href: '/kategori/doviz', icon: 'mdi:currency-usd-circle' },
	{ name: 'Altın', href: '/kategori/altin', icon: 'mdi:gold' },
	{ name: 'Borsa', href: '/kategori/borsa', icon: 'mdi:chart-line' },
	{ name: 'Enflasyon', href: '/kategori/enflasyon', icon: 'mdi:trending-down' },
	{ name: 'Faiz', href: '/kategori/faiz', icon: 'mdi:percent' },
];
---

<header class="sticky top-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 shadow-lg transition-colors duration-200">
	<!-- Top Bar with Logo and Search -->
	<div class="bg-slate-500 dark:bg-gray-800 border-b border-gray-400 dark:border-gray-700 transition-colors duration-200">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex items-center justify-between h-16">
				<!-- Logo -->
				<div class="flex items-center space-x-4">
					<a href="/" class="flex items-center space-x-3 group text-white dark:text-gray-200 "> {/* Text color for svg currentColor */}
						<Logo class="h-8 w-36 bg-slate-500 dark:bg-gray-800 transition-colors duration-200" /> {/* Size & placeholder BG */}
						<div class="hidden md:block">
							<div class="font-bold text-lg group-hover:text-amber-500 dark:group-hover:text-amber-400 transition-colors duration-200">
								Ekonomist Güven Skoru
							</div>
							<div class="text-gray-200 dark:text-gray-400 text-xs">
								YouTube Uzmanları Analiz Platformu
							</div>
						</div>
					</a>
				</div>

				<!-- Search and User Actions -->
				<div class="flex items-center space-x-4">
					<!-- Search Bar -->
					<div class="hidden sm:block">
						<div class="relative">
							<input
								type="search"
								placeholder="Ekonomist veya konu ara..."
								class="w-64 px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-colors"
							>
							<div class="absolute inset-y-0 right-0 flex items-center pr-3">
								<Icon name="mdi:magnify" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
							</div>
						</div>
					</div>

					<!-- Mobile Search Button -->
					<button class="md:hidden p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none transition-colors">
						<Icon name="mdi:magnify" class="w-5 h-5" />
					</button>

					<!-- User Menu - Will be hydrated client-side for all pages (SSG and SSR) -->
					<div class="flex items-center space-x-3">
						<!-- Theme Toggle -->
						<ThemeToggle client:idle />

						<!-- User menu container - will be populated by ClientAuth -->
						<div data-user-menu class="transition-all duration-200 ease-in-out">
							<!-- Default state - login/register buttons (most common case) -->
							<div class="flex items-center space-x-2">
								<a href="/login" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium transition-colors">
									Giriş
								</a>
								<a href="/register" class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
									Kayıt Ol
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Scrollable Navigation Bar -->
	<div class="bg-slate-500 dark:bg-gray-800 transition-colors duration-200">

		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="relative">
				<div id="scroll-arrow-left" class="scroll-arrow left-arrow absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 cursor-pointer hidden bg-slate-500/70 dark:bg-gray-800/70 hover:bg-slate-600/90 dark:hover:bg-gray-700/90 rounded-full">
					<Icon name="mdi:chevron-left" class="w-5 h-5 text-white" />
				</div>
				<!-- Fade Effect for Scrolling -->
				<div id="fade-effect-left" class="absolute hidden left-0 top-0 bottom-0 w-24 bg-gradient-to-r from-slate-500 dark:from-gray-800 to-transparent pointer-events-none transition-colors duration-200"></div>

				<!-- Navigation Items -->
				<div class="flex overflow-x-auto scrollbar-hide py-3 space-x-1" id="horizontal-nav">
					{navItems.map((item) => (
						<a
							href={item.href}
							class={`
								flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200
								${normalizedActivePath === item.href
									? 'bg-amber-500 text-white shadow-lg'
									: 'text-gray-100 dark:text-gray-400 hover:text-white dark:hover:text-gray-100 hover:bg-gray-800 dark:hover:bg-gray-700'
								}
							`}
						>
							<Icon name={item.icon} class="w-4 h-4" />
							<span>{item.name}</span>
						</a>
					))}
				</div>

				<!-- Fade Effect for Scrolling -->
				<div id="fade-effect-right" class="absolute hidden right-0 top-0 bottom-0 w-24 bg-gradient-to-l from-slate-500 dark:from-gray-800 to-transparent pointer-events-none transition-colors duration-200"></div>
				<div id="scroll-arrow-right" class="scroll-arrow right-arrow absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 cursor-pointer hidden bg-slate-500/70 dark:bg-gray-800/70 hover:bg-slate-600/90 dark:hover:bg-gray-700/90 rounded-full">
					<Icon name="mdi:chevron-right" class="w-5 h-5 text-white" />
				</div>
			</div>
		</div>
	</div>

	<!-- Market Ticker (Dynamic TCMB Integration) -->
	<MarketTicker client:load />
</header>

<script>
	// Script for navigation bar scrolling with mouse
	document.addEventListener('DOMContentLoaded', () => {
		const navContainer = document.getElementById('horizontal-nav') as HTMLElement | null;
		const arrowLeft = document.getElementById('scroll-arrow-left') as HTMLElement | null;
		const arrowRight = document.getElementById('scroll-arrow-right') as HTMLElement | null;
		const fadeLeft = document.getElementById('fade-effect-left') as HTMLElement | null;
		const fadeRight = document.getElementById('fade-effect-right') as HTMLElement | null;


		if (!navContainer || !arrowLeft || !arrowRight || !fadeLeft || !fadeRight) {
			console.warn('Scrollable navigation elements not found.');
			return;
		}

		const SCROLL_AMOUNT = 200; // Pixels to scroll on arrow click

		const updateArrowVisibility = () => {
			// Using a small tolerance to avoid floating point issues
			const tolerance = 1;
			const canScrollLeft = navContainer.scrollLeft > tolerance;
			const canScrollRight = navContainer.scrollLeft < (navContainer.scrollWidth - navContainer.clientWidth - tolerance);

			if (canScrollLeft) {
				arrowLeft.classList.remove('hidden');
				fadeLeft.classList.remove('hidden');
			} else {
				arrowLeft.classList.add('hidden');
				fadeLeft.classList.add('hidden');
			}

			if (canScrollRight) {
				arrowRight.classList.remove('hidden');
				fadeRight.classList.remove('hidden');
			} else {
				arrowRight.classList.add('hidden');
				fadeRight.classList.add('hidden');
			}
		};

		// Initial check
		updateArrowVisibility();

		// Update on scroll (including wheel scroll)
		navContainer.addEventListener('scroll', updateArrowVisibility);

		// Update on window resize (content width might change)
		window.addEventListener('resize', updateArrowVisibility);

		// Mouse wheel scrolling
		navContainer.addEventListener('wheel', (event: WheelEvent) => {
			if (event.deltaY !== 0) {
				event.preventDefault();
				navContainer.scrollLeft += event.deltaY * 2; // Adjust multiplier for scroll speed
				// updateArrowVisibility(); // 'scroll' event will already trigger this
			}
		}, { passive: false });

		// Click listeners for arrows
		arrowLeft.addEventListener('click', () => {
			navContainer.scrollBy({ left: -SCROLL_AMOUNT, behavior: 'smooth' });
		});

		arrowRight.addEventListener('click', () => {
			navContainer.scrollBy({ left: SCROLL_AMOUNT, behavior: 'smooth' });
		});

		// Optional: If nav items are dynamically loaded AFTER DOMContentLoaded,
		// you might need a MutationObserver or a slight delay to re-check visibility.
		// For most Astro setups, this should be fine.
		// Example:
		// setTimeout(updateArrowVisibility, 100); // If items render a bit late
});
</script>

<style>
	/* Hide scrollbar for webkit browsers */
	.scrollbar-hide {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}
	.scrollbar-hide::-webkit-scrollbar {
		display: none;
	}

	.scroll-arrow {
		transition: opacity 0.3s ease-in-out; /* Smooth show/hide */
	}

	/* Tailwind already handles 'hidden' class for display: none */
	/* If you weren't using Tailwind's hidden class, you might do:
	.scroll-arrow.hidden {
		display: none;
		opacity: 0;
		pointer-events: none;
	}
	*/

	/* Ensure arrows are above the scroll content but below any modals/popups if needed */
	.scroll-arrow {
		z-index: 10;
	}

</style>
