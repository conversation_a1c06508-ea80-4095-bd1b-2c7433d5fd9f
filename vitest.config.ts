import { defineConfig } from 'vitest/config';
import { fileURLToPath, URL } from 'node:url';
import { config } from 'dotenv';

// Load environment variables from .env file for tests
config();

export default defineConfig({
	test: {
		globals: true,
		environment: 'jsdom',
		// setupFiles: ['./tests/setup.ts'], // Temporarily disable setup file
		include: ['tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
		exclude: ['node_modules', 'dist', '.astro', 'tests/e2e/**'],
	},
	resolve: {
		alias: {
			'@': fileURLToPath(new URL('./src', import.meta.url)),
			'@/components': fileURLToPath(new URL('./src/components', import.meta.url)),
			'@/features': fileURLToPath(new URL('./src/features', import.meta.url)),
			'@/layouts': fileURLToPath(new URL('./src/layouts', import.meta.url)),
			'@/server': fileURLToPath(new URL('./src/server', import.meta.url)),
			'@/services': fileURLToPath(new URL('./src/services', import.meta.url)),
			'@/stores': fileURLToPath(new URL('./src/stores', import.meta.url)),
			'@/utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
		},
	},
});
