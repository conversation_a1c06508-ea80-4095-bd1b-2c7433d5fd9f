import type { APIRoute } from 'astro';
import { fetchTcmbData } from '../../server/services/verification/tcmb';

export const GET: APIRoute = async () => {
  try {
    const apiKey = import.meta.env.TCMB_API_KEY;

    // If no API key, return empty data (component will use fallback)
    if (!apiKey) {
      return new Response(JSON.stringify({
        status: 'error',
        message: 'TCMB API key not configured',
        data: []
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get recent date range (use past dates that have data)
    const endDate = new Date();
    endDate.setDate(endDate.getDate() - 1); // Yesterday
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 7); // Week ago

    const formatDate = (date: Date): string => {
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}-${month}-${year}`;
    };

    const startDateStr = formatDate(startDate);
    const endDateStr = formatDate(endDate);

    // Fetch multiple series in parallel
    const [usdData, eurData] = await Promise.allSettled([
      fetchTcmbData({
        series: 'TP.DK.USD.A',
        startDate: startDateStr,
        endDate: endDateStr,
      }, apiKey),
      fetchTcmbData({
        series: 'TP.DK.EUR.A',
        startDate: startDateStr,
        endDate: endDateStr,
      }, apiKey)
    ]);

    const marketData = [];

    // Process USD/TRY data
    if (usdData.status === 'fulfilled' && usdData.value.length > 0) {
      // Filter out null values and get the most recent valid data
      const validData = usdData.value.filter(item => item['TP_DK_USD_A'] !== null);
      if (validData.length > 0) {
        const latest = validData[validData.length - 1];
        const previous = validData.length > 1 ? validData[validData.length - 2] : null;

        const currentValue = parseFloat(latest['TP_DK_USD_A'] as string);
        const previousValue = previous ? parseFloat(previous['TP_DK_USD_A'] as string) : currentValue;
        const change = previousValue ? ((currentValue - previousValue) / previousValue) * 100 : 0;

        marketData.push({
          symbol: 'USD/TRY',
          value: currentValue.toFixed(4),
          change: change,
          lastUpdated: latest.Tarih
        });
      }
    }

    // Process EUR/TRY data
    if (eurData.status === 'fulfilled' && eurData.value.length > 0) {
      // Filter out null values and get the most recent valid data
      const validData = eurData.value.filter(item => item['TP_DK_EUR_A'] !== null);
      if (validData.length > 0) {
        const latest = validData[validData.length - 1];
        const previous = validData.length > 1 ? validData[validData.length - 2] : null;

        const currentValue = parseFloat(latest['TP_DK_EUR_A'] as string);
        const previousValue = previous ? parseFloat(previous['TP_DK_EUR_A'] as string) : currentValue;
        const change = previousValue ? ((currentValue - previousValue) / previousValue) * 100 : 0;

        marketData.push({
          symbol: 'EUR/TRY',
          value: currentValue.toFixed(4),
          change: change,
          lastUpdated: latest.Tarih
        });
      }
    }

    return new Response(JSON.stringify({
      status: 'success',
      data: marketData,
      timestamp: new Date().toISOString(),
      formattedTime: new Date().toLocaleString('tr-TR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'Europe/Istanbul'
      })
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
      }
    });

  } catch (error) {
    console.error('Market data API error:', error);

    return new Response(JSON.stringify({
      status: 'error',
      message: 'Failed to fetch market data',
      data: []
    }), {
      status: 200, // Return 200 so component falls back gracefully
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
