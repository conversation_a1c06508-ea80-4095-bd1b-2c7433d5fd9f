/**
 * Stats Service
 *
 * Main service for statistics domain. Provides high-level business logic operations
 * for platform statistics, combining queries and formatters to deliver clean APIs.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import type { DbConnection } from '@/server/db';

// Import queries
import {
	STATS_SQL,
	getPlatformStatistics,
	getEconomistStatistics,
	getCategoryStatistics,
	getVerificationQueueStats,
	getTopPerformingEconomists,
	calculateAccuracyScore,
	calculateTrustScore,
	executeStatsSQL,
} from './queries';

// ============================================================================
// TYPES
// ============================================================================

export interface PlatformStats {
	economistsCount: number;
	predictionsCount: number;
	videosCount: number;
	verifiedCount: number;
	accuracyPercentage: number;
	lastUpdated: string;
}

export interface EconomistStats {
	totalPredictions: number;
	verifiedPredictions: number;
	correctPredictions: number;
	accuracyRate: number;
	pendingPredictions: number;
	avgAccuracy: number;
}

export interface CategoryStats {
	category: string;
	totalPredictions: number;
	verifiedPredictions: number;
	correctPredictions: number;
	accuracyRate: number;
	pendingPredictions: number;
}

export interface VerificationQueueStats {
	totalPending: number;
	overdue: number;
	dueSoon: number;
}

export interface TopEconomistStats {
	id: number;
	name: string;
	accuracyScore: number;
	trustScore: number;
	totalPredictions: number;
	correctPredictions: number;
}

// ============================================================================
// FORMATTERS
// ============================================================================

/**
 * Format platform statistics for API responses
 */
export function formatPlatformStats(stats: any): PlatformStats {
	return {
		economistsCount: stats.economistsCount || 0,
		predictionsCount: stats.predictionsCount || 0,
		videosCount: stats.videosCount || 0,
		verifiedCount: stats.verifiedCount || 0,
		accuracyPercentage: stats.accuracyPercentage || 75, // Default fallback
		lastUpdated: new Date().toISOString(),
	};
}

/**
 * Format economist statistics
 */
export function formatEconomistStats(stats: any): EconomistStats {
	const { total, verified, correct, avgAccuracy } = stats;
	return {
		totalPredictions: total || 0,
		verifiedPredictions: verified || 0,
		correctPredictions: correct || 0,
		accuracyRate: verified > 0 ? Math.round((correct / verified) * 100) : 0,
		pendingPredictions: (total || 0) - (verified || 0),
		avgAccuracy: avgAccuracy || 0,
	};
}

/**
 * Format category statistics
 */
export function formatCategoryStats(categoryStats: any[]): CategoryStats[] {
	return categoryStats.map((stat) => ({
		category: stat.category,
		totalPredictions: stat.total || 0,
		verifiedPredictions: stat.verified || 0,
		correctPredictions: stat.correct || 0,
		accuracyRate: stat.accuracyRate || 0,
		pendingPredictions: (stat.total || 0) - (stat.verified || 0),
	}));
}

/**
 * Format verification queue statistics
 */
export function formatVerificationQueueStats(stats: any): VerificationQueueStats {
	return {
		totalPending: stats.totalPending || 0,
		overdue: stats.overdue || 0,
		dueSoon: stats.dueSoon || 0,
	};
}

/**
 * Format top economist statistics
 */
export function formatTopEconomistStats(economists: any[]): TopEconomistStats[] {
	return economists.map((economist) => ({
		id: economist.id,
		name: economist.name,
		accuracyScore: economist.accuracyScore || 0,
		trustScore: economist.trustScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		correctPredictions: economist.correctPredictions || 0,
	}));
}

// ============================================================================
// HIGH-LEVEL SERVICE FUNCTIONS
// ============================================================================

/**
 * Get platform statistics
 */
export async function getPlatformStats(db: DbConnection): Promise<PlatformStats> {
	const stats = await getPlatformStatistics(db);
	return formatPlatformStats(stats);
}

/**
 * Get economist statistics by ID
 */
export async function getEconomistStatsById(
	db: DbConnection,
	economistId: number
): Promise<EconomistStats> {
	const stats = await getEconomistStatistics(db, economistId);
	return formatEconomistStats(stats);
}

/**
 * Get category statistics
 */
export async function getCategoryStats(db: DbConnection): Promise<CategoryStats[]> {
	const stats = await getCategoryStatistics(db);
	return formatCategoryStats(stats);
}

/**
 * Get verification queue statistics
 */
export async function getVerificationStats(db: DbConnection): Promise<VerificationQueueStats> {
	const stats = await getVerificationQueueStats(db);
	return formatVerificationQueueStats(stats);
}

/**
 * Get top performing economists
 */
export async function getTopEconomistStats(
	db: DbConnection,
	limit = 10
): Promise<TopEconomistStats[]> {
	const economists = await getTopPerformingEconomists(db, limit);
	return formatTopEconomistStats(economists);
}

/**
 * Get comprehensive dashboard statistics
 */
export async function getDashboardStats(db: DbConnection) {
	const [platformStats, categoryStats, verificationStats, topEconomists] = await Promise.all([
		getPlatformStats(db),
		getCategoryStats(db),
		getVerificationStats(db),
		getTopEconomistStats(db, 5),
	]);

	return {
		platform: platformStats,
		categories: categoryStats,
		verification: verificationStats,
		topEconomists,
	};
}

// ============================================================================
// BUILD-TIME FUNCTIONS
// ============================================================================

/**
 * Get platform statistics for build scripts
 */
export function getPlatformStatsBuildData(dbName: string, isLocal = false) {
	const query = STATS_SQL.platformStats;
	const rawStats = executeStatsSQL(query, dbName, isLocal) as any[];

	if (!Array.isArray(rawStats) || rawStats.length === 0) {
		return {
			economistsCount: 0,
			predictionsCount: 0,
			videosCount: 0,
			verifiedCount: 0,
			accuracyPercentage: 75,
		};
	}

	return rawStats[0];
}

/**
 * Get category statistics for build scripts
 */
export function getCategoryStatsBuildData(dbName: string, isLocal = false) {
	const query = STATS_SQL.categoryStats();
	const rawStats = executeStatsSQL(query, dbName, isLocal) as any[];

	if (!Array.isArray(rawStats)) {
		return [];
	}

	return rawStats.map((stat) => ({
		category: stat.category,
		totalPredictions: stat.total || 0,
		verifiedPredictions: stat.verified || 0,
		correctPredictions: stat.correct || 0,
		accuracyRate: stat.accuracyRate || 0,
		pendingPredictions: (stat.total || 0) - (stat.verified || 0),
	}));
}

/**
 * Get top economists statistics for build scripts
 */
export function getTopEconomistStatsBuildData(dbName: string, isLocal = false, limit = 10) {
	const query = STATS_SQL.topEconomists(limit);
	const rawStats = executeStatsSQL(query, dbName, isLocal) as any[];

	if (!Array.isArray(rawStats)) {
		return [];
	}

	return rawStats.map((economist) => ({
		id: economist.id,
		name: economist.name,
		accuracyScore: economist.accuracyScore || 0,
		trustScore: economist.trustScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		correctPredictions: economist.correctPredictions || 0,
	}));
}

// ============================================================================
// STATS SERVICE OBJECT
// ============================================================================

export const StatsService = {
	// High-level functions
	getPlatformStats,
	getEconomistStatsById,
	getCategoryStats,
	getVerificationStats,
	getTopEconomistStats,
	getDashboardStats,

	// Build-time functions
	getPlatformStatsBuildData,
	getCategoryStatsBuildData,
	getTopEconomistStatsBuildData,

	// Query functions (re-exported for convenience)
	getPlatformStatistics,
	getEconomistStatistics,
	getCategoryStatistics,
	getVerificationQueueStats,
	getTopPerformingEconomists,

	// Formatter functions (re-exported for convenience)
	formatPlatformStats,
	formatEconomistStats,
	formatCategoryStats,
	formatVerificationQueueStats,
	formatTopEconomistStats,

	// Calculation helpers (re-exported for convenience)
	calculateAccuracyScore,
	calculateTrustScore,

	// SQL queries (re-exported for convenience)
	STATS_SQL,
	executeStatsSQL,
};

export default StatsService;

// Types are already exported above as interfaces
