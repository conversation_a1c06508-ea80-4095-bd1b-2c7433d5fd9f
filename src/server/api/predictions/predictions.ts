import { Hono } from 'hono';
import { eq, desc, and, count } from 'drizzle-orm';
import { predictions, economists, videos } from '@/server/db/schema';
import { getDbFromContext } from '../../data';

const predictionsRouter = new Hono();

type PredictionStatus = typeof predictions.$inferSelect['status'];

function isValidStatus(status: string): status is PredictionStatus {
    return ['pending', 'verified_correct', 'verified_incorrect', 'expired'].includes(status);
}

type PredictionRow = {
    predictions: typeof predictions.$inferSelect;
    economists: typeof economists.$inferSelect;
    videos: typeof videos.$inferSelect;
};

// GET /api/predictions - Get predictions with filters and pagination
predictionsRouter.get('/', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				success: false,
				error: 'Database connection not available'
			}, 503);
		}

		const page = parseInt(c.req.query('page') || '1');
		const limit = Math.min(parseInt(c.req.query('limit') || '20'), 50); // Max 50 per page
		const offset = (page - 1) * limit;

		const status = c.req.query('status');
		const category = c.req.query('category');
		const economistId = c.req.query('economistId');

		const conditions = [eq(economists.isActive, true)];
		if (status && isValidStatus(status)) {
			conditions.push(eq(predictions.status, status));
		}
		if (category) {
			conditions.push(eq(predictions.category, category));
		}
		if (economistId) {
			const parsedId = parseInt(economistId);
			if (!isNaN(parsedId)) {
				conditions.push(eq(predictions.economistId, parsedId));
			}
		}

		const dataQuery = db
			.select()
			.from(predictions)
			.innerJoin(economists, eq(predictions.economistId, economists.id))
			.innerJoin(videos, eq(predictions.videoId, videos.id))
			.where(and(...conditions))
			.orderBy(desc(predictions.extractedAt))
			.limit(limit)
			.offset(offset);

		const countQuery = db
			.select({ count: count() })
			.from(predictions)
			.innerJoin(economists, eq(predictions.economistId, economists.id))
			.where(and(...conditions));

		const [filteredData, totalCountResult] = await Promise.all([dataQuery, countQuery]);

		const totalCount = totalCountResult[0].count;
		const totalPages = Math.ceil(totalCount / limit);

		const transformedData = filteredData.map((row: PredictionRow) => {
			let targetValue = null;
			let tags: string[] = [];

			try {
				if (row.predictions.targetValue) {
					targetValue = JSON.parse(row.predictions.targetValue);
				}
			} catch {
				console.warn('Failed to parse targetValue JSON:', row.predictions.targetValue);
			}

			try {
				if (row.predictions.tags) {
					tags = JSON.parse(row.predictions.tags);
				}
			} catch {
				console.warn('Failed to parse tags JSON:', row.predictions.tags);
			}

			return {
				id: row.predictions.id,
				title: row.predictions.title,
				description: row.predictions.description,
				category: row.predictions.category,
				predictionType: row.predictions.predictionType,
				targetValue,
				targetDate: row.predictions.targetDate,
				confidenceLevel: row.predictions.confidenceLevel ? Math.round(row.predictions.confidenceLevel * 100) : null,
				status: row.predictions.status,
				accuracyScore: row.predictions.accuracyScore ? Math.round(row.predictions.accuracyScore * 100) : null,
				verifiedAt: row.predictions.verifiedAt,
				tags,
				extractedAt: row.predictions.extractedAt,
				createdAt: row.predictions.createdAt,
				economist: {
					id: row.economists.id,
					name: row.economists.name,
					avatarUrl: row.economists.avatarUrl,
					isVerified: row.economists.isVerified
				},
				video: {
					id: row.videos.id,
					title: row.videos.title,
					youtubeId: row.videos.youtubeVideoId,
					thumbnailUrl: row.videos.thumbnailUrl,
					publishedAt: row.videos.publishedAt,
					youtubeUrl: `https://www.youtube.com/watch?v=${row.videos.youtubeVideoId}`
				}
			};
		});

		const verifiedCount = filteredData.filter((row: PredictionRow) =>
			row.predictions.status === 'verified_correct' || row.predictions.status === 'verified_incorrect'
		).length;

		const accuracyScores = filteredData
			.map((row: PredictionRow) => row.predictions.accuracyScore)
			.filter((score: number | null): score is number => score !== null);

		const avgAccuracy = accuracyScores.length > 0
			? Math.round(accuracyScores.reduce((sum: number, score: number) => sum + score, 0) / accuracyScores.length * 100)
			: null;

		const categories = [...new Set(filteredData.map((row: PredictionRow) => row.predictions.category))];

		return c.json({
			success: true,
			data: transformedData,
			pagination: {
				page,
				limit,
				totalCount,
				totalPages,
				hasNextPage: page < totalPages,
				hasPreviousPage: page > 1
			},
			stats: {
				totalPredictions: totalCount,
				verifiedPredictions: verifiedCount,
				averageAccuracy: avgAccuracy,
				categories
			},
			filters: {
				status,
				category,
				economistId
			},
			message: 'Predictions fetched successfully'
		});
	} catch (error) {
		console.error('Error fetching predictions:', error);

		return c.json({
			success: false,
			error: 'Failed to fetch predictions',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

export default predictionsRouter;
