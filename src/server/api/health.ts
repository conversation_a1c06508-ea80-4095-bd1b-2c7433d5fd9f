import { Hono } from 'hono';
import { getDbFromContext, checkDbHealth } from '../data';

const health = new Hono();

health.get('/', async (c) => {
	try {
		// Get database connection from context
		const db = getDbFromContext(c);

		if (!db) {
			return c.json({
				status: 'error',
				timestamp: new Date().toISOString(),
				db: 'unavailable',
				message: 'Database connection not available'
			}, 503);
		}

		// Check database health
		const isDbHealthy = await checkDbHealth(db);

		if (!isDbHealthy) {
			return c.json({
				status: 'degraded',
				timestamp: new Date().toISOString(),
				db: 'unhealthy',
				message: 'Database is not responding'
			}, 503);
		}

		// Everything is healthy
		return c.json({
			status: 'ok',
			timestamp: new Date().toISOString(),
			db: 'healthy',
			service: 'economist-accuracy-tracker-api'
		}, 200);

	} catch (error) {
		console.error('Health check failed:', error);
		return c.json({
			status: 'error',
			timestamp: new Date().toISOString(),
			db: 'unhealthy',
			message: 'Health check failed',
			error: error instanceof Error ? error.message : 'Unknown error'
		}, 503);
	}
});

export default health;
