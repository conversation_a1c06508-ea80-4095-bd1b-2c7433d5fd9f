/**
 * Economists Service
 *
 * Main service for economists domain. Provides high-level business logic operations
 * for economists, combining queries and formatters to deliver clean APIs.
 */

import type { DbConnection } from '@/server/db';
import type { QueryOptions, ServiceResult } from '../shared/types';

// Import queries from service layer
import {
	findActiveEconomists,
	findEconomistById,
	getEconomistPredictionStats,
	findTopEconomists,
	findEconomistsWithRecentVideos,
	countEconomists,
} from './queries';

// Import formatters
import {
	formatEconomist,
	formatEconomistForHomepage,
	formatEconomistListItem,
	formatEconomistForAPI,
	formatEconomistForSearch,
	formatEconomistForSitemap,
	formatEconomistForFeed,
	addRankingToEconomists,
	type FormattedEconomist,
	type EconomistForBuild,
	type EconomistForHomepage,
	type EconomistListItem,
} from './formatters';

// ============================================================================
// HIGH-LEVEL SERVICE FUNCTIONS
// ============================================================================

/**
 * Get top economists for homepage
 */
export async function getTopEconomists(
	db: DbConnection,
	limit = 6
): Promise<EconomistForHomepage[]> {
	const economists = await findTopEconomists(db, limit);
	return economists.map(formatEconomistForHomepage);
}

/**
 * Get economist by ID with full details
 */
export async function getEconomistById(
	db: DbConnection,
	economistId: number
): Promise<FormattedEconomist | null> {
	const economist = await findEconomistById(db, economistId);
	if (!economist) {
		return null;
	}

	const stats = await getEconomistPredictionStats(db, economistId);
	return formatEconomist(economist, stats);
}

/**
 * Get all economists with pagination and ranking
 */
export async function getEconomistsWithPagination(
	db: DbConnection,
	options: QueryOptions = {}
): Promise<ServiceResult<FormattedEconomist>> {
	const { page = 1, pageSize = 20, orderBy = 'accuracy' } = options;
	const offset = (page - 1) * pageSize;

	const [economists, total] = await Promise.all([
		findActiveEconomists(db, {
			...options,
			limit: pageSize,
			offset,
			orderBy: orderBy as 'accuracy' | 'trust' | 'predictions',
		}),
		countEconomists(db, options),
	]);

	const totalPages = Math.ceil(total / pageSize);

	// Add ranking and format
	const formattedEconomists = economists.map((economist, index) => {
		const rank = offset + index + 1;
		return formatEconomistForAPI(economist, undefined, rank);
	});

	return {
		data: formattedEconomists,
		pagination: {
			page,
			pageSize,
			total,
			totalPages,
			hasNext: page < totalPages,
			hasPrev: page > 1,
		},
	};
}

/**
 * Get economists for listing page
 */
export async function getEconomistsForListing(
	db: DbConnection,
	limit = 50
): Promise<EconomistListItem[]> {
	const economists = await findActiveEconomists(db, { limit, orderBy: 'accuracy' });
	const rankedEconomists = addRankingToEconomists(economists);
	return rankedEconomists.map((economist) => formatEconomistListItem(economist, economist.rank));
}

/**
 * Get economists with recent videos
 */
export async function getEconomistsWithVideos(db: DbConnection, limit = 10) {
	const results = await findEconomistsWithRecentVideos(db, limit);
	return results.map((result) => ({
		...formatEconomistForHomepage(result.economist),
		videoCount: result.videoCount,
	}));
}

/**
 * Get economists for search
 */
export async function getEconomistsForSearch(db: DbConnection) {
	const economists = await findActiveEconomists(db, { limit: 1000 });
	return economists.map(formatEconomistForSearch);
}

/**
 * Get economists for sitemap
 */
export async function getEconomistsForSitemap(db: DbConnection) {
	const economists = await findActiveEconomists(db, { limit: 1000 });
	return economists.map(formatEconomistForSitemap);
}

/**
 * Get economists for RSS feed
 */
export async function getEconomistsForFeed(db: DbConnection, limit = 50) {
	const economists = await findActiveEconomists(db, { limit, orderBy: 'accuracy' });
	return economists.map(formatEconomistForFeed);
}

// ============================================================================
// ECONOMISTS SERVICE OBJECT
// ============================================================================

export const EconomistsService = {
	// High-level functions
	getTopEconomists,
	getEconomistById,
	getEconomistsWithPagination,
	getEconomistsForListing,
	getEconomistsWithVideos,
	getEconomistsForSearch,
	getEconomistsForSitemap,
	getEconomistsForFeed,

	// Query functions (re-exported for convenience)
	findActiveEconomists,
	findEconomistById,
	getEconomistPredictionStats,
	findTopEconomists,
	findEconomistsWithRecentVideos,
	countEconomists,

	// Formatter functions (re-exported for convenience)
	formatEconomist,
	formatEconomistForHomepage,
	formatEconomistListItem,
	formatEconomistForAPI,
	formatEconomistForSearch,
	formatEconomistForSitemap,
	formatEconomistForFeed,
	addRankingToEconomists,
};

export default EconomistsService;

// Re-export types
export type { FormattedEconomist, EconomistForBuild, EconomistForHomepage, EconomistListItem };
