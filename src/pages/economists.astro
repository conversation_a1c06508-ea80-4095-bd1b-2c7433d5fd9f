---
// Enable prerendering for SSG (static generation) by default
export const prerender = true;

import Layout from '@/layouts/Layout.astro';
import { getEntry } from 'astro:content';

// Get build-time data from content collections (fetched via fetch-build-data.ts)
console.log('[Economists Page] Loading build-time data from content collections...');
const cachedData = await getEntry('economists', 'data');

if (!cachedData?.data) {
	throw new Error('[Economists Page] No build-time data found. Run the build process to generate data.');
}

const economistsData = cachedData.data.data;
const { economists: topEconomists } = economistsData;

console.log('[Economists Page] Loaded economists from content collections:', topEconomists.length);
---

<Layout
	title="Ekonomistler - YouTube Economist Trust Score"
	description="YouTube'da en güvenilir ekonomistlerin listesi. <PERSON><PERSON><PERSON> doğruluğu ve güven skorlarına göre sıralanmış ekonomist profilleri."
	ogTitle="En Güvenilir YouTube Ekonomistleri"
	ogDescription="YouTube'da en güvenilir ekonomistlerin listesi. <PERSON><PERSON><PERSON> doğruluğu ve güven skorlarına göre sıralanmış ekonomist profilleri."
>
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
		<!-- Page Header -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
				Ekonomistler
			</h1>
			<p class="text-lg text-gray-600 dark:text-gray-400">
				YouTube'da en güvenilir ekonomistlerin listesi
			</p>
		</div>

		<!-- Top Economists Section -->
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 border border-gray-200 dark:border-gray-700 mb-8">
			<div class="flex items-center justify-between mb-6">
				<h2 class="text-2xl font-bold text-gray-900 dark:text-white">
					En Güvenilir Ekonomistler
				</h2>
				<span class="text-sm text-gray-500 dark:text-gray-400">
					Son güncelleme: {new Date().toLocaleDateString('tr-TR')}
				</span>
			</div>

			{topEconomists.length > 0 ? (
				<div class="space-y-4">
					{topEconomists.map((economist: any, index: number) => (
						<a href={`/economists/${economist.id}`} class="block">
							<div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer">
								<div class="flex items-center space-x-4">
									<div class="flex-shrink-0">
										<span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-200 font-semibold text-sm">
											{index + 1}
										</span>
									</div>
									<div class="flex items-center space-x-3">
										{economist.avatarUrl ? (
											<img
												src={economist.avatarUrl}
												alt={`${economist.name} avatar`}
												class="w-10 h-10 rounded-full object-cover"
											/>
										) : (
											<div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
												<span class="text-gray-600 dark:text-gray-400 text-sm font-medium">
													{economist.name?.charAt(0).toUpperCase()}
												</span>
											</div>
										)}
										<div>
											<h3 class="text-sm font-medium text-gray-900 dark:text-white hover:text-amber-600 dark:hover:text-amber-400 transition-colors">
												{economist.name}
											</h3>
											<p class="text-xs text-gray-500 dark:text-gray-400">
												{economist.totalPredictions || 0} tahmin, {economist.correctPredictions || 0} doğru
											</p>
										</div>
									</div>
								</div>
								<div class="flex items-center space-x-2">
									<div class="text-right">
										<div class="text-lg font-bold text-amber-600 dark:text-amber-400">
											{economist.trustScore ? economist.trustScore.toFixed(1) :
											economist.accuracyScore ? (economist.accuracyScore * 100).toFixed(1) : '0.0'}
										</div>
										<div class="text-xs text-gray-500 dark:text-gray-400">
											{economist.trustScore ? 'Güven Skoru' : 'Doğruluk %'}
										</div>
									</div>
									<!-- Arrow indicator -->
									<div class="flex-shrink-0 ml-2">
										<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
										</svg>
									</div>
								</div>
							</div>
						</a>
					))}
				</div>
			) : (
				<p class="text-gray-600 dark:text-gray-400 text-center py-8">
					Henüz ekonomist verisi bulunmuyor...
				</p>
			)}
		</div>

		<!-- How It Works Section -->
		<section class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 border border-gray-200 dark:border-gray-700 transition-colors">
			<h2 class="text-3xl font-bold text-gray-900 dark:text-white text-center mb-8">
				Nasıl Çalışır?
			</h2>
			<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
				<div class="text-center">
					<div class="w-16 h-16 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-2xl text-amber-600 dark:text-amber-400 font-bold">1</span>
					</div>
					<h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
						Tahminleri Topluyoruz
					</h3>
					<p class="text-gray-600 dark:text-gray-300">
						YouTube ekonomistlerinin videolarından ekonomic tahminlerini çıkarıyoruz.
					</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-2xl text-amber-600 dark:text-amber-400 font-bold">2</span>
					</div>
					<h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
						Gerçeklerle Karşılaştırıyoruz
					</h3>
					<p class="text-gray-600 dark:text-gray-300">
						Tahminlerin doğruluğunu resmi verilerle kontrol ediyoruz.
					</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-2xl text-amber-600 dark:text-amber-400 font-bold">3</span>
					</div>
					<h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
						Güven Skoru Hesaplıyoruz
					</h3>
					<p class="text-gray-600 dark:text-gray-300">
						Her ekonomist için şeffaf bir güven skoru oluşturuyoruz.
					</p>
				</div>
			</div>
		</section>
	</div>
</Layout>
