# YouTube Economist Trust Score Platform

A Turkish-language web platform for tracking YouTube economist reliability scores. This platform analyzes past predictions from YouTube economists to generate trust scores and provide comprehensive analysis to users.

## 🚀 Project Structure

This project uses the following technology stack:

```text
/
├── .ai_context/                 # AI interaction guidelines
├── .astro/                      # Astro build cache
├── .vscode/                     # VS Code settings
├── drizzle/                     # Database migrations
├── public/                      # Static assets
├── scripts/                     # Utility scripts (seed, test setup)
├── src/
│   ├── components/
│   │   ├── astro/              # Astro components
│   │   └── vue/                # Vue.js components (Astro Islands)
│   ├── composables/            # Vue composables
│   ├── layouts/                # Astro layout components
│   ├── lib/                    # Shared libraries and utilities
│   ├── pages/                  # Astro page files
│   │   ├── admin/              # Admin pages
│   │   └── api/                # API endpoints
│   ├── server/                 # Backend code (Cloudflare Workers/Hono)
│   │   ├── api/                # API route handlers
│   │   ├── auth/               # Authentication middleware
│   │   ├── db/                 # Database schema and tables
│   │   └── services/           # Business logic services
│   ├── types/                  # TypeScript type definitions
│   └── utils/                  # Utility functions
├── tests/                      # Test files
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end tests
├── astro.config.mjs            # Astro configuration
├── drizzle.config.ts           # Database ORM configuration
├── package.json                # Dependencies and scripts
├── playwright.config.ts        # E2E testing configuration
├── tailwind.config.mjs         # Tailwind CSS configuration
├── tsconfig.json               # TypeScript configuration
├── vitest.config.ts            # Unit testing configuration
└── wrangler.toml               # Cloudflare Workers configuration
```

## 🛠️ Tech Stack

- **Frontend**: Astro + Vue.js + Tailwind CSS + shadcn-vue
- **Backend**: Hono (Cloudflare Workers)
- **Database**: Cloudflare D1 + Drizzle ORM (D1 for all environments)
- **Authentication**: JWT (Jose) + bcryptjs
- **UI Components**: Radix Vue + Lucide Vue + CVA
- **Deployment**: Cloudflare Workers + Wrangler
- **Testing**: Vitest + Playwright + jsdom
- **Code Quality**: ESLint + Prettier + TypeScript

## �️ Architecture

### Static Site Generation (SSG) Strategy

This project uses a **hybrid SSG approach** that combines the performance benefits of static generation with database-driven content:

#### **Build-Time Data Fetching**
- Data is fetched directly from the appropriate D1 database using `wrangler d1` commands
- Each environment builds with its own database (dev D1 for dev builds, staging D1 for staging builds, etc.)
- Data is fetched fresh right before the build process starts

#### **Environment-Specific Builds**
```bash
# Build with development database
npm run build:dev

# Build with staging database
npm run build:staging

# Build with production database
npm run build:prod

# Build with local database
npm run build:local
```

#### **Benefits**
- ✅ **Always Fresh Data**: Builds always use the latest data from the target database
- ✅ **Environment Isolation**: Each build uses the correct database for its environment
- ✅ **No Cache Management**: No need to manage cached files or worry about stale data
- ✅ **True SSG**: All pages are statically generated with real database content

📖 **Learn more**: See [`docs/SSG_STRATEGY.md`](./docs/SSG_STRATEGY.md) for detailed implementation details.

## �🌍 Environment Overview

This application supports four distinct environments:

1. **Local** - Local development using Cloudflare D1 (via wrangler dev)
   - Database: Cloudflare D1 (SQLite compatible, used for all environments)
   - Command: `npm run dev` (Astro) or `npm run cf:dev` (Workers simulation)
   - Purpose: Local development and testing

2. **Development** - Deployed to Cloudflare Workers with D1 database
   - Database: Cloudflare D1 (`economist-tracker-dev`)
   - Command: `npm run deploy:dev`
   - URL: `https://economist-accuracy-tracker-tr-web-app-dev.cihangir-yildirim-2d0.workers.dev`

3. **Staging** - Deployed to Cloudflare Workers with separate D1 database
   - Database: Cloudflare D1 (`economist-tracker-staging`)
   - Command: `npm run deploy:staging`
   - URL: `https://economist-accuracy-tracker-tr-web-app-staging.cihangir-yildirim-2d0.workers.dev`

4. **Production** - Deployed to Cloudflare Workers with production D1 database
   - Database: Cloudflare D1 (`economist-tracker-prod`)
   - Command: `npm run deploy`
   - URL: `https://economist-tracker.workers.dev`

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

### Development

| Command | Action |
| :--- | :--- |
| `npm install` | Install dependencies |
| `npm run dev` | Start local dev server at `http://localhost:4321` |

### Building

**🏗️ Environment-Specific Builds with Fresh Database Data**

Each build command fetches fresh data from the corresponding D1 database:

| Command | Action |
| :--- | :--- |
| `npm run build:prod` | Fetch from `economist-tracker-prod` D1 database → Build for production |
| `npm run build:staging` | Fetch from `economist-tracker-staging` D1 database → Build for staging |
| `npm run build:dev` | Fetch from `economist-tracker-dev` D1 database → Build for development |
| `npm run build:local` | Fetch from `economist-tracker-local` D1 database → Build for local testing |
| `npm run build` | Fetch from local D1 database → Build (default) |
| `npm run fetch:data` | Fetch fresh data from database (without building) |

### Testing

| Command | Action |
| :--- | :--- |
| `npm run test` | Run all tests (Unit, Integration, E2E) |
| `npm run test:all` | Run all test suites via test runner script |
| `npm run test:watch` | Run tests in watch mode |
| `npm run test:ui` | Run tests with Vitest UI |
| `npm run test:unit` | Run unit tests |
| `npm run test:integration` | Run integration tests |
| `npm run test:e2e` | Run E2E tests |
| `npm run test:e2e:ui` | Run E2E tests with Playwright UI |

### Linting and Formatting

| Command | Action |
| :--- | :--- |
| `npm run lint` | Lint the codebase |
| `npm run lint:fix` | Fix linting errors |
| `npm run format` | Format the codebase |
| `npm run format:check` | Check formatting |

### Database

| Command | Action |
| :--- | :--- |
| `npm run db:generate` | Generate database migrations |
| `npm run db:migrate:local` | Migrate local database |
| `npm run db:migrate:dev` | Migrate development database |
| `npm run db:migrate:staging` | Migrate staging database |
| `npm run db:migrate:prod` | Migrate production database |
| `npm run db:studio` | Open Drizzle Studio for development DB |
| `npm run db:studio:prod` | Open Drizzle Studio for production DB |
| `npm run db:seed:local` | Seed local database |
| `npm run db:seed:dev` | Seed development database |
| `npm run db:seed:staging` | Seed staging database |
| `npm run db:test-users:local` | Create E2E test users in local DB |
| `npm run db:test-users:dev` | Create E2E test users in development DB |
| `npm run db:test-users:staging` | Create E2E test users in staging DB |

### Cloudflare Setup

| Command | Action |
| :--- | :--- |
| `npm run setup:cloudflare` | Setup Cloudflare resources (D1, KV) for all environments |
| `npm run setup:cloudflare:dev-staging` | Setup Cloudflare resources for dev and staging |
| `npm run setup:db` | Create D1 databases for all environments |
| `npm run setup:validate-env` | Validate environment variables |

### Deployment

**✨ Foolproof Environment-Specific Deployment**

Each deployment command automatically:
1. Fetches fresh data from the correct D1 database for that environment
2. Builds the site with that data
3. Runs database migrations if needed
4. Deploys to the correct Cloudflare Workers environment
5. Performs health checks

| Command | Action |
| :--- | :--- |
| `npm run deploy` | **Production Deploy**: Fetch from `economist-tracker-prod` D1 → Build → Deploy to production |
| `npm run deploy:staging` | **Staging Deploy**: Fetch from `economist-tracker-staging` D1 → Build → Deploy to staging |
| `npm run deploy:dev` | **Development Deploy**: Fetch from `economist-tracker-dev` D1 → Build → Deploy to development |

**🔒 Environment Safety**: Each command is completely isolated - there's no risk of accidentally using the wrong database data for the wrong environment.

## 📜 Full npm Scripts Reference

Below is a complete list of npm scripts defined in `package.json`, with explanations and usage notes for each:

| Script | Description |
|--------|-------------|
| `dev` | Start Astro dev server locally (http://localhost:4321). |
| `build` | Build the site with live database data (production). |
| `build:cached` | Build the site with cached data (no database required). |
| `cache:data` | Cache database data for static builds. |
| `preview` | Preview the production build locally. |
| `astro` | Run Astro CLI commands directly. |
| `test` | Run all tests in watch mode (Vitest). |
| `test:ui` | Run Vitest with interactive UI. |
| `test:run` | Run all tests once (Vitest). |
| `test:unit` | Run only unit tests. |
| `test:integration` | Run only integration tests. |
| `test:e2e` | Create E2E test users, then run Playwright E2E tests. |
| `test:e2e:ui` | Create E2E test users, then run Playwright E2E tests with UI. |
| `test:auth` | Run both unit and integration tests (auth-related). |
| `test:all` | Run all test suites (unit, integration, E2E). |
| `lint` | Run ESLint on all code. |
| `lint:fix` | Auto-fix lint issues. |
| `format` | Format codebase with Prettier. |
| `format:check` | Check code formatting with Prettier. |
| `db:generate` | Generate Drizzle ORM migration files. |
| `db:migrate:local` | Apply database migrations to local database. |
| `db:migrate:dev` | Apply database migrations to development database. |
| `db:migrate:staging` | Apply database migrations to staging database. |
| `db:migrate:prod` | Apply database migrations to production database. |
| `db:studio` | Open Drizzle Studio (visual DB explorer). |
| `db:studio:prod` | Open Drizzle Studio in production. |
| `db:seed` | Seed the database with sample data (local/dev). |
| `db:seed:prod` | Seed the database in production. |
| `db:test-users` | Create E2E test users in the database. |
| `cf:setup` | Set up Cloudflare resources (D1, KV, etc). |
| `cf:validate` | Validate Cloudflare deployment environment. |
| `cf:deploy:dev` | Deploy to Cloudflare Workers (development env). |
| `cf:deploy:staging` | Deploy to Cloudflare Workers (staging env). |
| `cf:deploy:prod` | Deploy to Cloudflare Workers (production env). |
| `cf:d1:create` | Create Cloudflare D1 databases. |
| `cf:d1:migrate:dev` | Apply D1 migrations to development DB. |
| `cf:d1:migrate:staging` | Apply D1 migrations to staging DB. |
| `cf:d1:migrate:prod` | Apply D1 migrations to production DB. |
| `deploy` | Deploy to production via custom deploy script. |
| `deploy:staging` | Deploy to staging via custom deploy script. |
| `deploy:dev` | Deploy to development via custom deploy script. |
| `cf:dev` | Start Cloudflare Workers dev server locally. |

**Notes:**
- Scripts with `:prod` or `:staging` suffixes are for production or staging environments. Use them only when deploying or managing those environments.
- Database and Cloudflare scripts are for advanced setup, migration, or troubleshooting. See inline comments in `package.json` or ask a maintainer if unsure.
- Some scripts (e.g., `db:test-users`, `cf:setup`, `cf:d1:create`) are for one-time or rare setup tasks.
- The `deploy` scripts use custom logic and may include additional steps (see `scripts/deploy.ts`).

For more details, see the comments in `package.json` or the `scripts/` directory.

---

## 🏗️ Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd test-folder
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   npm run db:generate        # Generate migration files
   npm run db:migrate:local   # Apply migrations to local database
   npm run db:seed:local      # Seed local database with sample data
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:4321`

## 🚀 Cloudflare Workers Deployment

For production deployment to Cloudflare Workers:

1. **Install Wrangler CLI**
   ```bash
   npm install -g wrangler
   wrangler login
   ```

2. **Set up Cloudflare resources**
   ```bash
   npm run cf:setup  # Creates D1 databases and KV namespaces
   ```

3. **Configure environment variables**
   Set sensitive variables in Cloudflare Dashboard:
   - `JWT_SECRET`: Secure random string
   - `OPENAI_API_KEY`: If using OpenAI features
   - Other API keys as needed

4. **Validate environment**
   ```bash
   npm run cf:validate  # Checks configuration
   ```

5. **Deploy**
   ```bash
   npm run deploy:staging  # Deploy to staging
   npm run deploy          # Deploy to production
   ```

See [docs/CLOUDFLARE_DEPLOYMENT.md](docs/CLOUDFLARE_DEPLOYMENT.md) for detailed deployment guide.

### Additional Development Tools

- **Database Studio**: `npm run db:studio` - Visual database explorer
- **Test UI**: `npm run test:ui` - Interactive test runner
- **E2E Tests**: `npm run test:e2e:ui` - Visual E2E test runner
- **Cloudflare Dev**: `npm run cf:dev` - Local Cloudflare Workers environment

**If you are using GitHub Copilot**:

- Add `.ai_context` folder to `chat.instructionsFilesLocations` settings so the coding agent knows where the coding guidelines are.
  - Details: [GitHub Copilot Custom Instructions](https://docs.github.com/en/copilot/customizing-copilot/adding-repository-custom-instructions-for-github-copilot)

## 📁 Key Features

- **Economist Tracking**: Monitor YouTube economist predictions and accuracy
- **Trust Score System**: Algorithm-based reliability scoring
- **Automated Verification**: AI-powered prediction outcome verification
- **Admin Dashboard**: Content management and verification tools
- **User Authentication**: Secure login/registration system
- **Responsive Design**: Mobile-first UI with dark/light theme
- **Real-time Data**: Live updates and dynamic content
- **Turkish Language**: Fully localized Turkish interface

## 📝 Contributing

This project follows specific coding standards and architectural patterns. Please review the guideline documents in the `.ai_context/` folder before contributing.

### Code Quality Standards
- **TypeScript**: Strict type checking enabled
- **ESLint**: Configured for Astro, Vue, and TypeScript
- **Prettier**: Automatic code formatting
- **Testing**: Unit, integration, and E2E test coverage required
- **Accessibility**: WCAG 2.1 compliance with jsx-a11y rules

## 🔗 Links

- [Astro Documentation](https://docs.astro.build)
- [Vue.js Documentation](https://vuejs.org)
- [Tailwind CSS Documentation](https://tailwindcss.com)
- [shadcn-vue Documentation](https://www.shadcn-vue.com)
- [Drizzle ORM Documentation](https://orm.drizzle.team)
- [Hono Documentation](https://hono.dev)
- [Cloudflare Workers Documentation](https://developers.cloudflare.com/workers/)
- [Vitest Documentation](https://vitest.dev)
- [Playwright Documentation](https://playwright.dev)