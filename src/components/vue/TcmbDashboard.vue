<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white">
        TCMB Exchange Rates
      </h3>
      <div class="flex items-center space-x-2">
        <span class="text-xs text-gray-500 dark:text-gray-400">
          {{ lastUpdated ? `Updated: ${formatTime(lastUpdated)}` : 'Loading...' }}
        </span>
        <button
          class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50"
          :disabled="isLoading"
          @click="refreshData"
        >
          <svg
            :class="{ 'animate-spin': isLoading }"
            class="h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>
    </div>

    <div v-if="isLoading && !data" class="animate-pulse">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div class="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    </div>

    <div v-else-if="error" class="text-center py-6">
      <p class="text-red-600 dark:text-red-400">{{ error }}</p>
      <button
        class="mt-2 text-sm text-blue-600 dark:text-blue-400 hover:underline"
        @click="refreshData"
      >
        Try again
      </button>
    </div>

    <div v-else-if="data" class="space-y-4">
      <!-- Current Rates -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-blue-900 dark:text-blue-100">USD/TRY</p>
              <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {{ data.statistics.latest?.toFixed(4) || 'N/A' }}
              </p>
            </div>
            <div class="text-right">
              <p class="text-sm text-blue-700 dark:text-blue-300">
                {{ data.statistics.change > 0 ? '+' : '' }}{{ data.statistics.change.toFixed(4) }}
              </p>
              <p class="text-xs" :class="data.statistics.changePercent >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ data.statistics.changePercent >= 0 ? '↗' : '↘' }} {{ Math.abs(data.statistics.changePercent).toFixed(2) }}%
              </p>
            </div>
          </div>
        </div>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p class="text-gray-600 dark:text-gray-400">Min ({{ days }}d)</p>
              <p class="font-semibold text-gray-900 dark:text-white">{{ data.statistics.min.toFixed(4) }}</p>
            </div>
            <div>
              <p class="text-gray-600 dark:text-gray-400">Max ({{ days }}d)</p>
              <p class="font-semibold text-gray-900 dark:text-white">{{ data.statistics.max.toFixed(4) }}</p>
            </div>
            <div>
              <p class="text-gray-600 dark:text-gray-400">Average</p>
              <p class="font-semibold text-gray-900 dark:text-white">{{ data.statistics.average.toFixed(4) }}</p>
            </div>
            <div>
              <p class="text-gray-600 dark:text-gray-400">Data Points</p>
              <p class="font-semibold text-gray-900 dark:text-white">{{ data.statistics.count }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Chart -->
      <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
          Exchange Rate Trend (Last {{ days }} days)
        </h4>
        <div class="h-48 flex items-center justify-center text-gray-500 dark:text-gray-400">
          <!-- Simple text-based chart placeholder -->
          <div class="text-center">
            <p class="text-sm">Chart visualization would go here</p>
            <p class="text-xs mt-1">Showing {{ data.data.length }} data points</p>
            <p class="text-xs">{{ formatDate(data.dateRange.start) }} - {{ formatDate(data.dateRange.end) }}</p>
          </div>
        </div>
      </div>

      <!-- Cache Info -->
      <div v-if="cached" class="flex items-center text-xs text-amber-600 dark:text-amber-400">
        <svg class="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
        </svg>
        Cached data (refreshes every 4 hours)
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

const isLoading = ref(false);
const error = ref<string | null>(null);
const data = ref<any>(null);
const lastUpdated = ref<string | null>(null);
const cached = ref(false);
const days = ref(30);

interface TcmbResponse {
  status: string;
  data: any;
  lastUpdated: string;
  cached: boolean;
  message?: string;
}

const fetchTcmbData = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const response = await fetch(`/api/tcmb-data?series=TP.DK.USD.A&days=${days.value}`);
    const result = await response.json() as TcmbResponse;

    if (result.status === 'success') {
      data.value = result.data;
      lastUpdated.value = result.lastUpdated;
      cached.value = result.cached;
    } else {
      error.value = result.message || 'Failed to fetch TCMB data';
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Network error';
  } finally {
    isLoading.value = false;
  }
};

const refreshData = () => {
  fetchTcmbData();
};

const formatTime = (isoString: string) => {
  return new Date(isoString).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatDate = (dateString: string) => {
  const [day, month, year] = dateString.split('-');
  return new Date(parseInt(year), parseInt(month) - 1, parseInt(day)).toLocaleDateString();
};

onMounted(() => {
  fetchTcmbData();

  // Auto-refresh every 30 minutes
  const interval = window.setInterval(fetchTcmbData, 30 * 60 * 1000);

  // Cleanup on unmount
  return () => window.clearInterval(interval);
});
</script>
