---
// Enable prerendering for SSG (static generation) by default
export const prerender = true;

import Layout from '@/layouts/Layout.astro';
import { getEntry } from 'astro:content';
import HomePageCarousel from '@/components/vue/HomePageCarousel.vue';
import CuratedArticles from '@/components/vue/CuratedArticles.vue';

// Utility function for formatting numbers
const formatNumber = (num: number): string => {
	if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
	if (num >= 1000) return `${(num / 1000).toFixed(0)}K`;
	return num.toString();
};

// Get build-time data from content collections (fetched via fetch-build-data.ts)
console.log('[Homepage] Loading build-time data from content collections...');
const cachedData = await getEntry('homepage', 'data');

if (!cachedData?.data) {
	throw new Error('[Homepage] No build-time data found. Run the build process to generate data.');
}

const homepageData = cachedData.data.data;
const { topEconomists, platformStats, recentVideos } = homepageData;

// Log the data source for debugging
console.log(`[Homepage] Loaded data from environment: ${cachedData.data.environment}`);
console.log(`[Homepage] Data fetched at: ${cachedData.data.fetchedAt}`);
console.log(`[Homepage] Top economists: ${topEconomists.length}`);
console.log(`[Homepage] Recent videos: ${recentVideos?.length || 0}`);
console.log(`[Homepage] Raw cachedData:`, JSON.stringify(cachedData.data.data.topEconomists[0], null, 2));
console.log(`[Homepage] First economist:`, topEconomists[0]);
---

<Layout title="Ana Sayfa - YouTube Economist Trust Score">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
		<!-- Main Content Grid -->
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
			<!-- Video Carousel - Main Feature (2/3 width) -->
			<div class="lg:col-span-2">
				<HomePageCarousel client:load />
			</div>

			<!-- Monthly Rankings - Secondary Feature (1/3 width) -->
			<div class="lg:col-span-1">
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 border border-gray-200 dark:border-gray-700 transition-colors">
					<div class="flex items-center justify-between mb-6">
						<h2 class="text-2xl font-bold text-gray-900 dark:text-white">Ayın Ekonomistleri</h2>
						<span class="text-sm text-gray-500 dark:text-gray-400">Son 30 Gün</span>
					</div>

					{topEconomists.length > 0 ? (
						<div class="space-y-3">
							{topEconomists.map((economist: any, index: number) => (
								<a href={`/economists/${economist.id}`} class="block">
									<div class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-650 transition-colors cursor-pointer">
										<!-- Rank Badge -->
										<div class="flex-shrink-0">
											<div class={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
												index === 0 ? 'bg-yellow-500 text-yellow-900' :
												index === 1 ? 'bg-gray-400 text-gray-900' :
												index === 2 ? 'bg-orange-600 text-orange-100' :
												'bg-gray-600 text-gray-300'
											}`}>
												{index + 1}
											</div>
										</div>

										<!-- Economist Info -->
										<div class="flex-1 min-w-0">
											<h3 class="text-gray-900 dark:text-white font-semibold truncate hover:text-amber-600 dark:hover:text-amber-400 transition-colors">{economist.name}</h3>
											<div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
												<span>{economist.totalPredictions} tahmin</span>
												{economist.youtubeChannelName && (
													<span>{economist.youtubeChannelName}</span>
												)}
											</div>
										</div>

									<!-- Trust Score -->
									<div class="flex-shrink-0 text-right">
										<div class={`text-lg font-bold ${
											economist.accuracyScore >= 0.8 ? 'text-green-400' :
											economist.accuracyScore >= 0.6 ? 'text-yellow-400' :
											'text-red-400'
										}`}>
											{Math.round(economist.accuracyScore * 100)}%
										</div>
										<div class="text-xs text-gray-500 dark:text-gray-400">güven skoru</div>
									</div>

									<!-- Trend Indicator -->
									<div class="flex-shrink-0">
										<div class="w-6 h-6 flex items-center justify-center text-green-400">
											<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
											</svg>
										</div>
									</div>
								</div>
								</a>
							))}
						</div>
					) : (
						<div class="text-center py-8">
							<p class="text-gray-600 dark:text-gray-400">Henüz sıralama bulunmuyor...</p>
						</div>
					)}

					<div class="mt-6 text-center">
						<a
							href="/economists"
							class="inline-flex items-center px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-lg text-sm transition-colors"
						>
							Tüm Ekonomistleri Gör
							<svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
							</svg>
						</a>
					</div>
				</div>
			</div>
		</div>

		<!-- Secondary Features -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Curated Articles -->
			<div>
				<CuratedArticles client:visible />
			</div>
			<!-- Platform Stats - Generated at build time -->
			<div>
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700 transition-colors">
					<h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Platform İstatistikleri</h2>

					<div class="grid grid-cols-2 gap-4">
						<div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg transition-colors">
							<div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
								{formatNumber(platformStats.economistsCount)}
							</div>
							<div class="text-sm text-gray-600 dark:text-gray-400">Ekonomist</div>
						</div>

						<div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg transition-colors">
							<div class="text-2xl font-bold text-green-600 dark:text-green-400">
								{formatNumber(platformStats.predictionsCount)}
							</div>
							<div class="text-sm text-gray-600 dark:text-gray-400">Tahmin</div>
						</div>

						<div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg transition-colors">
							<div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
								{formatNumber(platformStats.videosCount)}
							</div>
							<div class="text-sm text-gray-600 dark:text-gray-400">Video</div>
						</div>

						<div class="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg transition-colors">
							<div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
								{platformStats.accuracyPercentage}%
							</div>
							<div class="text-sm text-gray-600 dark:text-gray-400">Doğruluk</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</Layout>
