# AI Partner Memories

This document captures the user's established preferences, patterns, and architectural decisions to improve AI collaboration efficiency over time. It serves as a living document that should be updated when new patterns emerge.

## Architecture & Code Organization

**DRY Principle & Consistency:**

- Consolidate duplicate code (SQL queries, formatters, data access logic) into reusable, centralized functions
- Augment existing scripts rather than creating new ones when similar functionality exists
- Follow existing patterns (especially SSG patterns) rather than introducing new approaches
- Maintain architectural consistency and eliminate temporary workarounds

**Service Layer Organization:**

- Prefer domain-driven service architecture with clear separation of concerns
- Organize services in focused folders (e.g., `src/server/services/articles`) with smaller files
- Eliminate unnecessary organizational layers between build scripts and data access
- Build scripts should import directly from repositories for simplicity

**File Structure:**

- Use folder-organized approach over monolithic structures
- Keep build scripts short with clear methodology explanations
- Prefer path aliases (@/) over relative imports for maintainability
- Configure path aliases in all relevant config files to eliminate IDE errors

## Development Workflow

**Static Site Generation (SSG):**

- Prefer SSG over SSR for better performance and SEO
- Use `prerender = true` for economist and article detail pages
- Make data available during build time through fetch-build-data.ts script
- Consider hybrid SSG/SSR for latest N articles (static) + older articles (SSR)

**Testing & Quality:**

- Expect comprehensive test coverage (unit, integration, e2e)
- Use UI interactions in Playwright E2E tests, not direct API calls
- Fix lint warnings with appropriate ignore comments rather than leaving them
- Verify component functionality visually before making changes

**Error Handling:**

- Display '--' instead of fallback values when API data fetching fails
- Avoid showing potentially misleading information to users

## Tooling & Dependencies

**Database & Deployment:**

- Use drizzle-kit for database migrations (not wrangler d1)
- Use wrangler exclusively for deployments
- Use npm commands for migration and deployment operations
- Prefer incremental migration naming (avoid reusing numbers)

**Package Management:**

- Remove unused dependencies (e.g., questioned shadcn-vue usage)
- Plan to use shadcn for future visual elements like graphs

## Collaboration Patterns

- Uses .ai_context folder for project collaboration context
- Expects strict adherence to guidelines in AI interaction instructions
- **Memories Integration:** This memories file should be understood and applied during every human-AI pair programming task to ensure consistency and reduce repetitive questions
- Allows autonomous cleaning decisions but expects approval for major architectural changes
- Values complete migrations over partial implementations to avoid leftover code
