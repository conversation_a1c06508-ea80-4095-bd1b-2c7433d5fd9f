import { describe, it, expect, beforeAll, afterAll } from 'vitest';

describe('Economists Pages Integration', () => {
  const baseUrl = 'http://localhost:4321'; // Use the dev server that's already running

  beforeAll(async () => {
    // Wait for the dev server to be ready
    let retries = 0;
    const maxRetries = 10;

    while (retries < maxRetries) {
      try {
        const response = await fetch(`${baseUrl}/`);
        if (response.ok) {
          console.log('Dev server is ready for integration tests');
          break;
        }
      } catch {
        retries++;
        if (retries === maxRetries) {
          throw new Error('Dev server is not available for integration tests');
        }
        await new Promise(resolve => globalThis.setTimeout(resolve, 1000));
      }
    }
  });

  afterAll(async () => {
    // No cleanup needed since we're using the existing dev server
  });

  describe('Economists Listing Page', () => {
    it('should render economists listing page successfully', async () => {
      const response = await fetch(`${baseUrl}/economists`);

      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('text/html');

      const html = await response.text();

      // Check for essential page elements
      expect(html).toContain('<title>');
      expect(html).toContain('Ekonomistler');
      expect(html).toContain('YouTube Economist Trust Score');
    });

    it('should display economist cards with proper structure', async () => {
      const response = await fetch(`${baseUrl}/economists`);
      const html = await response.text();

      // Check for economist card elements
      expect(html).toContain('href="/economists/'); // Links to detail pages
      expect(html).toContain('tahmin'); // Prediction count text
      expect(html).toContain('doğru'); // Correct predictions text
    });

    it('should have proper meta tags for SEO', async () => {
      const response = await fetch(`${baseUrl}/economists`);
      const html = await response.text();

      expect(html).toContain('<meta name="description"');
      expect(html).toContain('<meta property="og:title"');
      expect(html).toContain('<meta property="og:description"');
    });

    it('should be accessible and have proper semantic structure', async () => {
      const response = await fetch(`${baseUrl}/economists`);
      const html = await response.text();

      // Check for semantic HTML elements
      expect(html).toContain('<main');
      expect(html).toContain('<h1');
      expect(html).toContain('role='); // ARIA roles
    });
  });

  describe('Economist Detail Pages', () => {
    it('should render economist detail page for valid ID', async () => {
      const response = await fetch(`${baseUrl}/economists/1`);

      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('text/html');

      const html = await response.text();

      // Check for essential page elements
      expect(html).toContain('<title>');
      expect(html).toContain('Ekonomist Profili');
    });

    it('should contain static content sections for economist data', async () => {
      const response = await fetch(`${baseUrl}/economists/1`);
      const html = await response.text();

      // Check for static content sections (SSG)
      expect(html).toContain('Son Tahminler');
      expect(html).toContain('Son Videolar');
      expect(html).toContain('Performans İstatistikleri');
      expect(html).toContain('Detaylı Metrikler');
    });

    it('should have proper static content structure', async () => {
      const response = await fetch(`${baseUrl}/economists/1`);
      const html = await response.text();

      // Check for static content structure (SSG)
      expect(html).toContain('Ekonomist Profili');
      expect(html).toContain('Performans İstatistikleri');
      expect(html).toContain('Güven Skoru');
      expect(html).toContain('Dr. Mehmet Ekonomi');
    });

    it('should have static content and proper SSG structure', async () => {
      const response = await fetch(`${baseUrl}/economists/1`);
      const html = await response.text();

      // Check for static content (SSG)
      expect(html).toContain('Ekonomist Profili');
      expect(html).toContain('Güven Skoru');
      expect(html).toContain('Son Tahminler');
      expect(html).toContain('Son Videolar');
    });

    it('should have responsive layout structure', async () => {
      const response = await fetch(`${baseUrl}/economists/1`);
      const html = await response.text();

      // Check for responsive grid classes
      expect(html).toContain('grid-cols-1');
      expect(html).toContain('lg:grid-cols-3');
      expect(html).toContain('max-w-7xl');
      expect(html).toContain('mx-auto');
    });

    it('should handle multiple economist IDs correctly', async () => {
      const economistIds = [1, 2, 8]; // Test a few different IDs

      for (const id of economistIds) {
        const response = await fetch(`${baseUrl}/economists/${id}`);

        expect(response.status).toBe(200);

        const html = await response.text();
        expect(html).toContain('Ekonomist Profili');
      }
    });
  });

  describe('Navigation and Links', () => {
    it('should have working navigation from economists listing to detail pages', async () => {
      const listingResponse = await fetch(`${baseUrl}/economists`);
      const listingHtml = await listingResponse.text();

      // Extract economist links from the listing page
      const linkMatches = listingHtml.match(/href="\/economists\/(\d+)"/g);
      expect(linkMatches).toBeTruthy();
      expect(linkMatches!.length).toBeGreaterThan(0);

      // Test the first link
      const firstLinkMatch = linkMatches![0].match(/href="(\/economists\/\d+)"/);
      expect(firstLinkMatch).toBeTruthy();

      const detailPageUrl = firstLinkMatch![1];
      const detailResponse = await fetch(`${baseUrl}${detailPageUrl}`);

      expect(detailResponse.status).toBe(200);
    });

    it('should have working navigation from homepage to economists pages', async () => {
      const homepageResponse = await fetch(`${baseUrl}/`);
      const homepageHtml = await homepageResponse.text();

      // Check for links to economist detail pages from homepage
      const economistLinks = homepageHtml.match(/href="\/economists\/\d+"/g);

      if (economistLinks && economistLinks.length > 0) {
        // Test the first economist link from homepage
        const firstLink = economistLinks[0].match(/href="(\/economists\/\d+)"/);
        expect(firstLink).toBeTruthy();

        const detailPageUrl = firstLink![1];
        const detailResponse = await fetch(`${baseUrl}${detailPageUrl}`);

        expect(detailResponse.status).toBe(200);
      }
    });
  });

  describe('Performance and Caching', () => {
    it('should serve static files with proper caching headers', async () => {
      const response = await fetch(`${baseUrl}/economists`);

      // Check for static generation indicators
      expect(response.status).toBe(200);

      // The response should be fast since it's pre-generated
      const startTime = Date.now();
      await fetch(`${baseUrl}/economists`);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should be very fast for static content
    });

    it('should serve economist detail pages as static content', async () => {
      const response = await fetch(`${baseUrl}/economists/1`);

      expect(response.status).toBe(200);

      // Multiple requests should be consistently fast
      const times: number[] = [];

      for (let i = 0; i < 3; i++) {
        const startTime = Date.now();
        await fetch(`${baseUrl}/economists/1`);
        const endTime = Date.now();
        times.push(endTime - startTime);
      }

      // All requests should be fast (static content)
      times.forEach(time => {
        expect(time).toBeLessThan(500);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent economist IDs gracefully', async () => {
      const response = await fetch(`${baseUrl}/economists/99999`);

      // Should either redirect to 404 or return 404 status
      expect([302, 404]).toContain(response.status);
    });

    it('should handle invalid economist ID formats', async () => {
      const invalidIds = ['abc', '1.5', '-1', '0'];

      for (const invalidId of invalidIds) {
        const response = await fetch(`${baseUrl}/economists/${invalidId}`);

        // Should either redirect or return 404
        expect([302, 404]).toContain(response.status);
      }
    });
  });
});
