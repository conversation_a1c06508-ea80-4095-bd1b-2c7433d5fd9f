---
// Enable prerendering for SSG (static generation) by default
export const prerender = true;

import Layout from '@/layouts/Layout.astro';
import { getEntry } from 'astro:content';
import { getDb } from '@/server/db/context';
import { getFormattedArticleBySlug } from '@/server/services';

// Generate static paths for recent articles at build time (following economists pattern)
export async function getStaticPaths() {
	try {
		// Get build-time data from content collections (fetched via fetch-build-data.ts)
		console.log('[Article Detail] Loading build-time data from content collections...');
		const cachedData = await getEntry('articles', 'data');

		if (!cachedData?.data) {
			console.error(
				'[Article Detail] No build-time data found. Run the build process to generate data.'
			);
			return [];
		}

		const articlesData = cachedData.data.data;
		const { articles } = articlesData;

		console.log('[Article Detail] Generating static paths for articles:', articles.length);

		return articles.map((article: any) => ({
			params: { slug: article.slug },
			props: { article },
		}));
	} catch (error) {
		console.error('[Article Detail] Error generating static paths:', error);
		return [];
	}
}

// Get the article slug from the URL parameters
const { slug } = Astro.params;
const { article } = Astro.props as { article?: any };

if (!slug) {
	// @ts-ignore - Astro allows return statements in top-level script
	return Astro.redirect('/404');
}

// For SSR fallback (older articles not in static generation)
let articleData = article;
if (!articleData) {
	try {
		const db = await getDb();
		if (db) {
			console.log(`[Article Detail SSR] Fetching article dynamically: ${slug}`);
			articleData = await getFormattedArticleBySlug(db, slug);
		}
	} catch (error) {
		console.error('[Article Detail SSR] Error fetching article:', error);
	}
}

// Handle 404
if (!articleData) {
	// @ts-ignore - Astro allows return statements in top-level script
	return Astro.redirect('/404');
}

// Format published date
const publishedDate = new Date(articleData.publishedAt);
const formattedDate = publishedDate.toLocaleDateString('tr-TR', {
	year: 'numeric',
	month: 'long',
	day: 'numeric',
});

// Determine if this is a static page (SSG) or dynamic (SSR)
const isStatic = !!article;

// SEO metadata
const pageTitle = `${articleData.title} - Ekonomist Güven Skoru`;
const pageDescription = articleData.metaDescription || articleData.excerpt;
---

<Layout title={pageTitle} description={pageDescription} ogImage={articleData.imageUrl}>
	<main class="min-h-screen bg-gray-50 dark:bg-gray-900">
		<!-- Article Header -->
		<article class="max-w-4xl mx-auto px-4 py-8">
			<!-- Breadcrumb -->
			<nav class="mb-6">
				<ol class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
					<li><a href="/" class="hover:text-blue-600 dark:hover:text-blue-400">Ana Sayfa</a></li>
					<li class="before:content-['/'] before:mx-2">
						<a href="/articles" class="hover:text-blue-600 dark:hover:text-blue-400">Makaleler</a>
					</li>
					<li class="before:content-['/'] before:mx-2 text-gray-900 dark:text-white truncate">
						{articleData.title}
					</li>
				</ol>
			</nav>

			<!-- Article Meta -->
			<header class="mb-8">
				<div class="flex items-center gap-2 mb-4">
					<span
						class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
					>
						{articleData.category}
					</span>
					{
						isStatic && (
							<span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
								Statik
							</span>
						)
					}
				</div>

				<h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4 leading-tight">
					{articleData.title}
				</h1>

				<p class="text-xl text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
					{articleData.excerpt}
				</p>

				<div
					class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700 pb-6"
				>
					<div class="flex items-center space-x-4">
						<span class="font-medium text-gray-900 dark:text-white">{articleData.author}</span>
						<span>{formattedDate}</span>
						<span>{articleData.readTime}</span>
					</div>

					{
						articleData.tags.length > 0 && (
							<div class="flex items-center space-x-2">
								{articleData.tags.map((tag: string) => (
									<span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
										#{tag}
									</span>
								))}
							</div>
						)
					}
				</div>
			</header>

			<!-- Article Image -->
			{
				articleData.imageUrl && (
					<div class="mb-8">
						<img
							src={articleData.imageUrl}
							alt={articleData.title}
							class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
							loading="lazy"
						/>
					</div>
				)
			}

			<!-- Article Content -->
			<div class="prose prose-lg max-w-none dark:prose-invert">
				{
					articleData.content ? (
						<div set:html={articleData.content} />
					) : (
						<div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
							<p class="text-yellow-800 dark:text-yellow-200">
								<strong>Özet:</strong> {articleData.excerpt}
							</p>
							<p class="text-yellow-700 dark:text-yellow-300 mt-2 text-sm">
								Bu makale henüz tam içerik ile güncellenmemiştir. Şu anda sadece özet mevcut.
							</p>
						</div>
					)
				}
			</div>

			<!-- Article Footer -->
			<footer class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
				<div class="flex items-center justify-between">
					<div class="text-sm text-gray-500 dark:text-gray-400">
						<p>
							Yazar: <span class="font-medium text-gray-900 dark:text-white"
								>{articleData.author}</span
							>
						</p>
						<p>
							Kategori: <span class="font-medium text-gray-900 dark:text-white"
								>{articleData.category}</span
							>
						</p>
					</div>

					<div class="flex items-center space-x-4">
						<!-- Social sharing buttons could go here -->
						<button
							onclick="window.history.back()"
							class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:focus:ring-offset-gray-900"
						>
							← Geri Dön
						</button>
					</div>
				</div>
			</footer>
		</article>
	</main>
</Layout>

<style>
	.prose {
		@apply text-gray-900 dark:text-gray-100;
	}

	.prose h1,
	.prose h2,
	.prose h3,
	.prose h4,
	.prose h5,
	.prose h6 {
		@apply text-gray-900 dark:text-white;
	}

	.prose h2 {
		@apply text-2xl font-bold mt-8 mb-4;
	}

	.prose h3 {
		@apply text-xl font-semibold mt-6 mb-3;
	}

	.prose p {
		@apply mb-4 leading-relaxed text-gray-700 dark:text-gray-300;
	}

	.prose ul,
	.prose ol {
		@apply mb-4 pl-6;
	}

	.prose li {
		@apply mb-2 text-gray-700 dark:text-gray-300;
	}

	.prose blockquote {
		@apply border-l-4 border-blue-500 dark:border-blue-400 pl-4 italic text-gray-700 dark:text-gray-300 my-6 bg-gray-50 dark:bg-gray-800/50 py-2;
	}

	.prose a {
		@apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline;
	}

	.prose strong {
		@apply font-semibold text-gray-900 dark:text-white;
	}

	.prose code {
		@apply bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 px-2 py-1 rounded text-sm font-mono;
	}

	.prose pre {
		@apply bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-4 rounded-lg overflow-x-auto;
	}

	.prose table {
		@apply border-collapse border border-gray-300 dark:border-gray-600 w-full;
	}

	.prose th,
	.prose td {
		@apply border border-gray-300 dark:border-gray-600 px-4 py-2 text-left;
	}

	.prose th {
		@apply bg-gray-100 dark:bg-gray-800 font-semibold text-gray-900 dark:text-white;
	}

	.prose td {
		@apply text-gray-700 dark:text-gray-300;
	}

	.prose img {
		@apply rounded-lg shadow-md;
	}

	.prose hr {
		@apply border-gray-300 dark:border-gray-600 my-8;
	}
</style>

<script>
	// Add any client-side functionality here
	console.log('Article page loaded');
</script>
