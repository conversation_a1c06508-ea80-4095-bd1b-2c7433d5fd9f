# build output
dist/
# generated types
.astro/

# dependencies
node_modules/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# environment variables
.env
.env.production
.env.local

# database
drizzle/
*.db
*.sqlite

# testing
coverage/
test-results/
playwright-report/
playwright/.cache/

# cloudflare
.wrangler/

# macOS-specific files
.DS_Store

# jetbrains setting folder
.idea/

# vscode settings folder
.vscode/

# Build-time data files (generated by fetch-build-data.ts)
src/content/**/data.json

# AI context files (except .ai_context/)
GEMINI.md
.augment/