import { Hono } from 'hono';
import { getDbFromContext } from '../data';
import { getRecentVideos } from '../services';

const videosRouter = new Hono();

// GET /api/videos/recent - Get recent videos
videosRouter.get('/recent', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {		return c.json({
			status: 'error',
			message: 'Database connection not available'
		}, 503);
		}

		// Extract limit from query params
		const limitParam = c.req.query('limit');
		const limit = limitParam ? parseInt(limitParam, 10) : 6;

		console.log('[API videos/recent] Fetching recent videos with limit:', limit);
		const videos = await getRecentVideos(db, limit);

		console.log('[API videos/recent] Successfully fetched videos:', videos.length);
		return c.json({
			status: 'success',
			data: videos,
			message: 'Recent videos fetched successfully'
		});
	} catch (error) {
		console.error('Error fetching recent videos:', error);
		return c.json({
			status: 'error',
			message: 'Failed to fetch videos',
			details: [error instanceof Error ? error.message : 'Unknown error']
		}, 500);
	}
});

export default videosRouter;
