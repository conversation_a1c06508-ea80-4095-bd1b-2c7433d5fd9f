#!/usr/bin/env tsx

/**
 * D1 Database Creation Script
 *
 * Simple script to create D1 databases for all environments
 *
 * Usage:
 *   npm run cf:d1:create
 *   tsx scripts/create-d1-databases.ts
 */

import { execSync } from 'child_process';

function log(message: string): void {
	console.log(`🔷 ${message}`);
}

function executeCommand(command: string): string {
	try {
		log(`Executing: ${command}`);
		const result = execSync(command, { encoding: 'utf-8', stdio: 'inherit' });
		return result;
	} catch (error) {
		console.error(`❌ Command failed: ${command}`);
		throw error;
	}
}

function createDatabases(): void {
	const databases = [
		'economist-tracker-dev',
		'economist-tracker-staging',
		'economist-tracker-prod'
	];

	log('Creating D1 databases...');

	for (const db of databases) {
		try {
			log(`Creating database: ${db}`);
			executeCommand(`wrangler d1 create ${db}`);
			log(`✅ Created: ${db}`);
		} catch (error) {
			if (String(error).includes('already exists')) {
				log(`⚠️  Database ${db} already exists`);
			} else {
				console.error(`❌ Failed to create ${db}:`, error);
			}
		}
	}

	log('✅ Database creation completed!');
	log('');
	log('📋 Next steps:');
	log('1. Copy the database IDs from the output above');
	log('2. Update your wrangler.toml file with the correct database IDs');
	log('3. Run migrations: npm run db:migrate:dev');
}

createDatabases();
