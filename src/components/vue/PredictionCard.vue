<template>
	<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow">
		<div class="flex items-start justify-between">
			<div class="flex-1">
				<!-- Prediction Title and Category -->
				<div class="flex items-center gap-2 mb-2">
					<h4 class="text-lg font-semibold text-gray-900 dark:text-white">
						{{ prediction.title }}
					</h4>
					<span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
						{{ categoryText }}
					</span>
				</div>

				<!-- Prediction Description -->
				<p class="text-gray-600 dark:text-gray-300 mb-4 text-sm leading-relaxed">
					{{ prediction.description }}
				</p>

				<!-- Target Value Display -->
				<div v-if="prediction.targetValue && Object.keys(prediction.targetValue).length > 0" class="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
					<div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Hedef:</div>
					<div class="text-sm text-gray-600 dark:text-gray-400">
						<span v-if="prediction.predictionType === 'range' && prediction.targetValue.min && prediction.targetValue.max">
							{{ prediction.targetValue.min }}% - {{ prediction.targetValue.max }}%
						</span>
						<span v-else-if="prediction.predictionType === 'target' && prediction.targetValue.target">
							{{ prediction.targetValue.target }}
						</span>
						<span v-else-if="prediction.predictionType === 'increase' && prediction.targetValue.threshold">
							{{ prediction.targetValue.threshold }}% artış
						</span>
						<span v-else-if="prediction.predictionType === 'decrease' && prediction.targetValue.threshold">
							{{ prediction.targetValue.threshold }}% düşüş
						</span>
						<span v-else>Hedef belirlendi</span>
					</div>
				</div>

				<!-- Economist and Video Info -->
				<div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
					<div class="flex items-center space-x-2">
						<a :href="`/economists/${prediction.economist.id}`" class="flex items-center space-x-2 hover:text-amber-600 dark:hover:text-amber-400 transition-colors group">
							<img :src="prediction.economist.avatarUrl" :alt="prediction.economist.name" class="w-6 h-6 rounded-full group-hover:ring-2 group-hover:ring-amber-500 group-hover:ring-offset-1 transition-all">
							<span class="flex items-center gap-1">
								{{ prediction.economist.name }}
								<Icon v-if="prediction.economist.isVerified" icon="mdi:check-decagram" class="w-4 h-4 text-amber-500" />
							</span>
						</a>
					</div>
					<span>•</span>
					<span>{{ formatDate(prediction.targetDate) }} hedefi</span>
					<span v-if="prediction.confidenceLevel">•</span>
					<span v-if="prediction.confidenceLevel" class="flex items-center gap-1">
						<Icon icon="mdi:lightning-bolt" class="w-4 h-4" />
						{{ prediction.confidenceLevel }}% güven
					</span>
				</div>

				<!-- Tags -->
				<div v-if="prediction.tags && prediction.tags.length > 0" class="flex flex-wrap gap-1 mb-3">
					<span v-for="tag in prediction.tags" :key="tag" class="inline-flex px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
						#{{ tag }}
					</span>
				</div>
			</div>

			<!-- Status Badge and Accuracy Score -->
			<div class="flex-shrink-0 ml-4 text-right">
				<div class="flex flex-col items-end space-y-2">
					<span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full" :class="statusColor">
						{{ statusText }}
					</span>
					<div v-if="prediction.accuracyScore !== null" class="flex items-center space-x-1">
						<span class="text-xs text-gray-500 dark:text-gray-400">Doğruluk:</span>
						<span class="inline-flex px-2 py-1 text-xs font-bold rounded-full" :class="accuracyColor">
							{{ prediction.accuracyScore }}%
						</span>
					</div>
				</div>
			</div>
		</div>

		<!-- Verification Details -->
		<div v-if="prediction.verifiedAt" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
			<div class="flex items-center justify-between text-sm">
				<span class="text-gray-600 dark:text-gray-400">
					Doğrulandı: {{ formatDate(prediction.verifiedAt) }}
				</span>
				<a
					:href="prediction.video.youtubeUrl"
					target="_blank"
					rel="noopener noreferrer"
					class="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-200 font-medium"
				>
					Videoyu İzle →
				</a>
			</div>
		</div>

		<!-- Video Link -->
		<div v-else class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
			<div class="flex items-center justify-between text-sm">
				<span class="text-gray-600 dark:text-gray-400">
					{{ formatDate(prediction.extractedAt) }} tarihinde çıkarıldı
				</span>
				<a
					:href="prediction.video.youtubeUrl"
					target="_blank"
					rel="noopener noreferrer"
					class="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-200 font-medium"
				>
					Kaynak Video →
				</a>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

interface Economist {
	id: number;
	name: string;
	avatarUrl: string;
	isVerified: boolean;
}

interface Video {
	id: number;
	title: string;
	youtubeId: string;
	thumbnailUrl: string;
	publishedAt: string;
	youtubeUrl: string;
}

interface Prediction {
	id: number;
	title: string;
	description: string;
	category: string;
	predictionType: string;
	targetValue: any;
	targetDate: string;
	confidenceLevel: number | null;
	status: 'pending' | 'verified_correct' | 'verified_incorrect' | 'expired';
	accuracyScore: number | null;
	verifiedAt: string | null;
	tags: string[];
	extractedAt: string;
	createdAt: string;
	economist: Economist;
	video: Video;
}

interface Props {
	prediction: Prediction;
}

const props = defineProps<Props>();

const categoryText = computed(() => {
	const categoryMap: { [key: string]: string } = {
		'interest_rate': 'Faiz Oranı',
		'exchange_rate': 'Döviz Kuru',
		'stock_market': 'Borsa',
		'inflation': 'Enflasyon',
		'gdp': 'GSYİH',
		'real_estate': 'Emlak'
	};
	return categoryMap[props.prediction.category] || props.prediction.category;
});

const statusColor = computed(() => {
	switch (props.prediction.status) {
		case 'verified_correct':
			return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
		case 'verified_incorrect':
			return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
		case 'expired':
			return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
		case 'pending':
		default:
			return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
	}
});

const statusText = computed(() => {
	switch (props.prediction.status) {
		case 'verified_correct':
			return 'Doğru';
		case 'verified_incorrect':
			return 'Yanlış';
		case 'expired':
			return 'Süresi Doldu';
		case 'pending':
		default:
			return 'Beklemede';
	}
});

const accuracyColor = computed(() => {
	const score = props.prediction.accuracyScore;
	if (score === null) return '';
	if (score >= 80) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
	if (score >= 60) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
	return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
});

const formatDate = (dateString: string) => {
	const date = new Date(dateString);
	return new Intl.DateTimeFormat('tr-TR', {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
	}).format(date);
};
</script>
