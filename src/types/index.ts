import type { users } from '@/server/db/tables/users';

// User types based on database schema
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  username?: string;
  password: string;
}

// User role types
export type UserRole =
  | 'ROLE_ANONYMOUS'
  | 'ROLE_GUEST'
  | 'ROLE_FREE_USER'
  | 'ROLE_PAID_USER'
  | 'ROLE_CONTRIBUTOR'
  | 'ROLE_MODERATOR'
  | 'ROLE_EDITOR'
  | 'ROLE_ANALYST'
  | 'ROLE_ADMIN';

export interface AuthContext {
  user: User | null;
  roles: UserRole[];
  isAuthenticated: boolean;
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
  pagination?: PaginationInfo;
}

// Data types for frontend components
export interface TopEconomist {
  id: number;
  name: string;
  avatarUrl: string | null;
  accuracyScore: number;
  trustScore: number;
  totalPredictions: number;
  correctPredictions: number;
  bio: string | null;
  youtubeChannelName: string | null;
  subscriberCount: number | null;
}

export interface PlatformStats {
  economistsCount:  number;
  predictionsCount: number;
  videosCount: number;
  verifiedCount: number;
  accuracyPercentage: number;
}