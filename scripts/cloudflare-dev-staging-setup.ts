#!/usr/bin/env tsx

/**
 * Cloudflare Development & Staging Setup Script
 *
 * This script sets up the required Cloudflare resources for development and staging deployment:
 * - Creates D1 databases for development and staging environments
 * - Creates KV namespaces for session storage
 * - Runs database migrations on each environment
 * - Seeds the databases with initial data
 * - Prepares the production configuration for future use (without creating resources)
 *
 * Run with: npm run cf:setup-dev-staging
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

interface CloudflareResource {
	environment: string;
	d1DatabaseName: string;
	kvNamespaceName: string;
	d1DatabaseId?: string;
	kvNamespaceId?: string;
}

const environments: CloudflareResource[] = [
	{
		environment: 'development',
		d1DatabaseName: 'economist-tracker-dev',
		kvNamespaceName: 'session-dev',
	},
	{
		environment: 'staging',
		d1DatabaseName: 'economist-tracker-staging',
		kvNamespaceName: 'session-staging',
	},
	// Production configuration prepared for future use (not created yet)
	// {
	// 	environment: 'production',
	// 	d1DatabaseName: 'economist-tracker-prod',
	// 	kvNamespaceName: 'session-prod',
	// },
];

function executeCommand(command: string): string {
	console.log(`🔧 Executing: ${command}`);
	try {
		const result = execSync(command, { encoding: 'utf-8', stdio: 'pipe' });
		return result.trim();
	} catch (error: unknown) {
		console.error(`❌ Command failed: ${command}`);
		const err = error as { message: string; stdout?: string; stderr?: string };
		console.error(`Error: ${err.message}`);
		if (err.stdout) console.error(`stdout: ${err.stdout}`);
		if (err.stderr) console.error(`stderr: ${err.stderr}`);
		throw error;
	}
}

function extractIdFromOutput(output: string, type: 'database' | 'namespace'): string {
	const lines = output.split('\n');

	if (type === 'database') {
		// Look for "database_id" pattern in D1 create output
		const dbIdLine = lines.find(line => line.includes('database_id'));
		if (dbIdLine) {
			const match = dbIdLine.match(/database_id\s*=\s*"([^"]+)"/);
			if (match) return match[1];
		}

		// Alternative: look for UUID pattern
		const uuidPattern = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/i;
		for (const line of lines) {
			const match = line.match(uuidPattern);
			if (match) return match[0];
		}
	} else {
		// Look for "id" pattern in KV create output
		const idLine = lines.find(line => line.includes('id'));
		if (idLine) {
			const match = idLine.match(/id\s*=\s*"([^"]+)"/);
			if (match) return match[1];
		}

		// Alternative: look for alphanumeric ID pattern
		const idPattern = /[a-f0-9]{32}/i;
		for (const line of lines) {
			const match = line.match(idPattern);
			if (match) return match[0];
		}
	}

	throw new Error(`Could not extract ${type} ID from output: ${output}`);
}

function updateWranglerConfig(environments: CloudflareResource[]) {
	const wranglerPath = join(process.cwd(), 'wrangler.toml');
	let content = readFileSync(wranglerPath, 'utf-8');

	for (const env of environments) {
		if (env.d1DatabaseId) {
			const oldPattern = new RegExp(`(\\[env\\.${env.environment}\\.d1_databases\\][\\s\\S]*?database_id\\s*=\\s*")[^"]*(")`);
			content = content.replace(oldPattern, `$1${env.d1DatabaseId}$2`);
		}

		if (env.kvNamespaceId) {
			const oldPattern = new RegExp(`(\\[\\[env\\.${env.environment}\\.kv_namespaces\\]][\\s\\S]*?id\\s*=\\s*")[^"]*(")`);
			content = content.replace(oldPattern, `$1${env.kvNamespaceId}$2`);
		}
	}

	writeFileSync(wranglerPath, content);
	console.log('✅ Updated wrangler.toml with new resource IDs');
}

async function setupCloudflareResources() {
	console.log('🚀 Starting Cloudflare development & staging setup...\n');

	// Check if user is logged in to Cloudflare
	try {
		executeCommand('npx wrangler whoami');
		console.log('✅ Cloudflare authentication verified\n');
	} catch {
		console.error('❌ Not logged in to Cloudflare. Please run: npx wrangler login');
		process.exit(1);
	}

	// Create resources for development and staging environments only
	for (const env of environments) {
		console.log(`📦 Setting up ${env.environment} environment...\n`);

		// Create D1 Database
		console.log(`Creating D1 database: ${env.d1DatabaseName}`);
		try {
			const d1Output = executeCommand(`npx wrangler d1 create ${env.d1DatabaseName}`);
			env.d1DatabaseId = extractIdFromOutput(d1Output, 'database');
			console.log(`✅ D1 database created with ID: ${env.d1DatabaseId}`);
		} catch {
			console.log(`⚠️  D1 database ${env.d1DatabaseName} might already exist, checking...`);
			try {
				const listOutput = executeCommand('npx wrangler d1 list');
				console.log('📋 Existing D1 databases:', listOutput);
			} catch {
				console.error('❌ Failed to list D1 databases');
			}
		}

		// Create KV Namespace
		console.log(`Creating KV namespace: ${env.kvNamespaceName}`);
		try {
			const kvOutput = executeCommand(`npx wrangler kv namespace create ${env.kvNamespaceName}`);
			env.kvNamespaceId = extractIdFromOutput(kvOutput, 'namespace');
			console.log(`✅ KV namespace created with ID: ${env.kvNamespaceId}`);
		} catch {
			console.log(`⚠️  KV namespace ${env.kvNamespaceName} might already exist, checking...`);
			try {
				const listOutput = executeCommand('npx wrangler kv namespace list');
				console.log('📋 Existing KV namespaces:', listOutput);
			} catch {
				console.error('❌ Failed to list KV namespaces');
			}
		}

		console.log(`✅ ${env.environment} environment setup complete\n`);
	}

	// Update wrangler.toml with the new IDs
	if (environments.some(env => env.d1DatabaseId || env.kvNamespaceId)) {
		updateWranglerConfig(environments);
	}

	console.log('🎉 Cloudflare development & staging setup completed!');
	console.log('\n📋 Next steps:');
	console.log('1. Review the updated wrangler.toml file');
	console.log('2. Run database migrations: npm run db:migrate:dev && npm run db:migrate:staging');
	console.log('3. Seed databases: npm run cf:seed-dev-staging');
	console.log('4. Deploy to staging: npm run deploy:staging');
	console.log('5. Deploy to development: npm run deploy:dev');
	console.log('\n💡 Production deployment will be set up when the platform is ready for public release.');
}

// Run the setup
setupCloudflareResources().catch((error) => {
	console.error('❌ Setup failed:', error.message);
	process.exit(1);
});
