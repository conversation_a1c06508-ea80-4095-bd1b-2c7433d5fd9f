/**
 * Services Index
 *
 * Main aggregator for all domain services. Provides clean, organized exports
 * for all service functionality across the application.
 */

// ============================================================================
// DOMAIN SERVICES
// ============================================================================

// Articles Service
export { default as ArticlesService } from './articles';
export * from './articles';

// Economists Service
export { default as EconomistsService } from './economists';
export * from './economists';

// Videos Service
export { default as VideosService } from './videos';
export * from './videos';

// Stats Service
export { default as StatsService } from './stats';
export * from './stats';

// Predictions Service
export { default as PredictionsService } from './predictions';
export * from './predictions';

// ============================================================================
// SHARED UTILITIES
// ============================================================================

export * from './shared/formatters';
export * from './shared/types';

// ============================================================================
// CONVENIENCE EXPORTS
// ============================================================================

// High-level service functions for common use cases
export {
	// Articles
	getCuratedArticles,
	getRecentArticlesForSSG,
	getFormattedArticleBySlug,
	getArticlesForHomepage,
} from './articles';

export {
	// Economists
	getTopEconomists,
	getEconomistById,
	getEconomistsForListing,
} from './economists';

export {
	// Videos
	getRecentVideos,
	getVideoById,
	getVideosByEconomist,
} from './videos';

export {
	// Stats
	getPlatformStats,
	getEconomistStatsById,
	getCategoryStats,
	getDashboardStats,
} from './stats';

export {
	// Predictions
	getAllPredictions,
	getPredictionById,
	getVerificationQueue,
} from './predictions';

// ============================================================================
// BUILD-TIME FUNCTIONS (moved to repositories for direct access)
// ============================================================================

// Note: Build-time functions have been moved to repositories for better performance
// and direct database access. Import them directly from:
// - src/server/repositories/articles.ts
// - src/server/repositories/economists.ts
// - src/server/repositories/videos.ts
// - src/server/repositories/stats.ts
// - src/server/services/predictions/index.ts (temporary)

// ============================================================================
// SQL QUERIES FOR BUILD SCRIPTS (moved to repositories)
// ============================================================================

// Note: SQL constants have been moved to repositories for single source of truth
// Import them directly from:
// - src/server/repositories/articles.ts (ARTICLES_SQL)
// - src/server/repositories/economists.ts (ECONOMISTS_SQL)
// - src/server/repositories/videos.ts (VIDEOS_SQL)
// - src/server/repositories/stats.ts (STATS_SQL)
// - src/server/services/predictions/queries.ts (PREDICTIONS_SQL)

// ============================================================================
// TYPE EXPORTS
// ============================================================================

// Re-export all types for convenience
export type {
	// Shared types
	BaseService,
	PaginationOptions,
	PaginationResult,
	FilterOptions,
	SortOptions,
	QueryOptions,
	ServiceResult,
	SingleServiceResult,
	ServiceResultWithMeta,
} from './shared/types';

export type { FormattedArticle, ArticleForBuild, ArticleListItem } from './articles';

export type {
	FormattedEconomist,
	EconomistForHomepage,
	EconomistListItem,
	EconomistForBuild,
} from './economists';

export type {
	FormattedVideo,
	VideoForHomepage,
	VideoWithPredictions,
	VideoForBuild,
} from './videos';

export type {
	PlatformStats,
	EconomistStats,
	CategoryStats,
	VerificationQueueStats,
	TopEconomistStats,
} from './stats';

export type { PredictionData, PredictionListItem, PredictionStatus } from './predictions';
