/**
 * Economists Repository
 *
 * Data access layer for economists. Contains both Drizzle ORM queries and raw SQL.
 * This is the single source of truth for all economist database operations.
 */

import type { DbConnection } from '@/server/db';
import { economists, predictions } from '@/server/db';
import { eq, desc, and, count } from 'drizzle-orm';
import { execSync } from 'child_process';

// ============================================================================
// RAW SQL QUERIES (for build scripts and external tools)
// ============================================================================

export const ECONOMISTS_SQL = {
	all: `
    SELECT
      id,
      name,
      youtube_channel_name as youtubeChannelName,
      total_predictions as totalPredictions,
      correct_predictions as correctPredictions,
      accuracy_score as accuracyScore,
      trust_score as trustScore,
      avatar_url as avatarUrl,
      bio,
      subscriber_count as subscriberCount
    FROM economists
    WHERE is_active = 1
    ORDER BY accuracy_score DESC, total_predictions DESC
  `
		.replace(/\s+/g, ' ')
		.trim(),

	topForHomepage: (limit = 10) =>
		`
    SELECT
      id,
      name,
      youtube_channel_name as youtubeChannelName,
      total_predictions as totalPredictions,
      accuracy_score as accuracyScore
    FROM economists
    WHERE is_active = 1
    ORDER BY accuracy_score DESC, total_predictions DESC
    LIMIT ${limit}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	byId: (id: number) =>
		`
    SELECT
      id,
      name,
      youtube_channel_name as youtubeChannelName,
      total_predictions as totalPredictions,
      correct_predictions as correctPredictions,
      accuracy_score as accuracyScore,
      trust_score as trustScore,
      avatar_url as avatarUrl,
      bio,
      subscriber_count as subscriberCount,
      is_active as isActive,
      created_at as createdAt,
      updated_at as updatedAt
    FROM economists
    WHERE id = ${id} AND is_active = 1
  `
			.replace(/\s+/g, ' ')
			.trim(),
} as const;

// ============================================================================
// DRIZZLE ORM QUERIES
// ============================================================================

/**
 * Find active economists with optional filtering and ordering
 */
export async function findActiveEconomists(
	db: DbConnection,
	options: {
		limit?: number;
		orderBy?: 'accuracy' | 'trust' | 'predictions';
	} = {}
) {
	const { limit, orderBy = 'accuracy' } = options;

	// Determine ordering columns
	let orderColumns;
	switch (orderBy) {
		case 'accuracy':
			orderColumns = [desc(economists.accuracyScore), desc(economists.trustScore)];
			break;
		case 'trust':
			orderColumns = [desc(economists.trustScore), desc(economists.accuracyScore)];
			break;
		case 'predictions':
			orderColumns = [desc(economists.totalPredictions), desc(economists.accuracyScore)];
			break;
		default:
			orderColumns = [desc(economists.accuracyScore), desc(economists.trustScore)];
	}

	// Build the complete query
	const baseQuery = db
		.select()
		.from(economists)
		.where(eq(economists.isActive, true))
		.orderBy(...orderColumns);

	// Apply limit if specified
	const finalQuery = limit ? baseQuery.limit(limit) : baseQuery;

	return await finalQuery;
}

/**
 * Find economist by ID
 */
export async function findEconomistById(db: DbConnection, id: number) {
	const result = await db
		.select()
		.from(economists)
		.where(and(eq(economists.id, id), eq(economists.isActive, true)))
		.limit(1);

	return result[0] || null;
}

/**
 * Get economist prediction statistics
 */
export async function getEconomistPredictionStats(db: DbConnection, economistId: number) {
	const stats = await db
		.select({
			total: count(),
			verified: count(predictions.verifiedAt),
			correct: count(and(predictions.verifiedAt, eq(predictions.status, 'verified_correct'))),
		})
		.from(predictions)
		.where(eq(predictions.economistId, economistId));

	return stats[0] || { total: 0, verified: 0, correct: 0 };
}

/**
 * Get top economists for homepage (optimized query)
 */
export async function findTopEconomists(db: DbConnection, limit = 10) {
	return await db
		.select({
			id: economists.id,
			name: economists.name,
			youtubeChannelName: economists.youtubeChannelName,
			totalPredictions: economists.totalPredictions,
			accuracyScore: economists.accuracyScore,
			avatarUrl: economists.avatarUrl,
		})
		.from(economists)
		.where(eq(economists.isActive, true))
		.orderBy(desc(economists.accuracyScore), desc(economists.totalPredictions))
		.limit(limit);
}

// ============================================================================
// RAW SQL EXECUTION (for build scripts)
// ============================================================================

/**
 * Execute raw SQL query for economists (used by build scripts)
 */
export function executeEconomistSQL(query: string, dbName: string, isLocal = false): unknown {
	const localFlag = isLocal ? '--local' : '--remote';
	const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

	try {
		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Economist SQL execution failed:', error);
		return [];
	}
}

/**
 * Get all economists using raw SQL (for build-time)
 */
export function getAllEconomistsSQL(dbName: string, isLocal = false) {
	return executeEconomistSQL(ECONOMISTS_SQL.all, dbName, isLocal);
}

/**
 * Get top economists using raw SQL (for build-time)
 */
export function getTopEconomistsSQL(dbName: string, isLocal = false, limit = 10) {
	return executeEconomistSQL(ECONOMISTS_SQL.topForHomepage(limit), dbName, isLocal);
}

/**
 * Get economist by ID using raw SQL (for build-time)
 */
export function getEconomistByIdSQL(dbName: string, id: number, isLocal = false) {
	return executeEconomistSQL(ECONOMISTS_SQL.byId(id), dbName, isLocal);
}

// ============================================================================
// FORMATTED BUILD DATA FUNCTIONS
// ============================================================================

/**
 * Get economists data for build scripts (formatted)
 */
export function getEconomistsBuildData(dbName: string, isLocal = false, limit?: number) {
	const query = limit ? ECONOMISTS_SQL.topForHomepage(limit) : ECONOMISTS_SQL.all;
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const rawEconomists = executeEconomistSQL(query, dbName, isLocal) as any[];

	if (!Array.isArray(rawEconomists)) {
		return { economists: [], totalCount: 0, averageAccuracy: 0 };
	}

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const formattedEconomists = rawEconomists.map((economist: any, index) => ({
		...economist,
		rank: index + 1,
		accuracyScore: economist.accuracyScore || 0,
		trustScore: economist.trustScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		correctPredictions: economist.correctPredictions || 0,
		isVerified: Boolean(economist.isVerified),
		isActive: Boolean(economist.isActive),
	}));

	const averageAccuracy =
		rawEconomists.length > 0
			? rawEconomists.reduce((sum, e) => sum + (e.accuracyScore || 0), 0) / rawEconomists.length
			: 0;

	return {
		economists: formattedEconomists,
		totalCount: rawEconomists.length,
		averageAccuracy: Math.round(averageAccuracy * 100) / 100,
	};
}

/**
 * Get top economists data for build scripts (formatted)
 */
export function getTopEconomistsBuildData(dbName: string, isLocal = false, limit = 10) {
	const rawEconomists = executeEconomistSQL(
		ECONOMISTS_SQL.topForHomepage(limit),
		dbName,
		isLocal
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
	) as any[];

	if (!Array.isArray(rawEconomists)) {
		return [];
	}

	return rawEconomists.map((economist, index) => ({
		...economist,
		rank: index + 1,
		accuracyScore: economist.accuracyScore || 0,
		trustScore: economist.trustScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		correctPredictions: economist.correctPredictions || 0,
		isVerified: Boolean(economist.isVerified),
		isActive: Boolean(economist.isActive),
	}));
}

/**
 * Get economist by ID for build scripts (formatted)
 */
export function getEconomistByIdBuildData(dbName: string, id: number, isLocal = false) {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const rawEconomists = executeEconomistSQL(ECONOMISTS_SQL.byId(id), dbName, isLocal) as any[];

	if (!Array.isArray(rawEconomists) || rawEconomists.length === 0) {
		return null;
	}

	const economist = rawEconomists[0];
	return {
		...economist,
		accuracyScore: economist.accuracyScore || 0,
		trustScore: economist.trustScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		correctPredictions: economist.correctPredictions || 0,
		isVerified: Boolean(economist.isVerified),
		isActive: Boolean(economist.isActive),
	};
}
