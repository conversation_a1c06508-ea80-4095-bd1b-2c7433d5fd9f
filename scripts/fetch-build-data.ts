#!/usr/bin/env tsx
/**
 * Build-time Data Fetcher
 *
 * Methodology: Fetch minimal, page-specific data from D1 database at build time
 * and cache in Astro content collections for optimal SSG performance.
 *
 * Benefits:
 * - Reduced runtime database queries
 * - Faster page generation
 * - Consistent data across static pages
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
// Import build functions directly from repositories (Option A: Simplified Direct Approach)
import { getArticlesBuildData } from '../src/server/repositories/articles.js';
import { getEconomistsBuildData } from '../src/server/repositories/economists.js';
import { getVideosBuildData } from '../src/server/repositories/videos.js';
import { getPlatformStatsBuildData } from '../src/server/repositories/stats.js';
import { getPredictionsBuildData } from '../src/server/repositories/predictions.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const CONTENT_DIR = path.join(__dirname, '../src/content');

const DB_NAMES = {
	local: 'economist-tracker-local',
	development: 'economist-tracker-dev',
	staging: 'economist-tracker-staging',
	production: 'economist-tracker-prod',
};

function fetchHomepageData(dbName: string, isLocal: boolean): unknown {
	const topEconomists = getEconomistsBuildData(dbName, isLocal, 10).economists;
	const platformStats = getPlatformStatsBuildData(dbName, isLocal);
	const recentVideos = getVideosBuildData(dbName, isLocal, 5).videos;

	return {
		topEconomists,
		platformStats,
		recentVideos,
		isStatic: false,
	};
}

function fetchEconomistsPageData(dbName: string, isLocal: boolean): unknown {
	return getEconomistsBuildData(dbName, isLocal);
}

function fetchArticlesData(dbName: string, isLocal: boolean) {
	return getArticlesBuildData(dbName, isLocal, 20);
}

function fetchPredictionsData(dbName: string, isLocal: boolean) {
	return getPredictionsBuildData(dbName, isLocal, 50);
}

function fetchVideosData(dbName: string, isLocal: boolean) {
	return getVideosBuildData(dbName, isLocal, 20);
}

function getTargetEnvironment(): string {
	const args = process.argv.slice(2);
	const envArg = args.find((arg) => arg.startsWith('--env='));

	if (envArg) return envArg.split('=')[1];

	const nodeEnv = process.env.NODE_ENV;
	if (nodeEnv === 'production') return 'production';
	if (nodeEnv === 'staging') return 'staging';
	if (nodeEnv === 'development') return 'development';

	return 'local';
}

async function main() {
	const environment = getTargetEnvironment();
	const dbName = DB_NAMES[environment as keyof typeof DB_NAMES];
	const isLocal = environment === 'local';

	console.log('🚀 Fetching build data...\n');
	console.log(`🌍 Environment: ${environment}`);
	console.log(`💾 Database: ${dbName}\n`);

	if (!dbName) {
		throw new Error(`Unknown environment: ${environment}`);
	}

	try {
		// Ensure content directories exist
		await fs.mkdir(path.join(CONTENT_DIR, 'homepage'), { recursive: true });
		await fs.mkdir(path.join(CONTENT_DIR, 'economists'), { recursive: true });
		await fs.mkdir(path.join(CONTENT_DIR, 'predictions'), { recursive: true });
		await fs.mkdir(path.join(CONTENT_DIR, 'videos'), { recursive: true });
		await fs.mkdir(path.join(CONTENT_DIR, 'articles'), { recursive: true });

		// Fetch homepage data
		const homepageData = fetchHomepageData(dbName, isLocal);
		const homepageEntry = {
			$schema: '../../../.astro/collections/homepage.schema.json',
			data: homepageData,
			fetchedAt: new Date().toISOString(),
			environment,
		};

		await fs.writeFile(
			path.join(CONTENT_DIR, 'homepage', 'data.json'),
			JSON.stringify(homepageEntry, null, 2)
		);

		// Fetch economists data
		const economistsData = fetchEconomistsPageData(dbName, isLocal);
		const economistsEntry = {
			$schema: '../../../.astro/collections/economists.schema.json',
			data: economistsData,
			fetchedAt: new Date().toISOString(),
			environment,
		};

		await fs.writeFile(
			path.join(CONTENT_DIR, 'economists', 'data.json'),
			JSON.stringify(economistsEntry, null, 2)
		);

		// Fetch predictions data
		const predictionsData = fetchPredictionsData(dbName, isLocal);
		const predictionsEntry = {
			$schema: '../../../.astro/collections/predictions.schema.json',
			data: predictionsData,
			fetchedAt: new Date().toISOString(),
			environment,
		};

		await fs.writeFile(
			path.join(CONTENT_DIR, 'predictions', 'data.json'),
			JSON.stringify(predictionsEntry, null, 2)
		);

		// Fetch videos data
		const videosData = fetchVideosData(dbName, isLocal);
		const videosEntry = {
			$schema: '../../../.astro/collections/videos.schema.json',
			data: videosData,
			fetchedAt: new Date().toISOString(),
			environment,
		};

		await fs.writeFile(
			path.join(CONTENT_DIR, 'videos', 'data.json'),
			JSON.stringify(videosEntry, null, 2)
		);

		// Fetch articles data
		const articlesData = fetchArticlesData(dbName, isLocal);
		const articlesEntry = {
			$schema: '../../../.astro/collections/articles.schema.json',
			data: articlesData,
			fetchedAt: new Date().toISOString(),
			environment,
		};

		await fs.writeFile(
			path.join(CONTENT_DIR, 'articles', 'data.json'),
			JSON.stringify(articlesEntry, null, 2)
		);

		console.log('\n✨ Build data fetching completed!');
		console.log('🏗️ Ready to build with fresh data');
	} catch (error) {
		console.error('\n❌ Optimized data fetch failed:', error);
		process.exit(1);
	}
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
	main();
}

export { main as fetchOptimizedBuildData };
