import { describe, it, expect } from 'vitest';
import {
  isValidEmail,
  isValidPassword,
  isValidUsername,
  doPasswordsMatch,
  isValidName,
  validateRegistrationData,
  validateProfileUpdateData,
  type RegistrationData,
  type ProfileUpdateData
} from '@/lib/validation';

describe('Authentication Validation Utilities', () => {
  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        '',
        'invalid-email',
        '@domain.com',
        'user@',
        'user <EMAIL>', // Contains space
        'user@domain',
        'user@.com'
      ];

      invalidEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(false);
      });
    });

    it('should handle edge cases', () => {
      expect(isValidEmail(undefined as unknown as string)).toBe(false);
      expect(isValidEmail(null as unknown as string)).toBe(false);
      expect(isValidEmail(123 as unknown as string)).toBe(false);
      expect(isValidEmail('  <EMAIL>  ')).toBe(true); // Should trim
    });
  });

  describe('isValidPassword', () => {
    it('should validate passwords with 8 or more characters', () => {
      const validPasswords = [
        'password123',
        '12345678',
        'MySecurePass!',
        'a'.repeat(8),
        'a'.repeat(100)
      ];

      validPasswords.forEach(password => {
        expect(isValidPassword(password)).toBe(true);
      });
    });

    it('should reject passwords with less than 8 characters', () => {
      const invalidPasswords = [
        '',
        '1234567',
        'short',
        'a'.repeat(7)
      ];

      invalidPasswords.forEach(password => {
        expect(isValidPassword(password)).toBe(false);
      });
    });

    it('should handle edge cases', () => {
      expect(isValidPassword(undefined as unknown as string)).toBe(false);
      expect(isValidPassword(null as unknown as string)).toBe(false);
      expect(isValidPassword(123 as unknown as string)).toBe(false);
    });
  });

  describe('isValidUsername', () => {
    it('should validate correct usernames', () => {
      const validUsernames = [
        'user123',
        'test-user',
        'user_name',
        'username',
        'User123',
        'a'.repeat(3),
        'a'.repeat(30)
      ];

      validUsernames.forEach(username => {
        expect(isValidUsername(username)).toBe(true);
      });
    });

    it('should reject invalid usernames', () => {
      const invalidUsernames = [
        '',
        'ab', // Too short
        'a'.repeat(31), // Too long
        'user name', // Contains space
        'user@name', // Contains @
        'user.name', // Contains dot
        'user!', // Contains special character
        'üser', // Contains non-ASCII
      ];

      invalidUsernames.forEach(username => {
        expect(isValidUsername(username)).toBe(false);
      });
    });

    it('should handle edge cases', () => {
      expect(isValidUsername(undefined as unknown as string)).toBe(false);
      expect(isValidUsername(null as unknown as string)).toBe(false);
      expect(isValidUsername(123 as unknown as string)).toBe(false);
      expect(isValidUsername('  user123  ')).toBe(true); // Should trim
    });
  });

  describe('doPasswordsMatch', () => {
    it('should return true for matching passwords', () => {
      expect(doPasswordsMatch('password123', 'password123')).toBe(true);
      expect(doPasswordsMatch('complex!Pass123', 'complex!Pass123')).toBe(true);
    });

    it('should return false for non-matching passwords', () => {
      expect(doPasswordsMatch('password123', 'password124')).toBe(false);
      expect(doPasswordsMatch('password', 'Password')).toBe(false);
      expect(doPasswordsMatch('password123', '')).toBe(false);
      expect(doPasswordsMatch('', 'password123')).toBe(false);
    });

    it('should handle edge cases', () => {
      expect(doPasswordsMatch(undefined as unknown as string, undefined as unknown as string)).toBe(false);
      expect(doPasswordsMatch(null as unknown as string, null as unknown as string)).toBe(false);
      expect(doPasswordsMatch('password', undefined as unknown as string)).toBe(false);
      expect(doPasswordsMatch(undefined as unknown as string, 'password')).toBe(false);
    });
  });

  describe('isValidName', () => {
    it('should validate correct names', () => {
      const validNames = [
        'John',
        'John Doe',
        'Jean-Pierre',
        'Mary O\'Connor',
        'José',
        'Александр',
        'a'.repeat(2),
        'a'.repeat(100)
      ];

      validNames.forEach(name => {
        expect(isValidName(name)).toBe(true);
      });
    });

    it('should reject invalid names', () => {
      const invalidNames = [
        '',
        'a', // Too short
        'a'.repeat(101), // Too long
        '   ', // Only whitespace
      ];

      invalidNames.forEach(name => {
        expect(isValidName(name)).toBe(false);
      });
    });

    it('should handle edge cases', () => {
      expect(isValidName(undefined as unknown as string)).toBe(false);
      expect(isValidName(null as unknown as string)).toBe(false);
      expect(isValidName(123 as unknown as string)).toBe(false);
      expect(isValidName('  John  ')).toBe(true); // Should trim
    });
  });

  describe('validateRegistrationData', () => {
    it('should validate complete and correct registration data', () => {
      const validData: RegistrationData = {
        name: 'John Doe',
        email: '<EMAIL>',
        username: 'johndoe',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const result = validateRegistrationData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate minimal registration data (without optional fields)', () => {
      const validData: RegistrationData = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const result = validateRegistrationData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid email', () => {
      const invalidData: RegistrationData = {
        email: 'invalid-email',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const result = validateRegistrationData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Please provide a valid email address');
    });

    it('should return errors for weak password', () => {
      const invalidData: RegistrationData = {
        email: '<EMAIL>',
        password: 'weak',
        confirmPassword: 'weak'
      };

      const result = validateRegistrationData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should return errors for mismatched passwords', () => {
      const invalidData: RegistrationData = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password124'
      };

      const result = validateRegistrationData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Passwords do not match');
    });

    it('should return errors for invalid name', () => {
      const invalidData: RegistrationData = {
        name: 'x', // Too short
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      };

      const result = validateRegistrationData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Name must be between 2 and 100 characters');
    });

    it('should return errors for invalid username', () => {
      const invalidData: RegistrationData = {
        email: '<EMAIL>',
        username: 'x', // Too short
        password: 'password123',
        confirmPassword: 'password123'
      };

      const result = validateRegistrationData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens');
    });

    it('should return multiple errors for multiple invalid fields', () => {
      const invalidData: RegistrationData = {
        name: 'x',
        email: 'invalid-email',
        username: 'x',
        password: 'weak',
        confirmPassword: 'different'
      };

      const result = validateRegistrationData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
    });
  });

  describe('validateProfileUpdateData', () => {
    it('should validate empty update data', () => {
      const validData: ProfileUpdateData = {};

      const result = validateProfileUpdateData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate name update only', () => {
      const validData: ProfileUpdateData = {
        name: 'John Updated'
      };

      const result = validateProfileUpdateData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate username update only', () => {
      const validData: ProfileUpdateData = {
        username: 'johnupdated'
      };

      const result = validateProfileUpdateData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate complete password change', () => {
      const validData: ProfileUpdateData = {
        currentPassword: 'oldpassword123',
        newPassword: 'newpassword123',
        confirmNewPassword: 'newpassword123'
      };

      const result = validateProfileUpdateData(validData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid name', () => {
      const invalidData: ProfileUpdateData = {
        name: 'x' // Too short
      };

      const result = validateProfileUpdateData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Name must be between 2 and 100 characters');
    });

    it('should return errors for invalid username', () => {
      const invalidData: ProfileUpdateData = {
        username: 'x' // Too short
      };

      const result = validateProfileUpdateData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens');
    });

    it('should require current password for password change', () => {
      const invalidData: ProfileUpdateData = {
        newPassword: 'newpassword123',
        confirmNewPassword: 'newpassword123'
        // Missing currentPassword
      };

      const result = validateProfileUpdateData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Current password is required to change password');
    });

    it('should validate new password strength', () => {
      const invalidData: ProfileUpdateData = {
        currentPassword: 'oldpassword123',
        newPassword: 'weak',
        confirmNewPassword: 'weak'
      };

      const result = validateProfileUpdateData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('New password must be at least 8 characters long');
    });

    it('should validate new password confirmation', () => {
      const invalidData: ProfileUpdateData = {
        currentPassword: 'oldpassword123',
        newPassword: 'newpassword123',
        confirmNewPassword: 'newpassword124'
      };

      const result = validateProfileUpdateData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('New passwords do not match');
    });

    it('should return multiple errors for multiple invalid fields', () => {
      const invalidData: ProfileUpdateData = {
        name: 'x',
        username: 'x',
        newPassword: 'weak',
        confirmNewPassword: 'different'
      };

      const result = validateProfileUpdateData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
    });
  });

  describe('Test Data Generation Utilities', () => {
    it('should generate valid test user data', () => {
      const testUser = {
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        password: 'testpass123'
      };

      expect(isValidName(testUser.name)).toBe(true);
      expect(isValidEmail(testUser.email)).toBe(true);
      expect(isValidUsername(testUser.username)).toBe(true);
      expect(isValidPassword(testUser.password)).toBe(true);
    });

    it('should generate invalid test data for negative testing', () => {
      const invalidUser = {
        name: 'x',
        email: 'invalid-email',
        username: 'x',
        password: 'weak'
      };

      expect(isValidName(invalidUser.name)).toBe(false);
      expect(isValidEmail(invalidUser.email)).toBe(false);
      expect(isValidUsername(invalidUser.username)).toBe(false);
      expect(isValidPassword(invalidUser.password)).toBe(false);
    });
  });
});
