#!/usr/bin/env tsx

/**
 * Cloudflare Workers Deployment Script
 *
 * This script handles the complete deployment process for Cloudflare Workers,
 * including database migrations, environment validation, and deployment.
 *
 * Usage:
 *   npm run deploy              # Deploy to production
 *   npm run deploy:staging      # Deploy to staging
 *   tsx scripts/deploy.ts dev   # Deploy to development
 */

import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import { resolve } from 'path';

type Environment = 'development' | 'staging' | 'production';

interface DeploymentConfig {
	environment: Environment;
	workerName: string;
	databaseName: string;
	healthCheckUrl: string;
}

const configs: Record<Environment, DeploymentConfig> = {
	development: {
		environment: 'development',
		workerName: 'economist-accuracy-tracker-tr-web-app-dev',
		databaseName: 'economist-tracker-dev',
		healthCheckUrl: 'https://economist-accuracy-tracker-tr-web-app-dev.cihangir-yildirim-2d0.workers.dev/api/v1/health'
	},
	staging: {
		environment: 'staging',
		workerName: 'economist-accuracy-tracker-tr-web-app-staging',
		databaseName: 'economist-tracker-staging',
		healthCheckUrl: 'https://economist-accuracy-tracker-tr-web-app-staging.cihangir-yildirim-2d0.workers.dev/api/v1/health'
	},
	production: {
		environment: 'production',
		workerName: 'economist-accuracy-tracker-tr-web-app-prod',
		databaseName: 'economist-tracker-prod',
		healthCheckUrl: 'https://economist-accuracy-tracker-tr-web-app-prod.cihangir-yildirim-2d0.workers.dev/api/v1/health'
	}
};

function log(message: string, level: 'info' | 'success' | 'error' | 'warning' = 'info'): void {
	const colors = {
		info: '\x1b[36m',
		success: '\x1b[32m',
		error: '\x1b[31m',
		warning: '\x1b[33m',
		reset: '\x1b[0m'
	};

	const prefix = {
		info: 'ℹ',
		success: '✅',
		error: '❌',
		warning: '⚠️'
	};

	console.log(`${colors[level]}${prefix[level]} ${message}${colors.reset}`);
}

function executeCommand(command: string, description: string): string {
	try {
		log(`Executing: ${description}...`);
		const result = execSync(command, { encoding: 'utf-8', stdio: 'pipe' });
		log(`✓ ${description} completed`, 'success');
		return result;
	} catch (error) {
		log(`✗ ${description} failed: ${String(error)}`, 'error');
		throw error;
	}
}

function validateEnvironment(environment: Environment): void {
	log(`Validating ${environment} environment...`);

	// Check if wrangler is installed and authenticated
	try {
		executeCommand('npx wrangler whoami', 'Checking Wrangler authentication');
	} catch {
		log('Wrangler authentication failed. Please run: npx wrangler login', 'error');
		throw new Error('Wrangler authentication failed');
	}

	// Check if wrangler.toml exists and has proper configuration
	const wranglerPath = resolve(process.cwd(), 'wrangler.toml');
	try {
		const wranglerContent = readFileSync(wranglerPath, 'utf-8');
		if (!wranglerContent.includes(`[env.${environment}]`) && environment !== 'development') {
			throw new Error(`Environment ${environment} not found in wrangler.toml`);
		}
		log('wrangler.toml configuration validated', 'success');
	} catch (error) {
		log(`wrangler.toml validation failed: ${String(error)}`, 'error');
		throw error;
	}
}

function buildProject(config: DeploymentConfig): void {
	log(`Building project with ${config.environment} database data...`);

	// Map environment names to build script names
	const buildScriptMap = {
		development: 'dev',
		staging: 'staging',
		production: 'prod'
	};

	const buildScriptName = buildScriptMap[config.environment];
	const buildCommand = `npm run build:${buildScriptName}`;
	executeCommand(buildCommand, `Building project with ${config.environment} database data`);
}

function runDatabaseMigrations(config: DeploymentConfig): void {
	log(`Running database migrations for ${config.environment}...`);

	const migrationCommand = `npx wrangler d1 migrations apply ${config.databaseName} --remote --env ${config.environment}`;
	try {
		executeCommand(migrationCommand, `Database migration for ${config.environment}`);
	} catch {
		log(`Database migration failed. This might be normal if no new migrations exist.`, 'warning');
		// Don't throw error for migrations - they might not exist or already be applied
	}
}

function deployWorker(config: DeploymentConfig): void {
	log(`Deploying to ${config.environment} environment...`);

	executeCommand(`npx wrangler deploy --env ${config.environment}`, `Deploying to ${config.environment}`);
}

async function healthCheck(config: DeploymentConfig): Promise<void> {
	log(`Performing health check for ${config.environment}...`);

	// Wait a bit for the deployment to propagate
	await new Promise(resolve => globalThis.setTimeout(resolve, 10000));

	try {
		const healthCheckCommand = `curl -f ${config.healthCheckUrl} -m 30`;
		executeCommand(healthCheckCommand, 'Health check');
		log(`Health check passed for ${config.environment}`, 'success');
	} catch {
		log(`Health check failed for ${config.environment}. The deployment may still be propagating.`, 'warning');
		// Don't throw error for health check - it might just be propagation delay
	}
}

function printDeploymentSummary(config: DeploymentConfig): void {
	log(`\n🎉 Deployment to ${config.environment} completed!`, 'success');
	log(`\n📋 Deployment Summary:`, 'info');
	log(`Environment: ${config.environment}`, 'info');
	log(`Worker: ${config.workerName}`, 'info');
	log(`Database: ${config.databaseName}`, 'info');
	log(`Health Check URL: ${config.healthCheckUrl}`, 'info');
	log(`\n🔗 Access your deployed application:`, 'info');
	log(`${config.healthCheckUrl.replace('/api/v1/health', '')}`, 'info');
}

async function deploy(environment: Environment): Promise<void> {
	const config = configs[environment];

	if (!config) {
		throw new Error(`Invalid environment: ${environment}`);
	}

	try {
		log(`🚀 Starting deployment to ${environment}...`, 'info');

		// Validate environment
		validateEnvironment(environment);

		// Build project with environment-specific database data
		buildProject(config);

		// Run database migrations
		runDatabaseMigrations(config);

		// Deploy worker
		deployWorker(config);

		// Health check
		await healthCheck(config);

		// Print summary
		printDeploymentSummary(config);

	} catch (error) {
		log(`Deployment to ${environment} failed: ${String(error)}`, 'error');
		process.exit(1);
	}
}

// Main execution
async function main(): Promise<void> {
	const environment = (process.argv[2] || 'production') as Environment;

	if (!configs[environment]) {
		log(`Invalid environment: ${environment}`, 'error');
		log('Valid environments: development, staging, production', 'info');
		process.exit(1);
	}

	await deploy(environment);
}

// Run if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
	main();
}

export { deploy, configs };
