---
/**
 * Articles Listing Page
 *
 * SSG page that shows all articles with pagination.
 * Uses build-time data for initial load, then hydrates for pagination.
 */

import Layout from '@/layouts/Layout.astro';
import { getEntry } from 'astro:content';

// Get build-time data from content collections
console.log('[Articles Page] Loading build-time data from content collections...');
const cachedData = await getEntry('articles', 'data');

if (!cachedData?.data) {
  throw new Error('[Articles Page] No build-time data found. Run the build process to generate data.');
}

const articlesData = cachedData.data.data;
const { articles, totalCount } = articlesData;

console.log(`[Articles Page] Loaded articles from content collections: ${articles.length}`);
console.log(`[Articles Page] Total articles available: ${totalCount}`);

// Format articles for display
const formattedArticles = articles.map((article: any) => ({
  id: article.id,
  title: article.title,
  excerpt: article.excerpt,
  author: article.author,
  publishedAt: new Date(article.publishedAt),
  imageUrl: article.imageUrl,
  category: article.category,
  readTime: article.readTime,
  slug: article.slug,
  tags: article.tags || [],
  isCurated: article.isCurated,
}));

// Sort by published date (newest first)
formattedArticles.sort((a: any, b: any) => b.publishedAt.getTime() - a.publishedAt.getTime());

// Separate curated and regular articles
const curatedArticles = formattedArticles.filter((article: any) => article.isCurated);

// Format dates for display
function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
}

function formatRelativeDate(date: Date): string {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return 'Bugün';
  } else if (diffInDays === 1) {
    return 'Dün';
  } else if (diffInDays < 7) {
    return `${diffInDays} gün önce`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `${weeks} hafta önce`;
  } else {
    return formatDate(date);
  }
}
---

<Layout title="Tüm Makaleler - YouTube Economist Trust Score">
  <main class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <!-- <div class="bg-white dark:bg-gray-800 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Ekonomi Makaleleri
          </h1>
          <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Türkiye ekonomisi, küresel piyasalar ve finansal analizler hakkında güncel makaleler
          </p>
          <div class="mt-4 text-sm text-gray-500 dark:text-gray-400">
            Toplam {totalCount} makale
          </div>
        </div>
      </div>
    </div> -->

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Curated Articles Section -->
      {curatedArticles.length > 0 && (
        <section class="mb-12">
          <div class="flex items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
              Öne Çıkan Makaleler
            </h2>
            <div class="ml-3 px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded-full">
              Editör Seçimi
            </div>
          </div>

          <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {curatedArticles.slice(0, 6).map((article: any) => (
              <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                <a href={`/articles/${article.slug}`} class="block">
                  {article.imageUrl && (
                    <div class="aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-lg overflow-hidden">
                      <img
                        src={article.imageUrl}
                        alt={article.title}
                        class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                        loading="lazy"
                      />
                    </div>
                  )}

                  <div class="p-6">
                    <div class="flex items-center gap-2 mb-3">
                      <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded">
                        {article.category}
                      </span>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        {article.readTime}
                      </span>
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                      {article.title}
                    </h3>

                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                      {article.excerpt}
                    </p>

                    <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>{article.author}</span>
                      <span>{formatRelativeDate(article.publishedAt)}</span>
                    </div>
                  </div>
                </a>
              </article>
            ))}
          </div>
        </section>
      )}

      <!-- All Articles Section -->
      <section>
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
            Tüm Makaleler
          </h2>

          <!-- Filter/Sort Options -->
          <div class="flex items-center gap-4">
            <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm">
              <option value="newest">En Yeni</option>
              <option value="oldest">En Eski</option>
              <option value="popular">Popüler</option>
            </select>
          </div>
        </div>

        <!-- Articles Grid -->
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3" id="articles-grid">
          {formattedArticles.map((article: any) => (
            <article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
              <a href={`/articles/${article.slug}`} class="block">
                {article.imageUrl && (
                  <div class="aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-lg overflow-hidden">
                    <img
                      src={article.imageUrl}
                      alt={article.title}
                      class="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                      loading="lazy"
                    />
                  </div>
                )}

                <div class="p-6">
                  <div class="flex items-center gap-2 mb-3">
                    <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs font-medium rounded">
                      {article.category}
                    </span>
                    <span class="text-xs text-gray-500 dark:text-gray-400">
                      {article.readTime}
                    </span>
                    {article.isCurated && (
                      <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded">
                        Öne Çıkan
                      </span>
                    )}
                  </div>

                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                    {article.title}
                  </h3>

                  <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                    {article.excerpt}
                  </p>

                  <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{article.author}</span>
                    <span>{formatRelativeDate(article.publishedAt)}</span>
                  </div>

                  {article.tags.length > 0 && (
                    <div class="mt-3 flex flex-wrap gap-1">
                      {article.tags.slice(0, 3).map((tag: string) => (
                        <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded">
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </a>
            </article>
          ))}
        </div>

        <!-- Load More Button (for future hydration) -->
        <div class="text-center mt-12">
          <button
            id="load-more-btn"
            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            style="display: none;"
          >
            Daha Fazla Makale Yükle
          </button>
        </div>
      </section>
    </div>
  </main>

  <!-- Future: Add client-side script for pagination -->
  <script>
    // Placeholder for future hydration functionality
    console.log('Articles page loaded with', document.querySelectorAll('article').length, 'articles');
  </script>

  <style>
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  </style>
</Layout>
