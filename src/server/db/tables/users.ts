import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const users = sqliteTable('users', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	email: text('email').notNull().unique(),
	username: text('username').unique(),
	passwordHash: text('password_hash'),
	role: text('role', {
		enum: ['admin', 'moderator', 'editor', 'analyst', 'contributor', 'user']
	}).notNull().default('user'),
	isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
	emailVerified: integer('email_verified', { mode: 'boolean' }).notNull().default(false),
	lastLoginAt: integer('last_login_at', { mode: 'timestamp' }),
	createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
	updatedAt: integer('updated_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
});
