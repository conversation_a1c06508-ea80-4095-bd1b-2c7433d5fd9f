import { Hono } from 'hono';
import { createVerificationService } from '@/server/services/verification';
import { getDbFromContext } from '../../data';
import { requireModerator } from '../../auth/middleware';

const app = new Hono();

// POST endpoint to run automated verification
app.post('/', requireModerator(), async (c) => {
	const db = getDbFromContext(c);
	if (!db) {
		return c.json({
			success: false,
			error: 'Database connection not available'
		}, 503);
	}

	const verificationService = createVerificationService(db);

	try {
		const body = await c.req.json();
		const { predictionIds, runAll = false } = body;

		let results = [];

		if (runAll) {
			// Run verification for all ready predictions
			console.log('Running automated verification for all ready predictions...');
			results = await verificationService.runAutomatedVerification();
		} else if (predictionIds && Array.isArray(predictionIds)) {
			// Run verification for specific predictions
			console.log(`Running automated verification for ${predictionIds.length} specific predictions...`);

			for (const predictionId of predictionIds) {
				const result = await verificationService.verifyPrediction(predictionId);
				if (result) {
					results.push(result);
				}
			}
		} else {
			return c.json({
				success: false,
				error: 'Invalid request',
				message: 'Either set runAll=true or provide predictionIds array'
			}, 400);
		}

		// Calculate summary statistics
		const summary = {
			totalProcessed: results.length,
			successfulVerifications: results.filter(r => r.success).length,
			failedVerifications: results.filter(r => !r.success).length,
			verifiedCorrect: results.filter(r => r.status === 'verified_correct').length,
			verifiedIncorrect: results.filter(r => r.status === 'verified_incorrect').length,
			averageAccuracy: results.length > 0
				? results.reduce((sum, r) => sum + (r.accuracy || 0), 0) / results.length
				: 0,
			categoryBreakdown: results.reduce((acc: Record<string, number>, _result) => {
				// We'd need to fetch prediction category for this
				acc['automated'] = (acc['automated'] || 0) + 1;
				return acc;
			}, {})
		};

		console.log(`Automated verification completed:`, summary);

		return c.json({
			success: true,
			data: {
				verificationResults: results,
				summary
			},
			message: `Successfully verified ${results.length} predictions using automated systems`
		});

	} catch (error) {
		console.error('Error running automated verification:', error);

		return c.json({
			success: false,
			error: 'Automated verification failed',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

// GET endpoint to check verification status and statistics
app.get('/', requireModerator(), async (c) => {
	const db = getDbFromContext(c);
	if (!db) {
		return c.json({
			success: false,
			error: 'Database connection not available'
		}, 503);
	}

	const verificationService = createVerificationService(db);

	try {
		// Get predictions ready for verification
		const readyPredictions = await verificationService.getPredictionsReadyForVerification();

		// Calculate statistics
		const stats = {
			totalReadyForVerification: readyPredictions.length,
			categoryBreakdown: readyPredictions.reduce((acc: Record<string, number>, prediction) => {
				acc[prediction.category] = (acc[prediction.category] || 0) + 1;
				return acc;
			}, {}),
			urgentPredictions: readyPredictions.filter(p => {
				const daysPast = Math.floor((Date.now() - new Date(p.targetDate).getTime()) / (1000 * 60 * 60 * 24));
				return daysPast > 7;
			}).length,
			oldestPrediction: readyPredictions.length > 0
				? readyPredictions.reduce((oldest, current) =>
					new Date(current.targetDate) < new Date(oldest.targetDate) ? current : oldest
				).targetDate
				: null
		};

		return c.json({
			success: true,
			data: {
				verificationStats: stats,
				readyPredictions: readyPredictions.map(p => ({
					id: p.id,
					category: p.category,
					predictionType: p.predictionType,
					targetDate: p.targetDate,
					economistName: p.economist?.name
				}))
			},
			message: 'Verification status retrieved successfully'
		});

	} catch (error) {
		console.error('Error getting verification status:', error);

		return c.json({
			success: false,
			error: 'Failed to get verification status',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

export default app;
