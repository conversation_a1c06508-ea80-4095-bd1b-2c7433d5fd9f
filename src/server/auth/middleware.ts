import type { Context, Next } from 'hono';
import { getAuth<PERSON>ontext, hasAnyRole, hasR<PERSON><PERSON><PERSON><PERSON>igher } from './index';
import type { UserRole } from './index';
import { getDbFromContext } from '../data';

export interface AuthMiddlewareOptions {
  requiredRoles?: UserRole[];
  requireAuth?: boolean;
  hierarchical?: boolean; // If true, uses hasRoleOrHigher instead of hasAnyRole
}

/**
 * Hono middleware for authentication and authorization
 */
export function authMiddleware(options: AuthMiddlewareOptions = {}) {
  return async (c: Context, next: Next) => {
    const { requiredRoles = [], requireAuth = false, hierarchical = false } = options;

    try {
      // Get database from Hono context (set by the main API handler)
      const db = getDbFromContext(c);
      if (!db) {
        console.warn('No database connection available in auth middleware');
        return c.json({
          status: 'error',
          errorCode: 'DATABASE_CONNECTION_ERROR',
          message: 'Database not available'
        }, 500);
      }

      const authContext = await getAuthContext(c.req.raw, db);

      // Attach auth context to Hono context
      c.set('auth', authContext);

      // Check if authentication is required
      if (requireAuth && !authContext.isAuthenticated) {
        return c.json({
          status: 'error',
          errorCode: 'AUTHENTICATION_REQUIRED',
          message: 'You must be logged in to access this resource'
        }, 401);
      }

      // Check role-based authorization
      if (requiredRoles.length > 0 && authContext.isAuthenticated) {
        const hasPermission = hierarchical
          ? requiredRoles.some(role => hasRoleOrHigher(authContext.roles, role))
          : hasAnyRole(authContext.roles, requiredRoles);

        if (!hasPermission) {
          return c.json({
            status: 'error',
            errorCode: 'INSUFFICIENT_PERMISSIONS',
            message: `Required roles: ${requiredRoles.join(', ')}`
          }, 403);
        }
      }

      await next();
    } catch (error) {
      console.error('Auth middleware error:', error);
      return c.json({
        status: 'error',
        errorCode: 'AUTHENTICATION_ERROR',
        message: 'Failed to authenticate request'
      }, 500);
    }
  };
}

/**
 * Convenience middleware functions for common use cases
 */
export const requireAuth = () => authMiddleware({ requireAuth: true });

export const requireAdmin = () => authMiddleware({
  requiredRoles: ['ROLE_ADMIN'],
  requireAuth: true
});

export const requireModerator = () => authMiddleware({
  requiredRoles: ['ROLE_MODERATOR'],
  requireAuth: true,
  hierarchical: true
});

export const requireEditor = () => authMiddleware({
  requiredRoles: ['ROLE_EDITOR'],
  requireAuth: true,
  hierarchical: true
});

export const requirePaidUser = () => authMiddleware({
  requiredRoles: ['ROLE_PAID_USER'],
  requireAuth: true
});

export const requireContributor = () => authMiddleware({
  requiredRoles: ['ROLE_CONTRIBUTOR'],
  requireAuth: true,
  hierarchical: true
});

export const requireAnalyst = () => authMiddleware({
  requiredRoles: ['ROLE_ANALYST'],
  requireAuth: true,
  hierarchical: true
});
