<!-- Client-side auth component that hydrates user state -->
<script>
interface User {
	id: string;
	email: string;
	username?: string;
	roles?: string[];
}

interface AuthResponse {
	success: boolean;
	data?: {
		user: User;
		roles: string[];
		isAuthenticated: boolean;
	};
	error?: string;
	message?: string;
}

declare global {
	interface Window {
		clientAuth: ClientAuth;
	}
}

class ClientAuth {
	private user: User | null = null;
	private roles: string[] = [];
	private isLoading = true;
	private hasInitialRender = false;

	async init() {
		// Try to get cached auth state for immediate rendering
		this.loadCachedAuthState();

		// Render immediately with cached data (if available) to prevent layout shift
		if (this.user || this.getCachedAuthState() === 'unauthenticated') {
			this.isLoading = false;
			this.hasInitialRender = true;
			this.updateUI();
		}

		// Now fetch the real auth state from server
		try {
			const response = await fetch('/api/v1/auth/me', {
				method: 'GET',
				credentials: 'include',
			});
		if (response.ok) {
			const authResponse = await response.json() as AuthResponse;
			// The API response structure is { success: true, data: { user: {...}, roles: [...] } }
			const newUser = authResponse.data?.user || null;
			const newRoles = authResponse.data?.roles || [];

			// Only update UI if the auth state actually changed
			if (this.hasAuthStateChanged(newUser, newRoles)) {
				this.user = newUser;
				this.roles = newRoles;
				this.updateUI();
			} else {
				this.user = newUser;
				this.roles = newRoles;
			}

			// Cache the new auth state
			this.cacheAuthState(newUser, newRoles);
			} else {
				// Only update if we thought user was logged in but they're not
				if (this.user !== null) {
					this.user = null;
					this.updateUI();
				}
				this.cacheAuthState(null);
			}
		} catch (error) {
			console.error('Auth check failed:', error);
			// Only update if we thought user was logged in
			if (this.user !== null) {
				this.user = null;
				this.updateUI();
			}
			this.cacheAuthState(null);
		} finally {
			this.isLoading = false;
			if (!this.hasInitialRender) {
				this.updateUI();
			}
		}
	}

	private loadCachedAuthState() {
		try {
			const cached = localStorage.getItem('auth-cache');
			if (cached) {
				const { user, roles, timestamp } = JSON.parse(cached);
				// Cache is valid for 5 minutes
				if (Date.now() - timestamp < 5 * 60 * 1000) {
					this.user = user;
					this.roles = roles || [];
				}
			}
		} catch {
			// Ignore cache errors
		}
	}

	private getCachedAuthState(): 'authenticated' | 'unauthenticated' | null {
		try {
			const cached = localStorage.getItem('auth-cache');
			if (cached) {
				const { user, timestamp } = JSON.parse(cached);
				// Cache is valid for 5 minutes
				if (Date.now() - timestamp < 5 * 60 * 1000) {
					return user ? 'authenticated' : 'unauthenticated';
				}
			}
		} catch {
			// Ignore cache errors
		}
		return null;
	}

	private cacheAuthState(user: User | null, roles: string[] = []) {
		try {
			localStorage.setItem('auth-cache', JSON.stringify({
				user,
				roles,
				timestamp: Date.now()
			}));
		} catch {
			// Ignore cache errors (e.g., storage full)
		}
	}

	private hasAuthStateChanged(newUser: User | null, newRoles: string[] = []): boolean {
		// Compare auth states
		const wasAuthenticated = this.user !== null;
		const isAuthenticated = newUser !== null;

		if (wasAuthenticated !== isAuthenticated) {
			return true;
		}

		// If both authenticated, check if it's the same user or roles changed
		if (wasAuthenticated && isAuthenticated) {
			const userChanged = this.user!.id !== newUser!.id || this.user!.username !== newUser!.username;
			const rolesChanged = JSON.stringify(this.roles.sort()) !== JSON.stringify(newRoles.sort());
			return userChanged || rolesChanged;
		}

		return false;
	}

	private updateUI() {
		// Update user menu in header
		this.updateUserMenu();

		// Dispatch custom event for other components to listen to
		window.dispatchEvent(new CustomEvent('auth:updated', {
			detail: { user: this.user, isLoading: this.isLoading }
		}));
	}

	private updateUserMenu() {
		const userMenuContainer = document.querySelector('[data-user-menu]');
		if (!userMenuContainer) return;

		// Add smooth transition if not already present
		if (!userMenuContainer.classList.contains('transition-all')) {
			userMenuContainer.classList.add('transition-all', 'duration-200', 'ease-in-out');
		}

		if (this.isLoading) {
			userMenuContainer.innerHTML = this.getLoadingHTML();
		} else if (this.user) {
			userMenuContainer.innerHTML = this.getLoggedInHTML();
		} else {
			userMenuContainer.innerHTML = this.getLoggedOutHTML();
		}

		// Re-attach event listeners after updating HTML
		this.attachEventListeners();
	}

	private getLoadingHTML(): string {
		// Show minimal loading state that matches the size of login buttons
		return `
			<div class="flex items-center space-x-2">
				<div class="w-12 h-8 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
				<div class="w-20 h-8 bg-amber-300 dark:bg-amber-600 rounded animate-pulse"></div>
			</div>
		`;
	}

	private getLoggedInHTML(): string {
		const displayName = this.user!.username || this.user!.email.split('@')[0];

		// Check if user has admin/moderator roles
		const hasAdminAccess = this.roles &&
			(this.roles.includes('ROLE_ADMIN') || this.roles.includes('ROLE_MODERATOR'));

		return `
			<div class="relative group">
				<button class="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none transition-colors" data-user-dropdown-trigger>
					<div class="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
						<svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
							<path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
						</svg>
					</div>
					<span class="hidden sm:block text-sm font-medium">
						${displayName}
					</span>
					<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
						<path d="M7.41 8.84L12 13.42l4.59-4.58L18 10.25l-6 6-6-6z"/>
					</svg>
				</button>

				<div class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50" data-user-dropdown>
					<div class="py-1">
						<a href="/dashboard" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
							<span class="flex items-center">
								<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
									<path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
								</svg>
								Panel
							</span>
						</a>
						${hasAdminAccess ? `
						<a href="/admin/dashboard" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
							<span class="flex items-center">
								<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
									<path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
								</svg>
								Admin Panel
							</span>
						</a>
						` : ''}
						<a href="/profile" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
							<span class="flex items-center">
								<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
									<path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
								</svg>
								Profil
							</span>
						</a>
						<hr class="border-gray-200 dark:border-gray-700 my-1" />
						<button data-logout-btn class="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
							<span class="flex items-center">
								<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
									<path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
								</svg>
								Çıkış Yap
							</span>
						</button>
					</div>
				</div>
			</div>
		`;
	}

	private getLoggedOutHTML(): string {
		return `
			<div class="flex items-center space-x-2">
				<a href="/login" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 text-sm font-medium transition-colors">
					Giriş
				</a>
				<a href="/register" class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
					Kayıt Ol
				</a>
			</div>
		`;
	}

	private attachEventListeners() {
		// Logout button
		const logoutBtn = document.querySelector('[data-logout-btn]');
		if (logoutBtn) {
			logoutBtn.addEventListener('click', async (e) => {
				e.preventDefault();
				await this.logout();
			});
		}
	}

	private async logout() {
		try {
			const response = await fetch('/api/v1/auth/logout', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
			});

			if (response.ok) {
				this.user = null;
				this.cacheAuthState(null); // Clear cached auth state
				this.updateUI();
				window.location.href = '/';
			} else {
				console.error('Logout failed');
			}
		} catch (error) {
			console.error('Logout error:', error);
		}
	}

	getUser(): User | null {
		return this.user;
	}

	isAuthenticated(): boolean {
		return this.user !== null;
	}
}

// Initialize auth when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
	const clientAuth = new ClientAuth();
	clientAuth.init();

	// Make auth available globally
	window.clientAuth = clientAuth;
});
</script>
