import { Hono } from 'hono';
import { eq, desc, and, lt } from 'drizzle-orm';
import { predictions, economists, videos } from '@/server/db/schema';
import { getDbFromContext } from '../../data';
import { requireModerator } from '../../auth/middleware';

const verificationQueueRouter = new Hono();

// GET /api/predictions/verification-queue - Get predictions that need verification
verificationQueueRouter.get('/', requireModerator(), async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				success: false,
				error: 'Database connection not available'
			}, 503);
		}

		const page = parseInt(c.req.query('page') || '1');
		const limit = Math.min(parseInt(c.req.query('limit') || '20'), 50);
		const offset = (page - 1) * limit;

		// Include filter for verification readiness
		const includeExpired = c.req.query('includeExpired') === 'true';

		// Base query for predictions that need verification
		const whereConditions = [
			eq(economists.isActive, true),
			eq(predictions.status, 'pending')
		];

		// Add condition for predictions past their target date
		if (!includeExpired) {
			whereConditions.push(lt(predictions.targetDate, new Date()));
		}

		// Fetch predictions ready for verification
		const predictionsData = await db
			.select()
			.from(predictions)
			.innerJoin(economists, eq(predictions.economistId, economists.id))
			.innerJoin(videos, eq(predictions.videoId, videos.id))
			.where(and(...whereConditions))
			.orderBy(desc(predictions.targetDate))
			.limit(limit)
			.offset(offset);

		// Get total count for pagination
		const totalPredictions = await db
			.select()
			.from(predictions)
			.innerJoin(economists, eq(predictions.economistId, economists.id))
			.where(and(...whereConditions));

		const totalCount = totalPredictions.length;
		const totalPages = Math.ceil(totalCount / limit);

		// Transform data with verification context
		const transformedData = predictionsData.map(row => {
			let targetValue = null;
			let tags: string[] = [];

			try {
				if (row.predictions.targetValue) {
					targetValue = JSON.parse(row.predictions.targetValue);
				}
			} catch {
				console.warn('Failed to parse targetValue JSON:', row.predictions.targetValue);
			}

			try {
				if (row.predictions.tags) {
					tags = JSON.parse(row.predictions.tags);
				}
			} catch {
				console.warn('Failed to parse tags JSON:', row.predictions.tags);
			}

			// Calculate days since target date for verification priority
			const targetDate = new Date(row.predictions.targetDate);
			const now = new Date();
			const daysSinceTarget = Math.floor((now.getTime() - targetDate.getTime()) / (1000 * 60 * 60 * 24));

			return {
				id: row.predictions.id,
				title: row.predictions.title,
				description: row.predictions.description,
				category: row.predictions.category,
				predictionType: row.predictions.predictionType,
				targetValue,
				targetDate: row.predictions.targetDate,
				confidenceLevel: row.predictions.confidenceLevel ? Math.round(row.predictions.confidenceLevel * 100) : null,
				status: row.predictions.status,
				tags,
				extractedAt: row.predictions.extractedAt,
				createdAt: row.predictions.createdAt,
				// Verification-specific fields
				daysSinceTarget,
				verificationPriority: daysSinceTarget > 30 ? 'high' : daysSinceTarget > 7 ? 'medium' : 'low',
				isPastDue: daysSinceTarget > 0,
				economist: {
					id: row.economists.id,
					name: row.economists.name,
					avatarUrl: row.economists.avatarUrl,
					isVerified: row.economists.isVerified,
					trustScore: row.economists.trustScore ? Math.round(row.economists.trustScore * 100) : null
				},
				video: {
					id: row.videos.id,
					title: row.videos.title,
					youtubeId: row.videos.youtubeVideoId,
					thumbnailUrl: row.videos.thumbnailUrl,
					publishedAt: row.videos.publishedAt,
					youtubeUrl: `https://www.youtube.com/watch?v=${row.videos.youtubeVideoId}`
				}
			};
		});

		// Calculate verification statistics
		const highPriorityCount = transformedData.filter(p => p.verificationPriority === 'high').length;
		const mediumPriorityCount = transformedData.filter(p => p.verificationPriority === 'medium').length;
		const lowPriorityCount = transformedData.filter(p => p.verificationPriority === 'low').length;

		const categoryCounts = transformedData.reduce((acc: Record<string, number>, prediction) => {
			acc[prediction.category] = (acc[prediction.category] || 0) + 1;
			return acc;
		}, {});

		return c.json({
			success: true,
			data: transformedData,
			pagination: {
				page,
				limit,
				totalCount,
				totalPages,
				hasNextPage: page < totalPages,
				hasPreviousPage: page > 1
			},
			verificationStats: {
				totalNeedingVerification: totalCount,
				highPriority: highPriorityCount,
				mediumPriority: mediumPriorityCount,
				lowPriority: lowPriorityCount,
				categoryBreakdown: categoryCounts
			},
			message: 'Predictions needing verification fetched successfully'
		});
	} catch (error) {
		console.error('Error fetching predictions for verification:', error);

		return c.json({
			success: false,
			error: 'Failed to fetch predictions for verification',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

export default verificationQueueRouter;
