-- Create E2E test users
-- These users are needed for the E2E tests to run properly

-- First test user
INSERT OR REPLACE INTO users (email, username, password_hash, role, is_active, email_verified, created_at)
VALUES (
  '<EMAIL>',
  'frontend-test1',
  '$2b$12$0sFy5VXbqhV58SkCW488I.MvO/uWo8GQMT0oaiIf5o9wvsedILj3i', -- bcrypt hash of 'testpass123'
  'user',
  1,
  1,
  datetime('now')
);

-- Second test user
INSERT OR REPLACE INTO users (email, username, password_hash, role, is_active, email_verified, created_at)
VALUES (
  '<EMAIL>',
  'e2e-test',
  '$2b$12$0sFy5VXbqhV58SkCW488I.MvO/uWo8GQMT0oaiIf5o9wvsedILj3i', -- bcrypt hash of 'testpass123'
  'user',
  1,
  1,
  datetime('now')
);

-- Admin test user for admin panel tests
INSERT OR REPLACE INTO users (email, username, password_hash, role, is_active, email_verified, created_at)
VALUES (
  '<EMAIL>',
  'admin-test',
  '$2b$12$0sFy5VXbqhV58SkCW488I.MvO/uWo8GQMT0oaiIf5o9wvsedILj3i', -- bcrypt hash of 'testpass123'
  'admin',
  1,
  1,
  datetime('now')
);

-- Moderator test user for testing moderator access
INSERT OR REPLACE INTO users (email, username, password_hash, role, is_active, email_verified, created_at)
VALUES (
  '<EMAIL>',
  'moderator-test',
  '$2b$12$0sFy5VXbqhV58SkCW488I.MvO/uWo8GQMT0oaiIf5o9wvsedILj3i', -- bcrypt hash of 'testpass123'
  'moderator',
  1,
  1,
  datetime('now')
);
