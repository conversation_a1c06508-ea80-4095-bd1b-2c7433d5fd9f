import { describe, it, expect, beforeAll, afterAll } from 'vitest';

/**
 * Integration tests for Authentication API endpoints with versioning
 * These tests verify the API endpoints work correctly with proper request/response formats
 */

const BASE_URL = 'http://localhost:4321';

interface ApiResponse<T = unknown> {
  status: 'success' | 'error' | 'fail';
  message?: string;
  data?: T;
  details?: string[];
}

describe('Authentication API Integration Tests with Versioning', () => {
  const existingUser = {
    email: '<EMAIL>',
    password: 'testpass123'
  };

  // Test user for registration flow
  const timestamp = Date.now();
  const newTestUser = {
    name: 'Integration Test User',
    email: `integration-test-${timestamp}@example.com`,
    password: 'testpass123'
  };

  // Helper function to extract auth token from Set-Cookie header
  function extractAuthToken(setCookieHeader: string | null): string {
    if (!setCookieHeader) return '';
    const match = setCookieHeader.match(/auth-token=([^;]+)/);
    return match ? match[1] : '';
  }

  beforeAll(async () => {
    console.log('Starting authentication API integration tests with versioning...');
  });

  afterAll(async () => {
    console.log('Cleaning up authentication API integration tests with versioning...');
  });

  it('should check authentication status when not logged in', async () => {
    const response = await fetch(`${BASE_URL}/api/v1/auth/me`);

    expect(response.status).toBe(401);

    const data = await response.json() as ApiResponse;
    expect(data.status).toBe('error');
    expect(data.message).toBe('You must be logged in to access this resource');
  });

  it('should register a new user successfully', async () => {
    const response = await fetch(`${BASE_URL}/api/v1/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newTestUser)
    });

    const data = await response.json() as ApiResponse;

    expect(response.status).toBe(201);
    expect(data.status).toBe('success');
    expect(data.message).toContain('Registration successful');

    // Note: Auth token is extracted and used within individual tests as needed
  });

  it('should authenticate with valid credentials', async () => {
    const response = await fetch(`${BASE_URL}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: existingUser.email,
        password: existingUser.password
      })
    });

    expect(response.status).toBe(200);

    const data = await response.json() as ApiResponse;
    expect(data.status).toBe('success');
    expect(data.message).toBe('Login successful');
    expect(data.data).toBeDefined();
    if (data.data && typeof data.data === 'object' && 'user' in data.data) {
      const userData = data.data as { user: { email: string } };
      expect(userData.user).toBeDefined();
      expect(userData.user.email).toBe(existingUser.email);
    }

    // Note: Auth token is extracted and used within individual tests as needed
  });

  it('should access authenticated endpoint with valid token', async () => {
    // First login to get a token
    const loginResponse = await fetch(`${BASE_URL}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: existingUser.email,
        password: existingUser.password
      })
    });

    const setCookieHeader = loginResponse.headers.get('set-cookie');
    const token = extractAuthToken(setCookieHeader);

    // Now test authenticated endpoint
    const response = await fetch(`${BASE_URL}/api/v1/auth/me`, {
      headers: {
        'Cookie': `auth-token=${token}`
      }
    });

    const data = await response.json() as ApiResponse;

    expect(response.status).toBe(200);
    expect(data.status).toBe('success');
    expect(data.data).toBeDefined();
    if (data.data && typeof data.data === 'object' && 'user' in data.data) {
      const userData = data.data as { user: { email: string } };
      expect(userData.user.email).toBe(existingUser.email);
    }
  });

  it('should reject invalid login credentials', async () => {
    const response = await fetch(`${BASE_URL}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: existingUser.email,
        password: 'wrongpassword'
      })
    });

    expect(response.status).toBe(401);

    const data = await response.json() as ApiResponse;
    expect(data.status).toBe('error');
    expect(data.message).toBe('Invalid credentials');
  });

  it('should logout successfully', async () => {
    const response = await fetch(`${BASE_URL}/api/v1/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    expect(response.status).toBe(200);

    const data = await response.json() as ApiResponse;
    expect(data.status).toBe('success');
    expect(data.message).toBe('Logout successful');
  });

  it('should verify API endpoints use correct versioning', async () => {
    // Test that non-versioned endpoints return 404
    const oldResponse = await fetch(`${BASE_URL}/api/auth/me`);
    expect(oldResponse.status).toBe(404);

    // Test that versioned endpoints exist
    const versionedResponse = await fetch(`${BASE_URL}/api/v1/auth/me`);
    expect(versionedResponse.status).toBe(401); // Should be 401 (not authenticated), not 404 (not found)
  });

  it('should verify response format consistency', async () => {
    const response = await fetch(`${BASE_URL}/api/v1/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const data = await response.json() as ApiResponse;

    // All API responses should follow the standard format
    expect(data).toHaveProperty('status');
    expect(data).toHaveProperty('message');
    expect(['success', 'error', 'fail']).toContain(data.status);
  });
});
