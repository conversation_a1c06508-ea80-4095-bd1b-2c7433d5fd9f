---
// src/components/astro/Logo.astro
const { class: className, ...rest } = Astro.props;
---
<svg
  width="180"
  height="40"
  viewBox="0 0 180 40"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
  class:list={[className]} {...rest}
>
  <circle cx="20" cy="20" r="18" fill="#D97706" stroke="#92400E" stroke-width="2"/>

  <path d="M8 24L12 20L16 22L20 18L24 14L28 16L32 12" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  <circle cx="12" cy="20" r="1.5" fill="white"/>
  <circle cx="16" cy="22" r="1.5" fill="white"/>
  <circle cx="20" cy="18" r="1.5" fill="white"/>
  <circle cx="24" cy="14" r="1.5" fill="white"/>
  <circle cx="28" cy="16" r="1.5" fill="white"/>
  <circle cx="32" cy="12" r="1.5" fill="white"/>

  <text x="45" y="16" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="700" fill="currentColor">Ekonomist</text>
  <text x="45" y="30" font-family="Inter, system-ui, sans-serif" font-size="12" font-weight="500" fill="currentColor" opacity="0.8">Güven Skoru</text>
</svg>