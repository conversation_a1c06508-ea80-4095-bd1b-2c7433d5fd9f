/**
 * Shared Formatters
 * 
 * Common formatting utilities used across different domain services.
 * These functions provide consistent formatting for dates, numbers, and other data types.
 */

/**
 * Format duration in seconds to MM:SS format
 */
export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Format view count with Turkish locale
 */
export function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M görüntülenme`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K görüntülenme`;
  }
  return `${count} görüntülenme`;
}

/**
 * Format relative date in Turkish
 */
export function formatRelativeDate(date: Date): string {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return 'Bugün';
  } else if (diffInDays === 1) {
    return 'Dün';
  } else if (diffInDays < 7) {
    return `${diffInDays} gün önce`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `${weeks} hafta önce`;
  } else if (diffInDays < 365) {
    const months = Math.floor(diffInDays / 30);
    return `${months} ay önce`;
  } else {
    const years = Math.floor(diffInDays / 365);
    return `${years} yıl önce`;
  }
}

/**
 * Format subscriber count with Turkish locale
 */
export function formatSubscriberCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M abone`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K abone`;
  }
  return `${count} abone`;
}

/**
 * Generate initials from a name
 */
export function generateInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2);
}

/**
 * Format accuracy score as percentage
 */
export function formatAccuracyScore(score: number): string {
  return `${Math.round(score * 100)}%`;
}

/**
 * Format trust score as percentage
 */
export function formatTrustScore(score: number): string {
  return `${Math.round(score * 100)}%`;
}

/**
 * Format YouTube URL from video ID
 */
export function formatYouTubeUrl(videoId: string): string {
  return `https://www.youtube.com/watch?v=${videoId}`;
}

/**
 * Format YouTube channel URL from channel ID
 */
export function formatYouTubeChannelUrl(channelId: string): string {
  return `https://www.youtube.com/channel/${channelId}`;
}

/**
 * Format thumbnail URL for YouTube video
 */
export function formatThumbnailUrl(videoId: string, quality: 'default' | 'medium' | 'high' | 'maxres' = 'medium'): string {
  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;
}

/**
 * Safely parse JSON string
 */
export function safeJsonParse<T>(jsonString: string | null, fallback: T): T {
  if (!jsonString) return fallback;
  
  try {
    return JSON.parse(jsonString) as T;
  } catch {
    return fallback;
  }
}

/**
 * Format date to ISO string safely
 */
export function formatDateToISO(date: Date | string | number | null): string | null {
  if (!date) return null;
  
  try {
    if (typeof date === 'number') {
      // Handle Unix timestamp (seconds)
      return new Date(date * 1000).toISOString();
    }
    return new Date(date).toISOString();
  } catch {
    return null;
  }
}

/**
 * Convert boolean-like values to actual boolean
 */
export function normalizeBoolean(value: unknown): boolean {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'number') return value === 1;
  if (typeof value === 'string') return value.toLowerCase() === 'true' || value === '1';
  return false;
}
