import { test, expect } from '@playwright/test';

// Test configuration
const existingUser = {
	email: '<EMAIL>',
	password: 'testpass123',
	username: 'e2e-test'
};

test.describe('Header Authentication Display Tests', () => {

	test('should show login/register buttons when not authenticated', async ({ page }) => {
		// Go to homepage (SSG page)
		await page.goto('/');

		// Wait for the page to load and ClientAuth to initialize
		await page.waitForTimeout(2000);

		// Check that login/register buttons are visible
		const loginButton = page.locator('a[href="/login"]').filter({ hasText: 'Giriş' });
		const registerButton = page.locator('a[href="/register"]').filter({ hasText: 'Kayıt Ol' });

		await expect(loginButton).toBeVisible();
		await expect(registerButton).toBeVisible();

		// Ensure user menu is not visible
		const userDropdown = page.locator('[data-user-dropdown-trigger]');
		await expect(userDropdown).not.toBeVisible();
	});

	test('should show username in header after login on SSG page', async ({ page }) => {
		// Login using UI flow
		await page.goto('/login');
		await page.fill('input[name="email"]', existingUser.email);
		await page.fill('input[name="password"]', existingUser.password);
		await page.click('button[type="submit"]');

		// Wait for login to complete and redirect
		await page.waitForLoadState('networkidle');
		await page.waitForTimeout(2000);

		// Now visit a SSG page (homepage)
		await page.goto('/');

		// Wait for ClientAuth to fetch user data and update the UI
		await page.waitForTimeout(3000);

		// Check that the user menu is visible with the username
		const userDropdown = page.locator('[data-user-dropdown-trigger]');
		await expect(userDropdown).toBeVisible();

		// Check that the username is displayed
		const usernameSpan = userDropdown.locator('span').filter({ hasText: existingUser.username });
		await expect(usernameSpan).toBeVisible();

		// Ensure login/register buttons are not visible
		const loginButton = page.locator('a[href="/login"]').filter({ hasText: 'Giriş' });
		const registerButton = page.locator('a[href="/register"]').filter({ hasText: 'Kayıt Ol' });

		await expect(loginButton).not.toBeVisible();
		await expect(registerButton).not.toBeVisible();
	});

	test('should show username in header after login on SSR page', async ({ page }) => {
		// Login using UI flow
		await page.goto('/login');
		await page.fill('input[name="email"]', existingUser.email);
		await page.fill('input[name="password"]', existingUser.password);
		await page.click('button[type="submit"]');

		// Wait for login to complete and redirect
		await page.waitForLoadState('networkidle');
		await page.waitForTimeout(2000);

		// Visit a SSR page (dashboard)
		await page.goto('/dashboard');

		// Wait for ClientAuth to fetch user data and update the UI
		await page.waitForTimeout(3000);

		// Check that the user menu is visible with the username
		const userDropdown = page.locator('[data-user-dropdown-trigger]');
		await expect(userDropdown).toBeVisible();

		// Check that the username is displayed
		const usernameSpan = userDropdown.locator('span').filter({ hasText: existingUser.username });
		await expect(usernameSpan).toBeVisible();
	});

	test('should show user dropdown menu items when authenticated', async ({ page }) => {
		// Login using UI flow
		await page.goto('/login');
		await page.fill('input[name="email"]', existingUser.email);
		await page.fill('input[name="password"]', existingUser.password);
		await page.click('button[type="submit"]');

		// Wait for login to complete and redirect
		await page.waitForLoadState('networkidle');
		await page.waitForTimeout(2000);

		// Visit homepage
		await page.goto('/');

		// Wait for ClientAuth to initialize
		await page.waitForTimeout(3000);

		// Hover over the user dropdown to make menu visible
		const userDropdown = page.locator('[data-user-dropdown-trigger]');
		await userDropdown.hover();

		// Check that dropdown menu items are visible
		const panelLink = page.locator('a[href="/dashboard"]').filter({ hasText: 'Panel' });
		const profileLink = page.locator('a[href="/profile"]').filter({ hasText: 'Profil' });
		const logoutButton = page.locator('[data-logout-btn]').filter({ hasText: 'Çıkış Yap' });

		await expect(panelLink).toBeVisible();
		await expect(profileLink).toBeVisible();
		await expect(logoutButton).toBeVisible();
	});

	test('should logout and update header UI correctly', async ({ page, browserName }) => {
		// Skip this test for WebKit due to automation/hydration issues after logout
		test.skip(browserName === 'webkit', 'WebKit automation cannot reliably detect logout UI state after redirect.');

		// Login using UI flow
		await page.goto('/login');
		await page.fill('input[name="email"]', existingUser.email);
		await page.fill('input[name="password"]', existingUser.password);
		await page.click('button[type="submit"]');

		// Wait for login to complete and redirect
		await page.waitForLoadState('networkidle');
		await page.waitForTimeout(2000);

		// Visit homepage
		await page.goto('/');

		// Wait for ClientAuth to initialize
		await page.waitForTimeout(3000);

		// Verify user is logged in
		const userDropdown = page.locator('[data-user-dropdown-trigger]');
		await expect(userDropdown).toBeVisible();

		// Hover over user dropdown and click logout
		await userDropdown.hover();
		const logoutButton = page.locator('[data-logout-btn]');
		await logoutButton.click();

		// Wait for logout to complete and page to redirect/reload
		await page.waitForTimeout(2000);

		// Check that login/register buttons are now visible
		const loginButton = page.locator('a[href="/login"]').filter({ hasText: 'Giriş' });
		const registerButton = page.locator('a[href="/register"]').filter({ hasText: 'Kayıt Ol' });

		await expect(loginButton).toBeVisible();
		await expect(registerButton).toBeVisible();

		// Ensure user dropdown is no longer visible
		await expect(userDropdown).not.toBeVisible();
	});

	test('should handle auth state changes across different page types', async ({ page }) => {
		// Test navigation between SSG and SSR pages while authenticated

		// Login using UI flow (more reliable than direct API call)
		await page.goto('/login');
		await page.fill('input[name="email"]', existingUser.email);
		await page.fill('input[name="password"]', existingUser.password);
		await page.click('button[type="submit"]');

		// Wait for login to complete and redirect (could be dashboard or home)
		await page.waitForLoadState('networkidle');
		await page.waitForTimeout(2000);

		// Start on SSG page (homepage)
		await page.goto('/');
		await page.waitForTimeout(2000);

		// Verify username is shown
		let userDropdown = page.locator('[data-user-dropdown-trigger]');
		await expect(userDropdown).toBeVisible();
		let usernameSpan = userDropdown.locator('span').filter({ hasText: existingUser.username });
		await expect(usernameSpan).toBeVisible();

		// Navigate to SSR page (dashboard)
		await page.goto('/dashboard');
		await page.waitForTimeout(2000);

		// Verify username is still shown
		userDropdown = page.locator('[data-user-dropdown-trigger]');
		await expect(userDropdown).toBeVisible();
		usernameSpan = userDropdown.locator('span').filter({ hasText: existingUser.username });
		await expect(usernameSpan).toBeVisible();

		// Navigate back to SSG page (predictions)
		await page.goto('/predictions');
		await page.waitForTimeout(2000);

		// Verify username is still shown
		userDropdown = page.locator('[data-user-dropdown-trigger]');
		await expect(userDropdown).toBeVisible();
		usernameSpan = userDropdown.locator('span').filter({ hasText: existingUser.username });
		await expect(usernameSpan).toBeVisible();
	});
});
