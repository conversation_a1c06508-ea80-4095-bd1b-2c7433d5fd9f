import { test, expect } from '@playwright/test';

test.describe('Homepage Dynamic Data Loading', () => {
	test('should load homepage components', async ({ page }) => {
		await page.goto('/');

		// Wait for the API call to complete and data to load
		await page.waitForLoadState('networkidle');

		// Check for main title to verify page loaded
		await expect(page).toHaveTitle("Ana Sayfa - YouTube Economist Trust Score");

		// Check for Platform Stats specific text
		await expect(page.locator('text=Platform İstatistikleri')).toBeVisible();
		await expect(page.locator('text=Editörlük Makaleler')).toBeVisible();
	});

	test('should handle API failures gracefully', async ({ page }) => {
		// Intercept all API calls that might be made and make them fail
		await page.route('**/api/**', route => {
			route.fulfill({
				status: 500,
				contentType: 'application/json',
				body: JSON.stringify({ error: 'Internal server error' })
			});
		});

		await page.goto('/');
		await page.waitForLoadState('networkidle');

		// Check that the page loads with basic content
		await expect(page).toHaveTitle("Ana Sayfa - YouTube Economist Trust Score");

		// Verify static content is still visible despite API failures
		await expect(page.locator('h2:has-text("Platform İstatistikleri")')).toBeVisible();
	});

	test('should display economist data from API in UI', async ({ page }) => {
		await page.goto('/');
		await page.waitForLoadState('networkidle');

		// Check that economist data is displayed in the UI
		// This verifies the API is working by checking the UI result
		const economistCards = page.locator('[href^="/economists/"]');
		const economistCount = await economistCards.count();

		if (economistCount > 0) {
			// Verify economist cards have expected content
			const firstCard = economistCards.first();
			await expect(firstCard).toBeVisible();

			// Check for typical economist card content (accuracy scores, predictions)
			await expect(page.locator('text=/tahmin|doğru|güven|%/i').first()).toBeVisible();
		} else {
			// If no economist cards, at least verify the page structure loaded
			await expect(page.locator('text=Platform İstatistikleri')).toBeVisible();
		}
	});
});
