import type { APIRoute } from 'astro';
import app from '@/server/api';

export const prerender = false;

const handler: APIRoute = async ({ request, locals }) => {
	// Pass the database connection from Astro's locals to the request
	// This allows Hono handlers to access the database
	const requestWithDb = new Request(request);

	// Store the database connection in a way that Hon<PERSON> can access it
	// We'll use a property on the request object
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	(requestWithDb as any).__astroLocals = locals;

	return app.fetch(requestWithDb);
};

export const GET = handler;
export const POST = handler;
export const PUT = handler;
export const DELETE = handler;
export const PATCH = handler;
