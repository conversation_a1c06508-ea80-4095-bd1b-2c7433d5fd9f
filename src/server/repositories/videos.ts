/**
 * Videos Repository
 *
 * Data access layer for videos. Contains both Drizzle ORM queries and raw SQL.
 * This is the single source of truth for all video database operations.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import type { DbConnection } from '@/server/db';
import { videos, economists, predictions } from '@/server/db';
import { eq, desc, and, inArray } from 'drizzle-orm';
import { execSync } from 'child_process';

// ============================================================================
// RAW SQL QUERIES (for build scripts and external tools)
// ============================================================================

export const VIDEOS_SQL = {
	recentWithEconomist: (limit = 5) =>
		`
    SELECT
      v.id,
      v.title,
      v.youtube_video_id as youtubeVideoId,
      v.published_at as publishedAt,
      v.economist_id as economistId,
      v.is_active as isActive,
      e.name as economistName,
      e.avatar_url as economistAvatarUrl
    FROM videos v
    INNER JOIN economists e ON v.economist_id = e.id
    WHERE e.is_active = 1 AND v.is_active = 1
    ORDER BY v.published_at DESC
    LIMIT ${limit}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	byEconomist: (economistId: number, limit = 10) =>
		`
    SELECT
      id,
      title,
      youtube_video_id as youtubeVideoId,
      published_at as publishedAt,
      economist_id as economistId,
      is_active as isActive
    FROM videos
    WHERE economist_id = ${economistId} AND is_active = 1
    ORDER BY published_at DESC
    LIMIT ${limit}
  `
			.replace(/\s+/g, ' ')
			.trim(),
} as const;

// ============================================================================
// DRIZZLE ORM QUERIES
// ============================================================================

/**
 * Get recent videos with economist information
 */
export async function findRecentVideosWithEconomist(db: DbConnection, limit = 6) {
	return await db
		.select()
		.from(videos)
		.innerJoin(economists, eq(videos.economistId, economists.id))
		.where(and(eq(economists.isActive, true), eq(videos.isActive, true)))
		.orderBy(desc(videos.publishedAt))
		.limit(limit);
}

/**
 * Get videos by economist
 */
export async function findVideosByEconomist(db: DbConnection, economistId: number, limit = 10) {
	return await db
		.select()
		.from(videos)
		.where(and(eq(videos.economistId, economistId), eq(videos.isActive, true)))
		.orderBy(desc(videos.publishedAt))
		.limit(limit);
}

/**
 * Get video by ID
 */
export async function findVideoById(db: DbConnection, id: number) {
	const result = await db
		.select()
		.from(videos)
		.where(and(eq(videos.id, id), eq(videos.isActive, true)))
		.limit(1);

	return result[0] || null;
}

/**
 * Get predictions for videos
 */
export async function findPredictionsForVideos(db: DbConnection, videoIds: number[]) {
	if (videoIds.length === 0) return [];

	return await db.select().from(predictions).where(inArray(predictions.videoId, videoIds));
}

// ============================================================================
// RAW SQL EXECUTION (for build scripts)
// ============================================================================

/**
 * Execute raw SQL query for videos (used by build scripts)
 */
export function executeVideoSQL(query: string, dbName: string, isLocal = false): unknown {
	const localFlag = isLocal ? '--local' : '--remote';
	const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

	try {
		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Video SQL execution failed:', error);
		return [];
	}
}

/**
 * Get recent videos using raw SQL (for build-time)
 */
export function getRecentVideosSQL(dbName: string, isLocal = false, limit = 5) {
	return executeVideoSQL(VIDEOS_SQL.recentWithEconomist(limit), dbName, isLocal);
}

/**
 * Get videos by economist using raw SQL (for build-time)
 */
export function getVideosByEconomistSQL(
	dbName: string,
	economistId: number,
	isLocal = false,
	limit = 10
) {
	return executeVideoSQL(VIDEOS_SQL.byEconomist(economistId, limit), dbName, isLocal);
}

// ============================================================================
// FORMATTED BUILD DATA FUNCTIONS
// ============================================================================

/**
 * Get videos data for build scripts (formatted)
 */
export function getVideosBuildData(dbName: string, isLocal = false, limit = 20) {
	const rawVideos = executeVideoSQL(
		VIDEOS_SQL.recentWithEconomist(limit),
		dbName,
		isLocal
	) as any[];

	if (!Array.isArray(rawVideos)) {
		return { videos: [], totalCount: 0 };
	}

	const formattedVideos = rawVideos.map((video) => ({
		id: video.id,
		title: video.title,
		videoTitle: video.title,
		youtubeVideoId: video.youtubeVideoId,
		thumbnailUrl:
			video.thumbnailUrl || `https://img.youtube.com/vi/${video.youtubeVideoId}/mqdefault.jpg`,
		duration: video.duration || 0,
		viewCount: video.viewCount || 0,
		publishedAt: new Date(video.publishedAt * 1000).toISOString(),
		publishDate: formatRelativeDate(new Date(video.publishedAt * 1000)),
		youtubeUrl: `https://www.youtube.com/watch?v=${video.youtubeVideoId}`,
		economistId: video.economistId,
		economistName: video.economistName,
		economistAvatarUrl: video.economistAvatarUrl,
		economistChannelName: video.economistChannelName,
		isActive: Boolean(video.isActive || 1),
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString(),
		economist: {
			id: video.economistId,
			name: video.economistName,
			avatarUrl: video.economistAvatarUrl,
			initials: video.economistName
				.split(' ')
				.map((n: string) => n.charAt(0))
				.join('')
				.toUpperCase(),
			title: 'Ekonomi Uzmanı',
		},
		predictions: [],
	}));

	return {
		videos: formattedVideos,
		totalCount: formattedVideos.length,
	};
}

/**
 * Get videos by economist for build scripts (formatted)
 */
export function getVideosByEconomistBuildData(
	dbName: string,
	economistId: number,
	isLocal = false,
	limit = 10
) {
	const rawVideos = executeVideoSQL(
		VIDEOS_SQL.byEconomist(economistId, limit),
		dbName,
		isLocal
	) as any[];

	if (!Array.isArray(rawVideos)) {
		return [];
	}

	return rawVideos.map((video) => ({
		id: video.id,
		title: video.title,
		youtubeVideoId: video.youtubeVideoId,
		thumbnailUrl: video.thumbnailUrl,
		publishedAt: new Date(video.publishedAt * 1000).toISOString(),
		predictionCount: video.predictionCount || 0,
	}));
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Format relative date
 */
function formatRelativeDate(date: Date): string {
	const now = new Date();
	const diffInMs = now.getTime() - date.getTime();
	const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

	if (diffInDays === 0) {
		return 'Bugün';
	} else if (diffInDays === 1) {
		return 'Dün';
	} else if (diffInDays < 7) {
		return `${diffInDays} gün önce`;
	} else if (diffInDays < 30) {
		const weeks = Math.floor(diffInDays / 7);
		return `${weeks} hafta önce`;
	} else if (diffInDays < 365) {
		const months = Math.floor(diffInDays / 30);
		return `${months} ay önce`;
	} else {
		const years = Math.floor(diffInDays / 365);
		return `${years} yıl önce`;
	}
}
