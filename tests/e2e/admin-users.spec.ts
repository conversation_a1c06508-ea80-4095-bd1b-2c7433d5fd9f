import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.describe('Admin Users Management', () => {
  // Helper function to login as admin
  async function loginAsAdmin(page: Page) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'testpass123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  }

  // Helper function to login as moderator
  async function loginAsModerator(page: Page) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'testpass123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  }

  // Helper function to login as regular user
  async function loginAsUser(page: Page) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'testpass123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  }

  test.describe('Access Control', () => {
    test('should allow admin access to user management page', async ({ page }) => {
      await loginAsAdmin(page);

      // Navigate to admin users page
      await page.goto('/admin/users');

      // Should successfully load the page
      await expect(page.getByRole('heading', { name: 'Kullanıcı Yönetimi' })).toBeVisible();
      await expect(page.locator('text=Kullanıcılar ve roller')).toBeVisible();
    });

    test('should deny moderator access to user management page', async ({ page }) => {
      await loginAsModerator(page);

      // Navigate to admin users page
      await page.goto('/admin/users');

      // Should be redirected back to admin dashboard with error
      await expect(page).toHaveURL(/\/admin\/dashboard.*error=access_denied/);
    });

    test('should deny regular user access to user management page', async ({ page }) => {
      await loginAsUser(page);

      // Navigate to admin users page
      await page.goto('/admin/users');

      // Should be redirected back to dashboard with access denied error
      // (since user is authenticated but lacks admin permissions)
      await expect(page).toHaveURL(/\/dashboard.*error=access_denied/);
    });
  });

  test.describe('User Interface', () => {
    test('should display user statistics correctly', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto('/admin/users');

      // Check that stats cards are present
      await expect(page.locator('text=Toplam Kullanıcı')).toBeVisible();
      await expect(page.locator('text=Aktif Kullanıcı')).toBeVisible();
      await expect(page.locator('text=Moderatörler')).toBeVisible();
      // Be more specific for "Askıya Alınmış" to avoid the dropdown option
      await expect(page.locator('p:has-text("Askıya Alınmış")')).toBeVisible();

      // Check that stats display numbers (not empty)
      const statsCards = page.locator('.text-3xl.font-bold');
      await expect(statsCards).toHaveCount(4);
    });

    test('should display user list table', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto('/admin/users');

      // Check that user table is present
      await expect(page.locator('text=Kullanıcı Listesi')).toBeVisible();
      await expect(page.locator('table')).toBeVisible();

      // Check table headers
      await expect(page.locator('th:has-text("Kullanıcı")')).toBeVisible();
      await expect(page.locator('th:has-text("Roller")')).toBeVisible();
      await expect(page.locator('th:has-text("Durum")')).toBeVisible();
      await expect(page.locator('th:has-text("Son Giriş")')).toBeVisible();
      await expect(page.locator('th:has-text("İşlemler")')).toBeVisible();
    });

    test('should show test users in the table', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto('/admin/users');

      // Should show the test users created by the setup script
      // We expect at least the admin-test user to be visible
      const userRows = page.locator('tbody tr');
      await expect(userRows.first()).toBeVisible();

      // Look for the admin test user specifically
      await expect(page.locator('text=<EMAIL>')).toBeVisible();
    });

    test('should navigate back to admin panel', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto('/admin/users');

      // Click the back to admin panel link
      await page.click('text=← Admin Panel');

      // Should navigate back to admin dashboard
      await expect(page).toHaveURL('/admin/dashboard');
    });
  });
});
