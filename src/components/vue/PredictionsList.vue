<template>
	<div class="space-y-6">
		<!-- <PERSON><PERSON> and <PERSON>ats -->
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
			<div class="flex flex-col md:flex-row md:items-center md:justify-between">
				<div>
					<h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
						Ekonomist Tahminleri
					</h2>
					<p class="text-gray-600 dark:text-gray-400">
						Türk ekonomistlerin video analizlerinden çıkarılan tahminler ve doğruluk oranları
					</p>
				</div>
				<div class="mt-4 md:mt-0 grid grid-cols-3 gap-4 text-center">
					<div>
						<div class="text-2xl font-bold text-amber-600 dark:text-amber-400">
							{{ stats.totalPredictions }}
						</div>
						<div class="text-sm text-gray-500 dark:text-gray-400">
							<PERSON>lam Tahmin
						</div>
					</div>
					<div>
						<div class="text-2xl font-bold text-green-600 dark:text-green-400">
							{{ stats.verifiedPredictions }}
						</div>
						<div class="text-sm text-gray-500 dark:text-gray-400">
							Doğrulanmış
						</div>
					</div>
					<div>
						<div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
							{{ stats.averageAccuracy ? stats.averageAccuracy + '%' : 'N/A' }}
						</div>
						<div class="text-sm text-gray-500 dark:text-gray-400">
							Ortalama Doğruluk
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Filters -->
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
			<div class="flex flex-wrap gap-2">
				<button
					class="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
					@click="clearFilters"
				>
					Tüm Tahminler
				</button>
				<button
					class="px-4 py-2 text-sm border border-yellow-300 dark:border-yellow-600 text-yellow-800 dark:text-yellow-200 rounded-md hover:bg-yellow-50 dark:hover:bg-yellow-900"
					@click="filterByStatus('pending')"
				>
					Beklemede
				</button>
				<button
					class="px-4 py-2 text-sm border border-green-300 dark:border-green-600 text-green-800 dark:text-green-200 rounded-md hover:bg-green-50 dark:hover:bg-green-900"
					@click="filterByStatus('verified_correct')"
				>
					Doğru
				</button>
				<button
					class="px-4 py-2 text-sm border border-red-300 dark:border-red-600 text-red-800 dark:text-red-200 rounded-md hover:bg-red-50 dark:hover:bg-red-900"
					@click="filterByStatus('verified_incorrect')"
				>
					Yanlış
				</button>
			</div>
		</div>

		<!-- Loading State -->
		<div v-if="loading" class="text-center py-8">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
			<p class="text-gray-600 dark:text-gray-400 mt-2">Tahminler yükleniyor...</p>
		</div>

		<!-- Error State -->
		<div v-else-if="error" class="text-center py-8">
			<div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-6">
				<p class="text-red-600 dark:text-red-400 mb-4">{{ error }}</p>
				<button
					class="px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700"
					@click="fetchPredictions()"
				>
					Yeniden Dene
				</button>
			</div>
		</div>

		<!-- Predictions Grid -->
		<div v-else-if="predictions.length > 0" class="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
			<PredictionCard
				v-for="prediction in predictions"
				:key="prediction.id"
				:prediction="prediction"
			/>
		</div>

		<!-- Empty State -->
		<div v-else class="text-center py-8">
			<div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-8">
				<p class="text-gray-600 dark:text-gray-400">Henüz hiç tahmin bulunamadı.</p>
			</div>
		</div>

		<!-- Pagination -->
		<div v-if="pagination.totalPages > 1" class="flex justify-center items-center space-x-2">
			<button
				class="px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 disabled:opacity-50 disabled:cursor-not-allowed"
				:disabled="!pagination.hasPreviousPage"
				@click="previousPage"
			>
				Önceki
			</button>

			<span class="px-4 py-2 text-gray-600 dark:text-gray-400">
				Sayfa {{ pagination.page }} / {{ pagination.totalPages }}
			</span>

			<button
				class="px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 disabled:opacity-50 disabled:cursor-not-allowed"
				:disabled="!pagination.hasNextPage"
				@click="nextPage"
			>
				Sonraki
			</button>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { usePredictions } from '@/composables/usePredictions';
import PredictionCard from './PredictionCard.vue';

const {
	predictions,
	loading,
	error,
	pagination,
	stats,
	fetchPredictions,
	nextPage,
	previousPage,
	filterByStatus,
	clearFilters,
} = usePredictions();

onMounted(() => {
	fetchPredictions();
});
</script>
