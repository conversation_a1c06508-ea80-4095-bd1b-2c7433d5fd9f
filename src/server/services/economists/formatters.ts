/**
 * Economists Formatters
 *
 * Formatting functions for economist data.
 * Transforms raw database data into frontend-ready formats.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import {
	generateInitials,
	formatAccuracyScore,
	formatTrustScore,
	formatSubscriberCount,
	formatYouTubeChannelUrl,
	formatDateToISO,
	normalizeBoolean,
} from '../shared/formatters';

// ============================================================================
// TYPES
// ============================================================================

export interface RawEconomist {
	id: number;
	name: string;
	bio: string | null;
	avatarUrl: string | null;
	youtubeChannelId?: string | null;
	youtubeChannelName: string;
	youtubeChannelUrl?: string | null;
	subscriberCount: number | null;
	accuracyScore: number | null;
	trustScore: number | null;
	totalPredictions: number | null;
	correctPredictions: number | null;
	isVerified?: boolean | number | null;
	isActive?: boolean | number | null;
	createdAt?: Date | string | number | null;
	updatedAt?: Date | string | number | null;
}

export interface FormattedEconomist {
	id: number;
	name: string;
	bio: string | null;
	avatarUrl: string | null;
	initials: string;
	youtubeChannelId: string | null;
	youtubeChannelName: string;
	youtubeChannelUrl: string | null;
	subscriberCount: number;
	subscriberCountText: string;
	accuracyScore: number;
	accuracyScoreText: string;
	trustScore: number;
	trustScoreText: string;
	totalPredictions: number;
	correctPredictions: number;
	verifiedPredictions: number;
	isVerified: boolean;
	isActive: boolean;
	createdAt: string;
	updatedAt: string;
	profileUrl: string;
	rank?: number;
}

export interface EconomistForHomepage {
	id: number;
	name: string;
	avatarUrl: string | null;
	initials: string;
	youtubeChannelName: string;
	accuracyScore: number;
	trustScore: number;
	totalPredictions: number;
	correctPredictions: number;
	bio: string | null;
	subscriberCount: number;
	profileUrl: string;
}

export interface EconomistListItem {
	id: number;
	name: string;
	avatarUrl: string | null;
	initials: string;
	youtubeChannelName: string;
	accuracyScore: number;
	trustScore: number;
	totalPredictions: number;
	correctPredictions: number;
	subscriberCount: number;
	profileUrl: string;
	rank?: number;
}

export interface EconomistForBuild {
	id: number;
	name: string;
	bio: string | null;
	avatarUrl: string | null;
	initials: string;
	youtubeChannelName: string;
	accuracyScore: number;
	trustScore: number;
	totalPredictions: number;
	correctPredictions: number;
	subscriberCount: number;
	isVerified: boolean;
	profileUrl: string;
}

// ============================================================================
// FORMATTERS
// ============================================================================

/**
 * Format economist for full display
 */
export function formatEconomist(economist: RawEconomist, stats?: any): FormattedEconomist {
	const subscriberCount = economist.subscriberCount || 0;
	const youtubeChannelUrl =
		economist.youtubeChannelUrl ||
		(economist.youtubeChannelId ? formatYouTubeChannelUrl(economist.youtubeChannelId) : null);

	return {
		id: economist.id,
		name: economist.name,
		bio: economist.bio,
		avatarUrl: economist.avatarUrl,
		initials: generateInitials(economist.name),
		youtubeChannelId: economist.youtubeChannelId || null,
		youtubeChannelName: economist.youtubeChannelName,
		youtubeChannelUrl,
		subscriberCount,
		subscriberCountText: formatSubscriberCount(subscriberCount),
		accuracyScore: economist.accuracyScore || 0,
		accuracyScoreText: formatAccuracyScore(economist.accuracyScore || 0),
		trustScore: economist.trustScore || 0,
		trustScoreText: formatTrustScore(economist.trustScore || 0),
		totalPredictions: stats?.total || economist.totalPredictions || 0,
		correctPredictions: stats?.correct || economist.correctPredictions || 0,
		verifiedPredictions: stats?.verified || 0,
		isVerified: normalizeBoolean(economist.isVerified),
		isActive: normalizeBoolean(economist.isActive),
		createdAt: formatDateToISO(economist.createdAt ?? null) || new Date().toISOString(),
		updatedAt: formatDateToISO(economist.updatedAt ?? null) || new Date().toISOString(),
		profileUrl: `/economists/${economist.id}`,
	};
}

/**
 * Format economist for homepage display (minimal fields)
 */
export function formatEconomistForHomepage(economist: RawEconomist): EconomistForHomepage {
	return {
		id: economist.id,
		name: economist.name,
		avatarUrl: economist.avatarUrl,
		initials: generateInitials(economist.name),
		youtubeChannelName: economist.youtubeChannelName,
		accuracyScore: economist.accuracyScore || 0,
		trustScore: economist.trustScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		correctPredictions: economist.correctPredictions || 0,
		bio: economist.bio,
		subscriberCount: economist.subscriberCount || 0,
		profileUrl: `/economists/${economist.id}`,
	};
}

/**
 * Format economist for list display
 */
export function formatEconomistListItem(economist: RawEconomist, rank?: number): EconomistListItem {
	return {
		id: economist.id,
		name: economist.name,
		avatarUrl: economist.avatarUrl,
		initials: generateInitials(economist.name),
		youtubeChannelName: economist.youtubeChannelName,
		accuracyScore: economist.accuracyScore || 0,
		trustScore: economist.trustScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		correctPredictions: economist.correctPredictions || 0,
		subscriberCount: economist.subscriberCount || 0,
		profileUrl: `/economists/${economist.id}`,
		rank,
	};
}

/**
 * Format economist for build-time data
 */
export function formatEconomistForBuild(economist: RawEconomist): EconomistForBuild {
	return {
		id: economist.id,
		name: economist.name,
		bio: economist.bio,
		avatarUrl: economist.avatarUrl,
		initials: generateInitials(economist.name),
		youtubeChannelName: economist.youtubeChannelName,
		accuracyScore: economist.accuracyScore || 0,
		trustScore: economist.trustScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		correctPredictions: economist.correctPredictions || 0,
		subscriberCount: economist.subscriberCount || 0,
		isVerified: normalizeBoolean(economist.isVerified),
		profileUrl: `/economists/${economist.id}`,
	};
}

/**
 * Format economist for API responses (with additional metadata)
 */
export function formatEconomistForAPI(
	economist: RawEconomist,
	stats?: any,
	rank?: number
): FormattedEconomist {
	const formatted = formatEconomist(economist, stats);
	return {
		...formatted,
		rank,
	};
}

/**
 * Format economist for search results
 */
export function formatEconomistForSearch(economist: RawEconomist) {
	return {
		id: economist.id,
		name: economist.name,
		bio: economist.bio,
		youtubeChannelName: economist.youtubeChannelName,
		accuracyScore: economist.accuracyScore || 0,
		totalPredictions: economist.totalPredictions || 0,
		profileUrl: `/economists/${economist.id}`,
		type: 'economist' as const,
	};
}

/**
 * Format economist for sitemap
 */
export function formatEconomistForSitemap(economist: RawEconomist) {
	return {
		url: `/economists/${economist.id}`,
		lastmod: formatDateToISO(economist.updatedAt ?? null) || new Date().toISOString(),
		changefreq: 'weekly' as const,
		priority: 0.8,
	};
}

/**
 * Format economist for RSS/feed
 */
export function formatEconomistForFeed(economist: RawEconomist) {
	return {
		id: economist.id,
		name: economist.name,
		description: economist.bio || `${economist.name} - ${economist.youtubeChannelName}`,
		link: `/economists/${economist.id}`,
		accuracyScore: economist.accuracyScore || 0,
		totalPredictions: economist.totalPredictions || 0,
	};
}

/**
 * Calculate and format economist ranking
 */
export function addRankingToEconomists<
	T extends { accuracyScore: number | null; totalPredictions: number | null },
>(economists: T[]): (T & { rank: number })[] {
	return economists
		.sort((a, b) => {
			// Primary sort: accuracy score (descending)
			const aAccuracy = a.accuracyScore || 0;
			const bAccuracy = b.accuracyScore || 0;
			if (bAccuracy !== aAccuracy) {
				return bAccuracy - aAccuracy;
			}
			// Secondary sort: total predictions (descending)
			const aPredictions = a.totalPredictions || 0;
			const bPredictions = b.totalPredictions || 0;
			return bPredictions - aPredictions;
		})
		.map((economist, index) => ({
			...economist,
			rank: index + 1,
		}));
}
