import { spawn, exec, ChildProcess } from 'child_process';
import { promisify } from 'util';
import { createServer } from 'net';
import { setTimeout } from 'timers';

const execAsync = promisify(exec);

// Server management with proper state tracking
interface ServerState {
  process: ChildProcess | null;
  startedByScript: boolean;
  port: number;
}

let serverState: ServerState = {
  process: null,
  startedByScript: false,
  port: 4321
};

export async function ensureDevServer(port = 4321): Promise<{ wasStartedByScript: boolean; process: ChildProcess | null }> {
  // Check if server is already running
  if (await isPortInUse(port)) {
    console.log(`🚀 Development server already running on port ${port} (will not be managed by script)`);
    serverState = {
      process: null,
      startedByScript: false,
      port
    };
    return { wasStartedByScript: false, process: null };
  }

  console.log(`🚀 Starting development server on port ${port}...`);

  const serverProcess = spawn('npm', ['run', 'dev'], {
    stdio: ['inherit', 'pipe', 'pipe'],
    env: { ...process.env, PORT: port.toString() }
  });

  // Store reference for cleanup
  serverState = {
    process: serverProcess,
    startedByScript: true,
    port
  };

  // Wait for server to be ready
  console.log(`⏳ Waiting for server to be ready on port ${port}...`);
  const serverReady = await waitForPort(port, 30, 1000);

  if (!serverReady) {
    console.error(`❌ Server failed to start on port ${port} within 30 seconds`);
    if (serverProcess && !serverProcess.killed) {
      serverProcess.kill('SIGTERM');
    }
    serverState = { process: null, startedByScript: false, port };
    return { wasStartedByScript: false, process: null };
  }

  console.log(`✅ Development server ready on port ${port} (started by script)`);
  return { wasStartedByScript: true, process: serverProcess };
}

export function cleanupIfStartedByScript(): void {
  if (serverState.startedByScript && serverState.process && !serverState.process.killed) {
    console.log(`🧹 Cleaning up server process started by script on port ${serverState.port}...`);
    try {
      serverState.process.kill('SIGTERM');
      console.log('✅ Server process terminated');
    } catch {
      console.log('Server process already terminated');
    }
  } else if (!serverState.startedByScript) {
    console.log('👍 Server was already running before script started - leaving it unchanged');
  }

  // Reset state
  serverState = { process: null, startedByScript: false, port: 4321 };
}

export async function isPortInUse(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = createServer();
    server.listen(port, () => {
      server.close(() => resolve(false));
    });
    server.on('error', () => resolve(true));
  });
}

export async function waitForPort(port: number, maxRetries = 30, delayMs = 1000): Promise<boolean> {
  for (let i = 0; i < maxRetries; i++) {
    if (await isPortInUse(port)) return true;
    await new Promise<void>(resolve => setTimeout(resolve, delayMs));
  }
  return false;
}

export async function killProcessOnPort(port: number): Promise<void> {
  console.log(`🧹 Checking for and terminating any process on port ${port}...`);
  try {
    const { stdout } = await execAsync(`lsof -ti :${port}`);
    const pid = stdout.trim();
    if (pid) {
      console.log(`🔪 Found process with PID ${pid} on port ${port}. Terminating forcefully (kill -9)...`);
      await execAsync(`kill -9 ${pid}`);
      console.log(`✅ Process ${pid} terminated.`);
    } else {
      console.log(`👍 Port ${port} is already free.`);
    }
  } catch {
    console.log(`👍 Port ${port} is already free.`);
  }
}

export async function runCommand(command: string, args: string[]): Promise<number> {
  return new Promise<number>((resolve) => {
    const process = spawn(command, args, { stdio: 'inherit' });
    process.on('close', (code) => resolve(code || 0));
    process.on('error', () => resolve(1));
  });
}

export { execAsync };
