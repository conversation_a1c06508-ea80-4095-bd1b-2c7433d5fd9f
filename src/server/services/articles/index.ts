/**
 * Articles Service
 *
 * Main service for articles domain. Provides high-level business logic operations
 * for articles, combining queries and formatters to deliver clean APIs.
 */

import type { DbConnection } from '@/server/db';
import type { QueryOptions, ServiceResult } from '../shared/types';

// Import queries from service layer
import { findActiveArticles, findArticleBySlug, findArticleById, countArticles } from './queries';

// Import formatters
import {
	formatArticle,
	formatArticleListItem,
	formatArticleForHomepage,
	formatArticleForRSS,
	formatArticleForSitemap,
	formatArticleForSearch,
	type FormattedArticle,
	type ArticleForBuild,
	type ArticleListItem,
} from './formatters';

// ============================================================================
// HIGH-LEVEL SERVICE FUNCTIONS
// ============================================================================

/**
 * Get curated articles (most common use case)
 */
export async function getCuratedArticles(
	db: DbConnection,
	limit = 10
): Promise<FormattedArticle[]> {
	const articles = await findActiveArticles(db, { limit, curated: true });
	return articles.map(formatArticle);
}

/**
 * Get recent articles for SSG
 */
export async function getRecentArticlesForSSG(
	db: DbConnection,
	limit = 20
): Promise<FormattedArticle[]> {
	const articles = await findActiveArticles(db, { limit, curated: false });
	return articles.map(formatArticle);
}

/**
 * Get formatted article by slug
 */
export async function getFormattedArticleBySlug(
	db: DbConnection,
	slug: string
): Promise<FormattedArticle | null> {
	const article = await findArticleBySlug(db, slug);
	return article ? formatArticle(article) : null;
}

/**
 * Get formatted article by ID
 */
export async function getFormattedArticleById(
	db: DbConnection,
	id: number
): Promise<FormattedArticle | null> {
	const article = await findArticleById(db, id);
	return article ? formatArticle(article) : null;
}

/**
 * Get articles for homepage display
 */
export async function getArticlesForHomepage(
	db: DbConnection,
	limit = 5
): Promise<ArticleListItem[]> {
	const articles = await findActiveArticles(db, { limit, curated: true });
	return articles.map(formatArticleForHomepage);
}

/**
 * Get articles with pagination
 */
export async function getArticlesWithPagination(
	db: DbConnection,
	options: QueryOptions = {}
): Promise<ServiceResult<FormattedArticle>> {
	const { page = 1, pageSize = 20 } = options;
	const offset = (page - 1) * pageSize;

	const [articles, total] = await Promise.all([
		findActiveArticles(db, { ...options, limit: pageSize, offset }),
		countArticles(db, options),
	]);

	const totalPages = Math.ceil(total / pageSize);

	return {
		data: articles.map(formatArticle),
		pagination: {
			page,
			pageSize,
			total,
			totalPages,
			hasNext: page < totalPages,
			hasPrev: page > 1,
		},
	};
}

/**
 * Get articles for RSS feed
 */
export async function getArticlesForRSS(db: DbConnection, limit = 50) {
	const articles = await findActiveArticles(db, { limit });
	return articles.map(formatArticleForRSS);
}

/**
 * Get articles for sitemap
 */
export async function getArticlesForSitemap(db: DbConnection) {
	const articles = await findActiveArticles(db, { limit: 1000 }); // Get all active articles
	return articles.map(formatArticleForSitemap);
}

/**
 * Get articles for search indexing
 */
export async function getArticlesForSearch(db: DbConnection) {
	const articles = await findActiveArticles(db, { limit: 1000 });
	return articles.map(formatArticleForSearch);
}

// ============================================================================
// ARTICLES SERVICE OBJECT
// ============================================================================

export const ArticlesService = {
	// High-level functions
	getCuratedArticles,
	getRecentArticlesForSSG,
	getFormattedArticleBySlug,
	getFormattedArticleById,
	getArticlesForHomepage,
	getArticlesWithPagination,
	getArticlesForRSS,
	getArticlesForSitemap,
	getArticlesForSearch,

	// Query functions (re-exported for convenience)
	findActiveArticles,
	findArticleBySlug,
	findArticleById,
	countArticles,

	// Formatter functions (re-exported for convenience)
	formatArticle,
	formatArticleListItem,
	formatArticleForHomepage,
	formatArticleForRSS,
	formatArticleForSitemap,
	formatArticleForSearch,
};

export default ArticlesService;

// Re-export types
export type { FormattedArticle, ArticleForBuild, ArticleListItem };
