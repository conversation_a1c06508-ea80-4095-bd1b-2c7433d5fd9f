import { test, expect } from '@playwright/test';

test.describe('Economists Pages E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Set viewport for consistent testing
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test.describe('Economists Listing Page', () => {
    test('should display economists listing page correctly', async ({ page }) => {
      await page.goto('/economists');

      // Check page title and heading
      await expect(page).toHaveTitle(/Ekonomistler/);
      await expect(page.locator('h1').first()).toContainText('Ekonomistler');

      // Check that economist cards are displayed
      const economistCards = page.locator('[href^="/economists/"]');
      await expect(economistCards.first()).toBeVisible();

      // Check that each card has essential information
      const firstCard = economistCards.first();
      await expect(firstCard).toBeVisible();

      // Check for economist name, predictions count, and trust score
      await expect(page.locator('p:has-text("tahmin")').first()).toBeVisible();
      await expect(page.locator('p:has-text("doğru")').first()).toBeVisible();
    });

    test('should navigate to economist detail page when clicking on economist card', async ({ page }) => {
      await page.goto('/economists');

      // Wait for economist cards to load
      const economistCard = page.locator('[href^="/economists/"]').first();
      await expect(economistCard).toBeVisible();

      // Click on the first economist card
      await economistCard.click();

      // Should navigate to economist detail page
      await expect(page).toHaveURL(/\/economists\/\d+/);
      await expect(page.locator('text=Son Tahminler')).toBeVisible();
    });

    test('should display economist rankings correctly', async ({ page }) => {
      await page.goto('/economists');

      // Check for ranking indicators (numbers 1, 2, 3, etc.)
      const rankingBadges = page.locator('.inline-flex.items-center.justify-center');
      await expect(rankingBadges.first()).toBeVisible();

      // Check that economists are displayed in order
      const economistNames = await page.locator('[href^="/economists/"] h3').allTextContents();
      expect(economistNames.length).toBeGreaterThan(0);
    });

    test('should be responsive on mobile devices', async ({ page }) => {
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/economists');

      // Check that the page is still functional on mobile
      await expect(page.locator('h1').first()).toBeVisible();
      await expect(page.locator('[href^="/economists/"]').first()).toBeVisible();

      // Check that cards stack properly on mobile
      const cards = page.locator('[href^="/economists/"]');
      const firstCardBox = await cards.first().boundingBox();
      const secondCardBox = await cards.nth(1).boundingBox();

      if (firstCardBox && secondCardBox) {
        // On mobile, cards should stack vertically
        expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height - 10);
      }
    });
  });

  test.describe('Economist Detail Page', () => {
    test('should display economist detail page correctly', async ({ page }) => {
      await page.goto('/economists/1');

      // Wait for page to load (SSG - no loading states)
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });

      // Check for main sections
      await expect(page.locator('text=Son Tahminler')).toBeVisible();
      await expect(page.locator('text=Son Videolar')).toBeVisible();
      await expect(page.locator('text=Performans İstatistikleri')).toBeVisible();
    });

    test('should load economist profile data correctly', async ({ page }) => {
      await page.goto('/economists/1');

      // Wait for main content to be visible (SSG)
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });

      // Check for economist profile elements
      await expect(page.locator('span:has-text("Güven Skoru:")').first()).toBeVisible();
      await expect(page.locator('text=Toplam Tahmin').first()).toBeVisible();
      await expect(page.locator('text=Doğrulanmış').first()).toBeVisible();
      await expect(page.locator('text=Doğru Tahmin').first()).toBeVisible();
      await expect(page.locator('text=Doğruluk Oranı').first()).toBeVisible();
    });

    test('should display predictions timeline correctly', async ({ page }) => {
      await page.goto('/economists/1');

      // Wait for main content to load (SSG)
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });

      // Check for predictions section
      await expect(page.locator('text=Son Tahminler')).toBeVisible();

      // Check for prediction content (either cards or empty state)
      const hasEmptyState = await page.locator('text=Henüz Tahmin Yok').isVisible();
      if (!hasEmptyState) {
        // If there are predictions, check for basic structure
        const predictionCards = page.locator('.bg-white.dark\\:bg-gray-800').filter({ hasText: 'Enflasyon' });
        if (await predictionCards.count() > 0) {
          await expect(predictionCards.first()).toBeVisible();
        }
      } else {
        // Verify empty state is shown
        await expect(page.locator('text=Henüz Tahmin Yok')).toBeVisible();
      }
    });

    test('should display videos section correctly', async ({ page }) => {
      await page.goto('/economists/1');

      // Wait for main content to load (SSG)
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });

      // Check for videos section
      await expect(page.locator('text=Son Videolar')).toBeVisible();

      // Check for video content (either cards or empty state)
      const hasEmptyState = await page.locator('text=Henüz Video Yok').isVisible();
      if (!hasEmptyState) {
        // If there are videos, check for YouTube links
        const youtubeLinks = page.locator('a[href*="youtube.com"]');
        if (await youtubeLinks.count() > 0) {
          await expect(youtubeLinks.first()).toBeVisible();
        }
      } else {
        // Verify empty state is shown
        await expect(page.locator('text=Henüz Video Yok')).toBeVisible();
      }
    });

    test('should display performance statistics correctly', async ({ page }) => {
      await page.goto('/economists/1');

      // Wait for main content to load (SSG)
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });

      // Check for performance statistics section
      await expect(page.locator('text=Performans İstatistikleri')).toBeVisible();
      await expect(page.locator('h3:has-text("Güven Skoru")').first()).toBeVisible();
      await expect(page.locator('text=Doğruluk Oranı').first()).toBeVisible();

      // Check for circular progress indicators (SVG elements) - SSG renders these statically
      const circularProgress = page.locator('svg circle[stroke-dasharray]');
      if (await circularProgress.count() > 0) {
        await expect(circularProgress.first()).toBeVisible();
      }
    });

    test('should handle loading states properly', async ({ page }) => {
      await page.goto('/economists/1');

      // SSG pages don't have loading states - content is pre-rendered
      // Just verify the page loads correctly
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });
      await expect(page.locator('text=Performans İstatistikleri')).toBeVisible();

      // Verify static content is immediately available
      await expect(page.locator('h3').filter({ hasText: 'Güven Skoru' })).toBeVisible();
    });

    test('should be responsive on mobile devices', async ({ page }) => {
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/economists/1');

      // Wait for content to load (SSG)
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });

      // Check that content is still accessible on mobile
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible();
      await expect(page.locator('text=Performans İstatistikleri')).toBeVisible();
    });

    test('should handle invalid economist IDs gracefully', async ({ page }) => {
      // Test with non-existent economist ID
      const response = await page.goto('/economists/99999');

      // Should either redirect to 404 or show error state
      if (response?.status() === 200) {
        // If page loads, check for error state
        await expect(page.locator('#error-state')).toBeVisible({ timeout: 5000 });
        await expect(page.locator('text=Ekonomist Bulunamadı')).toBeVisible();
      } else {
        // Should redirect to 404 or return error status
        expect([302, 404]).toContain(response?.status());
      }
    });
  });

  test.describe('Navigation Between Pages', () => {
    test('should navigate from homepage to economists listing', async ({ page }) => {
      await page.goto('/');

      // Look for links to economists page
      const economistsLink = page.locator('a[href="/economists"]');
      if (await economistsLink.count() > 0) {
        await economistsLink.first().click();
        await expect(page).toHaveURL('/economists');
        await expect(page.locator('h1').first()).toContainText('Ekonomistler');
      }
    });

    test('should navigate from homepage to economist detail pages', async ({ page }) => {
      await page.goto('/');

      // Look for direct links to economist detail pages from homepage
      const economistDetailLinks = page.locator('a[href^="/economists/"]');
      if (await economistDetailLinks.count() > 0) {
        await economistDetailLinks.first().click();
        await expect(page).toHaveURL(/\/economists\/\d+/);

        // Wait for any economist detail page to load (not specific to Dr. Mehmet Ekonomi)
        await expect(page.locator('text=Performans İstatistikleri')).toBeVisible({ timeout: 10000 });
        await expect(page.locator('text=Son Tahminler')).toBeVisible();
        await expect(page.locator('text=Son Videolar')).toBeVisible();
      }
    });

    test('should navigate back from economist detail to listing', async ({ page }) => {
      await page.goto('/economists/1');

      // Wait for page to load (SSG)
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });

      // Navigate back to economists listing using header navigation
      const headerEconomistsLink = page.locator('header a[href="/economists"]');
      if (await headerEconomistsLink.count() > 0) {
        await headerEconomistsLink.click();
        await expect(page).toHaveURL('/economists');
      } else {
        // Use browser back button
        await page.goBack();
        await expect(page).toHaveURL('/economists');
      }
    });
  });

  test.describe('Performance', () => {
    test('should load economists listing page quickly', async ({ page }) => {
      const startTime = Date.now();
      await page.goto('/economists');
      await expect(page.locator('h1').first()).toBeVisible();
      const loadTime = Date.now() - startTime;

      // Should load within reasonable time (static content should be fast)
      expect(loadTime).toBeLessThan(3000);
    });

    test('should load economist detail page quickly', async ({ page }) => {
      const startTime = Date.now();
      await page.goto('/economists/1');
      await expect(page.locator('h1').filter({ hasText: 'Dr. Mehmet Ekonomi' })).toBeVisible({ timeout: 10000 });
      const loadTime = Date.now() - startTime;

      // Should load within reasonable time
      expect(loadTime).toBeLessThan(10000);
    });
  });
});
