import { Hono } from 'hono';
import { getTopEconomists } from '@/server/services';
import { getDbFromContext } from '../../data';

const topEconomistsRouter = new Hono();

// GET /api/economists/top - Get top economists
topEconomistsRouter.get('/', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				status: 'error',
				message: 'Database connection not available'
			}, 503);
		}

		const limit = parseInt(c.req.query('limit') || '6');
		const economists = await getTopEconomists(db, limit);

		return c.json({
			status: 'success',
			data: economists,
			message: 'Top economists fetched successfully',
			count: economists.length,
		});
	} catch (error) {
		console.error('Error fetching top economists:', error);
		return c.json({
			status: 'error',
			message: 'Failed to fetch top economists',
		}, 500);
	}
});

export default topEconomistsRouter;
