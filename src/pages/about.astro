---
// This page will be pre-rendered at build time (SSG)
export const prerender = true;

import Layout from '../layouts/Layout.astro';
---

<Layout title="Hakkımızda - Ekonomist <PERSON><PERSON><PERSON><PERSON> Takipçisi">
	<main class="container mx-auto px-4 py-8 max-w-4xl">
		<h1 class="text-4xl font-bold text-center mb-8 text-gray-900 dark:text-white">
			Hakkımızda
		</h1>

		<div class="prose prose-lg dark:prose-invert mx-auto">
			<section class="mb-8">
				<h2 class="text-2xl font-semibold mb-4">Projemiz Nedir?</h2>
				<p class="text-gray-700 dark:text-gray-300 leading-relaxed">
					Ekonomist Doğruluk Takipçisi, Türkiye'deki YouTube ekonomistlerinin tahminlerinin
					doğruluğunu objektif bir şekilde ölçen ve takip eden bir platformdur.
					Amacımız, ekonomik öngörülerin güvenilirliğini şeffaf bir şekilde sunmaktır.
				</p>
			</section>

			<section class="mb-8">
				<h2 class="text-2xl font-semibold mb-4">Nasıl Çalışıyor?</h2>
				<ul class="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
					<li>YouTube ekonomistlerinin tahminlerini takip ediyoruz</li>
					<li>Gerçekleşen verilerle karşılaştırıyoruz</li>
					<li>Doğruluk oranlarını hesaplıyoruz</li>
					<li>Sonuçları şeffaf bir şekilde paylaşıyoruz</li>
				</ul>
			</section>

			<section class="mb-8">
				<h2 class="text-2xl font-semibold mb-4">Metodolojimiz</h2>
				<p class="text-gray-700 dark:text-gray-300 leading-relaxed">
					Tüm tahminler objektif kriterlerle değerlendirilir. Sadece somut,
					ölçülebilir öngörüler dikkate alınır. Değerlendirmelerimiz tamamen
					veri odaklıdır ve kişisel görüşlerden bağımsızdır.
				</p>
			</section>

			<section class="mb-8">
				<h2 class="text-2xl font-semibold mb-4">İletişim</h2>
				<p class="text-gray-700 dark:text-gray-300 leading-relaxed">
					Bu proje açık kaynak olarak geliştirilmektedir. Katkıda bulunmak
					veya geri bildirim vermek için GitHub sayfamızı ziyaret edebilirsiniz.
				</p>
			</section>
		</div>

		<div class="mt-12 text-center">
			<a
				href="/"
				class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
			>
				Ana Sayfaya Dön
			</a>
		</div>
	</main>
</Layout>

<style>
	.prose h2 {
		@apply text-gray-900 dark:text-white;
	}
</style>
