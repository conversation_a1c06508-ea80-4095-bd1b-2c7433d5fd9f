// @ts-check
import { defineConfig } from 'astro/config';
import vue from '@astrojs/vue';
import tailwind from '@astrojs/tailwind';
import cloudflare from '@astrojs/cloudflare';
import icon from 'astro-icon';
import { fileURLToPath, URL } from 'node:url';

// https://astro.build/config
export default defineConfig({
	// Static by default for optimal performance on Cloudflare Workers
	// API routes will automatically use server rendering
	output: 'static',

	integrations: [
		vue({
			template: {
				compilerOptions: {
					isCustomElement: (tag) => tag.includes('-'),
				},
			},
		}),
		tailwind(),
		icon(),
	],

	adapter: cloudflare({
		// Optimize image service for Cloudflare
		imageService: 'cloudflare',

		// Platform proxy for local development
		platformProxy: {
			enabled: true,
			configPath: 'wrangler.toml',
			persist: true,
		},
	}),

	vite: {
		resolve: {
			alias: {
				'@': fileURLToPath(new URL('./src', import.meta.url)),
				'@/components': fileURLToPath(new URL('./src/components', import.meta.url)),
				'@/features': fileURLToPath(new URL('./src/features', import.meta.url)),
				'@/layouts': fileURLToPath(new URL('./src/layouts', import.meta.url)),
				'@/server': fileURLToPath(new URL('./src/server', import.meta.url)),
				'@/services': fileURLToPath(new URL('./src/services', import.meta.url)),
				'@/stores': fileURLToPath(new URL('./src/stores', import.meta.url)),
				'@/utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
			},
			preserveSymlinks: true,
		},
		optimizeDeps: {
			include: ['vue', '@vue/runtime-core', '@vue/runtime-dom'],
		},
		ssr: {
			external: ['node:fs/promises', 'node:path', 'node:url', 'node:crypto', 'node:timers', 'util'],
		},
	},
});
