/**
 * Videos Formatters
 *
 * Formatting functions for video data.
 * Transforms raw database data into frontend-ready formats.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import {
	formatDuration,
	formatViewCount,
	formatRelativeDate,
	formatYouTubeUrl,
	formatThumbnailUrl,
	formatDateToISO,
	normalizeBoolean,
	generateInitials,
} from '../shared/formatters';

// ============================================================================
// TYPES
// ============================================================================

export interface RawVideo {
	id: number;
	title: string;
	youtubeVideoId: string;
	thumbnailUrl: string | null;
	duration: number | null;
	viewCount: number | null;
	publishedAt: Date | string | number;
	economistId: number;
	isActive: unknown;
	createdAt?: Date | string | number | null;
	updatedAt?: Date | string | number | null;
}

export interface RawVideoWithEconomist extends RawVideo {
	economistName: string;
	economistAvatarUrl: string | null;
	economistChannelName: string;
}

export interface FormattedVideo {
	id: number;
	title: string;
	youtubeVideoId: string;
	thumbnailUrl: string;
	duration: number;
	durationText: string;
	viewCount: number;
	viewCountText: string;
	publishedAt: string;
	publishDate: string;
	youtubeUrl: string;
	isActive: boolean;
	createdAt?: string;
	updatedAt?: string;
	economist?: {
		id: number;
		name: string;
		avatarUrl: string | null;
		initials: string;
		channelName: string;
		title: string;
	};
}

export interface VideoForHomepage {
	id: number;
	videoTitle: string;
	youtubeVideoId: string;
	thumbnailUrl: string | null;
	duration: string;
	viewCount: string;
	publishedAt: Date | null;
	publishDate: string;
	youtubeUrl: string;
	economist: {
		id: number;
		name: string;
		channelName: string;
		avatarUrl: string | null;
		initials: string;
		title: string;
	};
	predictions: any[];
}

export interface VideoWithPredictions extends FormattedVideo {
	predictions: Array<{
		id: number;
		title: string;
		description: string;
		category: string;
		predictionType: string;
		targetValue: string | null;
		targetDate: Date | null;
		confidenceLevel: number | null;
		status: string;
		accuracyScore: number | null;
	}>;
}

export interface VideoForBuild {
	id: number;
	videoTitle: string;
	youtubeVideoId: string;
	publishedAt: string;
	publishDate: string;
	youtubeUrl: string;
	economist: {
		id: number;
		name: string;
		avatarUrl: string | null;
		initials: string;
		title: string;
	};
	predictions: any[];
}

// ============================================================================
// FORMATTERS
// ============================================================================

/**
 * Format video data for API responses
 */
export function formatVideo(video: RawVideo, economist?: any): FormattedVideo {
	const duration = video.duration || 0;
	const viewCount = video.viewCount || 0;
	const publishedAt = formatDateToISO(video.publishedAt) || new Date().toISOString();
	const thumbnailUrl = video.thumbnailUrl || formatThumbnailUrl(video.youtubeVideoId);

	return {
		id: video.id,
		title: video.title,
		youtubeVideoId: video.youtubeVideoId,
		thumbnailUrl,
		duration,
		durationText: formatDuration(duration),
		viewCount,
		viewCountText: formatViewCount(viewCount),
		publishedAt,
		publishDate: formatRelativeDate(new Date(publishedAt)),
		youtubeUrl: formatYouTubeUrl(video.youtubeVideoId),
		isActive: normalizeBoolean(video.isActive),
		createdAt: video.createdAt ? formatDateToISO(video.createdAt) || undefined : undefined,
		updatedAt: video.updatedAt ? formatDateToISO(video.updatedAt) || undefined : undefined,
		economist: economist
			? {
					id: economist.id,
					name: economist.name,
					avatarUrl: economist.avatarUrl,
					initials: generateInitials(economist.name),
					channelName: economist.youtubeChannelName || economist.channelName,
					title: 'Ekonomi Uzmanı',
				}
			: undefined,
	};
}

/**
 * Format video with predictions
 */
export function formatVideoWithPredictions(
	video: RawVideo,
	economist: any,
	predictions: any[] = []
): VideoWithPredictions {
	const baseVideo = formatVideo(video, economist);

	return {
		...baseVideo,
		predictions: predictions.map((p) => ({
			id: p.id,
			title: p.title,
			description: p.description,
			category: p.category,
			predictionType: p.predictionType,
			targetValue: p.targetValue,
			targetDate: p.targetDate,
			confidenceLevel: p.confidenceLevel,
			status: p.status,
			accuracyScore: p.accuracyScore,
		})),
	};
}

/**
 * Format video for homepage carousel
 */
export function formatVideoForHomepage(videoData: any): VideoForHomepage {
	const { videos: video, economists: economist } = videoData;

	const duration = video.duration || 0;
	const viewCount = video.viewCount || 0;
	const publishedAt = video.publishedAt;

	return {
		id: video.id,
		videoTitle: video.title,
		youtubeVideoId: video.youtubeVideoId,
		thumbnailUrl: video.thumbnailUrl || formatThumbnailUrl(video.youtubeVideoId),
		duration: formatDuration(duration),
		viewCount: formatViewCount(viewCount),
		publishedAt,
		publishDate: publishedAt ? formatRelativeDate(new Date(publishedAt)) : 'Bilinmiyor',
		youtubeUrl: formatYouTubeUrl(video.youtubeVideoId),
		economist: {
			id: video.economistId,
			name: economist.name,
			channelName: economist.youtubeChannelName,
			avatarUrl: economist.avatarUrl,
			initials: generateInitials(economist.name),
			title: 'Ekonomi Uzmanı',
		},
		predictions: [], // Will be populated separately if needed
	};
}

/**
 * Format video for build-time (from raw SQL)
 */
export function formatVideoForBuild(video: RawVideoWithEconomist): VideoForBuild {
	const publishedAt =
		typeof video.publishedAt === 'number'
			? new Date(video.publishedAt * 1000)
			: new Date(video.publishedAt);

	return {
		id: video.id,
		videoTitle: video.title,
		youtubeVideoId: video.youtubeVideoId,
		publishedAt: publishedAt.toISOString(),
		publishDate: formatRelativeDate(publishedAt),
		youtubeUrl: formatYouTubeUrl(video.youtubeVideoId),
		economist: {
			id: video.economistId,
			name: video.economistName,
			avatarUrl: video.economistAvatarUrl,
			initials: generateInitials(video.economistName),
			title: 'Ekonomi Uzmanı',
		},
		predictions: [],
	};
}

/**
 * Format video for list display
 */
export function formatVideoListItem(video: RawVideo, economist?: any) {
	const duration = video.duration || 0;
	const viewCount = video.viewCount || 0;
	const publishedAt = formatDateToISO(video.publishedAt) || new Date().toISOString();

	return {
		id: video.id,
		title: video.title,
		youtubeVideoId: video.youtubeVideoId,
		thumbnailUrl: video.thumbnailUrl || formatThumbnailUrl(video.youtubeVideoId),
		duration: formatDuration(duration),
		viewCount: formatViewCount(viewCount),
		publishedAt,
		publishDate: formatRelativeDate(new Date(publishedAt)),
		youtubeUrl: formatYouTubeUrl(video.youtubeVideoId),
		economist: economist
			? {
					id: economist.id,
					name: economist.name,
					avatarUrl: economist.avatarUrl,
					channelName: economist.youtubeChannelName || economist.channelName,
				}
			: undefined,
	};
}

/**
 * Format video for search results
 */
export function formatVideoForSearch(video: RawVideo, economist?: any) {
	return {
		id: video.id,
		title: video.title,
		youtubeVideoId: video.youtubeVideoId,
		publishedAt: formatDateToISO(video.publishedAt) || new Date().toISOString(),
		youtubeUrl: formatYouTubeUrl(video.youtubeVideoId),
		economist: economist
			? {
					id: economist.id,
					name: economist.name,
					channelName: economist.youtubeChannelName || economist.channelName,
				}
			: undefined,
		type: 'video' as const,
	};
}

/**
 * Format video for sitemap
 */
export function formatVideoForSitemap(video: RawVideo) {
	return {
		url: formatYouTubeUrl(video.youtubeVideoId),
		lastmod: formatDateToISO(video.updatedAt || video.publishedAt) || new Date().toISOString(),
		changefreq: 'monthly' as const,
		priority: 0.6,
	};
}

/**
 * Format video for RSS feed
 */
export function formatVideoForRSS(video: RawVideo, economist?: any) {
	const publishedAt = formatDateToISO(video.publishedAt) || new Date().toISOString();

	return {
		title: video.title,
		description: `${economist?.name || 'Ekonomist'} tarafından paylaşılan video`,
		link: formatYouTubeUrl(video.youtubeVideoId),
		pubDate: new Date(publishedAt),
		author: economist?.name || 'Ekonomist',
		category: 'Video',
	};
}
