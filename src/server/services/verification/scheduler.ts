// Verification Scheduler for periodic automated verification
// import { verificationService } from './index';
import type { PredictionVerificationService } from './index';
import { setInterval, clearInterval } from 'node:timers';

export class VerificationScheduler {
	private intervalId: ReturnType<typeof setInterval> | null = null;
	private isRunning = false;
	private verificationService: PredictionVerificationService;

	constructor(verificationService: PredictionVerificationService) {
		this.verificationService = verificationService;
	}

	// Start periodic verification (default: every 6 hours)
	start(intervalHours = 6): void {
		if (this.isRunning) {
			console.log('Verification scheduler is already running');
			return;
		}

		const intervalMs = intervalHours * 60 * 60 * 1000; // Convert hours to milliseconds

		console.log(`Starting verification scheduler: running every ${intervalHours} hours`);

		// Run immediately on start
		this.runVerification();

		// Schedule periodic runs
		this.intervalId = setInterval(() => {
			this.runVerification();
		}, intervalMs);

		this.isRunning = true;
	}

	// Stop the scheduler
	stop(): void {
		if (this.intervalId) {
			clearInterval(this.intervalId);
			this.intervalId = null;
		}
		this.isRunning = false;
		console.log('Verification scheduler stopped');
	}

	// Get scheduler status
	getStatus(): { isRunning: boolean; nextRun?: Date } {
		return {
			isRunning: this.isRunning,
			// Note: setInterval doesn't provide next run time easily
			// In production, you'd want to use a more sophisticated scheduler like node-cron
		};
	}

	// Run verification manually
	async runVerification(): Promise<void> {
		try {
			console.log(`[${new Date().toISOString()}] Running scheduled verification...`);

			const results = await this.verificationService.runAutomatedVerification();

			console.log(`[${new Date().toISOString()}] Scheduled verification completed:`);
			console.log(`- Total processed: ${results.length} predictions`);
			console.log(`- Successful: ${results.filter(r => r.success).length}`);
			console.log(`- Failed: ${results.filter(r => !r.success).length}`);
			console.log(`- Verified correct: ${results.filter(r => r.status === 'verified_correct').length}`);
			console.log(`- Verified incorrect: ${results.filter(r => r.status === 'verified_incorrect').length}`);
			console.log(`- Average accuracy: ${results.length > 0 ? (results.reduce((sum, r) => sum + (r.accuracy || 0), 0) / results.length * 100).toFixed(1) : 0}%`);

		} catch (error) {
			console.error(`[${new Date().toISOString()}] Scheduled verification failed:`, error);
		}
	}
}

// No default export; instantiate with a verification service in your runtime entrypoint.
