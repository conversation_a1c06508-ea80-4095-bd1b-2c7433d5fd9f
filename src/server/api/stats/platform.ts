import { Hono } from 'hono';
import { getPlatformStats } from '@/server/services';
import { getDbFromContext } from '../../data';

const app = new Hono();

app.get('/', async (c) => {
	const db = getDbFromContext(c);
	if (!db) {
		return c.json({
			success: false,
			error: 'Database connection not available'
		}, 503);
	}

	try {
		const stats = await getPlatformStats(db);

		return c.json({
			success: true,
			data: stats,
			message: 'Platform statistics fetched successfully',
		});
	} catch (error) {
		console.error('Error fetching platform stats:', error);
		return c.json({
			success: false,
			error: 'Internal server error',
		}, 500);
	}
});

export default app;
