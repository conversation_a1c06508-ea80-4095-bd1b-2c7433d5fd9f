#!/bin/bash

# <PERSON><PERSON>t to fix VS Code TypeScript issues
# This script clears caches and regenerates type definitions

echo "🔧 Fixing VS Code TypeScript issues..."

# Clear TypeScript and Astro caches
echo "📁 Clearing caches..."
rm -rf .astro/
rm -rf node_modules/.cache/
rm -rf node_modules/.vite/

# Regenerate Astro types
echo "🔄 Regenerating Astro types..."
npx astro sync

# Check TypeScript
echo "✅ Running TypeScript check..."
npx astro check

echo "🎉 Done! Please reload VS Code window (Ctrl+Shift+P -> 'Developer: Reload Window')"
