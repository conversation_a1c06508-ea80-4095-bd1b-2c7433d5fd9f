---
export const prerender = false;

import Layout from '../layouts/Layout.astro';

// This page requires authentication - handled by middleware
const user = ((Astro.locals as any).auth?.user) || null;

const title = 'Profil - Türk Ekonomistler Doğruluk <PERSON>ç<PERSON>';

// Role display mapping
const roleDisplayNames = {
  'admin': 'Yönet<PERSON>',
  'moderator': 'Moderat<PERSON>r',
  'editor': '<PERSON><PERSON><PERSON>',
  'analyst': '<PERSON><PERSON>',
  'contributor': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  'user': '<PERSON><PERSON><PERSON><PERSON><PERSON>'
};

const userRoleDisplay = roleDisplayNames[user.role as keyof typeof roleDisplayNames] || user.role;

// Format date
const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
};
---

<Layout title={title}>
  <main class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
      <!-- Profile Header -->
      <div class="bg-white shadow rounded-lg overflow-hidden mb-6">
        <div class="bg-gradient-to-r from-amber-600 to-orange-600 px-6 py-8">
          <div class="flex items-center space-x-4">
            <div class="w-20 h-20 bg-white rounded-full flex items-center justify-center">
              <svg class="w-10 h-10 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="text-white">
              <h1 class="text-2xl font-bold">
                {user.username || 'Kullanıcı'}
              </h1>
              <p class="text-amber-100">
                {user.email}
              </p>
              <div class="flex items-center mt-2">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                  {userRoleDisplay}
                </span>
                {user.emailVerified ? (
                  <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    Doğrulanmış
                  </span>
                ) : (
                  <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                    Doğrulanmamış
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Information -->
        <div class="lg:col-span-2">
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Profil Bilgileri</h2>
            </div>
            <div class="px-6 py-4 space-y-6">
              <form id="profile-form" class="space-y-4">
                <div id="success-message" class="hidden bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative" role="alert">
                  <span class="block sm:inline">Profil başarıyla güncellendi!</span>
                </div>

                <div id="error-message" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
                  <span class="block sm:inline"></span>
                </div>

                <div>
                  <label for="username" class="block text-sm font-medium text-gray-700">
                    Kullanıcı Adı
                  </label>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    value={user.username || ''}
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500"
                  />
                </div>

                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700">
                    E-posta Adresi
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={user.email}
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500"
                    disabled
                  />
                  <p class="mt-1 text-xs text-gray-500">
                    E-posta adresi değiştirilemez.
                  </p>
                </div>

                <div>
                  <button
                    type="submit"
                    id="update-btn"
                    class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span id="update-text">Güncelle</span>
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- Password Change -->
          <div class="bg-white shadow rounded-lg mt-6">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Şifre Değiştir</h2>
            </div>
            <div class="px-6 py-4">
              <form id="password-form" class="space-y-4">
                <div id="password-success" class="hidden bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative">
                  <span class="block sm:inline">Şifre başarıyla değiştirildi!</span>
                </div>

                <div id="password-error" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
                  <span class="block sm:inline"></span>
                </div>

                <div>
                  <label for="currentPassword" class="block text-sm font-medium text-gray-700">
                    Mevcut Şifre
                  </label>
                  <input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500"
                  />
                </div>

                <div>
                  <label for="newPassword" class="block text-sm font-medium text-gray-700">
                    Yeni Şifre
                  </label>
                  <input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500"
                  />
                  <p class="mt-1 text-xs text-gray-500">En az 8 karakter olmalıdır.</p>
                </div>

                <div>
                  <label for="confirmNewPassword" class="block text-sm font-medium text-gray-700">
                    Yeni Şifre Onayı
                  </label>
                  <input
                    id="confirmNewPassword"
                    name="confirmNewPassword"
                    type="password"
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500"
                  />
                </div>

                <div>
                  <button
                    type="submit"
                    id="password-btn"
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span id="password-text">Şifreyi Değiştir</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Account Statistics -->
        <div class="space-y-6">
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Hesap İstatistikleri</h2>
            </div>
            <div class="px-6 py-4 space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Kayıt Tarihi</span>
                <span class="text-sm font-medium text-gray-900">
                  {user.createdAt ? formatDate(user.createdAt) : 'Bilinmiyor'}
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Son Giriş</span>
                <span class="text-sm font-medium text-gray-900">
                  {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'İlk giriş'}
                </span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Hesap Durumu</span>
                <span class={`text-sm font-medium ${user.isActive ? 'text-green-600' : 'text-red-600'}`}>
                  {user.isActive ? 'Aktif' : 'Pasif'}
                </span>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">Hızlı İşlemler</h2>
            </div>
            <div class="px-6 py-4 space-y-3">
              <a
                href="/dashboard"
                class="block w-full text-center bg-amber-600 hover:bg-amber-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
              >
                Panele Dön
              </a>
              {!user.emailVerified && (
                <button
                  id="verify-email-btn"
                  class="block w-full text-center bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
                >
                  E-posta Doğrula
                </button>
              )}
              <button
                id="delete-account-btn"
                class="block w-full text-center bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
              >
                Hesabı Sil
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Profile form handling
      const profileForm = document.getElementById('profile-form') as HTMLFormElement;
      const passwordForm = document.getElementById('password-form') as HTMLFormElement;

      function showMessage(elementId: string, message: string, _isError = false) {
        const element = document.getElementById(elementId);
        if (element) {
          const span = element.querySelector('span');
          if (span) span.textContent = message;
          element.classList.remove('hidden');
          setTimeout(() => element.classList.add('hidden'), 5000);
        }
      }

      // Profile update
      profileForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(profileForm);
        const data = {
          username: formData.get('username') as string,
        };

        try {
          const response = await fetch('/api/v1/auth/update-profile', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          });

          const result = await response.json() as { status: string; message?: string };

          if (response.ok && result.status === 'success') {
            showMessage('success-message', 'Profil başarıyla güncellendi!');
          } else {
            showMessage('error-message', result.message || 'Güncelleme başarısız.', true);
          }
        } catch {
          showMessage('error-message', 'Bağlantı hatası.', true);
        }
      });

      // Password change
      passwordForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(passwordForm);
        const data = {
          currentPassword: formData.get('currentPassword') as string,
          newPassword: formData.get('newPassword') as string,
          confirmNewPassword: formData.get('confirmNewPassword') as string,
        };

        if (data.newPassword !== data.confirmNewPassword) {
          showMessage('password-error', 'Yeni şifreler eşleşmiyor.', true);
          return;
        }

        try {
          const response = await fetch('/api/v1/auth/update-profile', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          });

          const result = await response.json() as { status: string; message?: string };

          if (response.ok && result.status === 'success') {
            showMessage('password-success', 'Şifre başarıyla değiştirildi!');
            passwordForm.reset();
          } else {
            showMessage('password-error', result.message || 'Şifre değiştirme başarısız.', true);
          }
        } catch {
          showMessage('password-error', 'Bağlantı hatası.', true);
        }
      });
    });
  </script>
</Layout>
