/**
 * Authentication validation utilities
 * These utilities are used for both client-side and server-side validation
 */

/**
 * Validates email format
 */
export function isValidEmail(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }

  const trimmedEmail = email.trim();

  // Use the same regex as in the actual auth code
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(trimmedEmail);
}

/**
 * Validates password strength
 */
export function isValidPassword(password: string): boolean {
  if (!password || typeof password !== 'string') {
    return false;
  }

  return password.length >= 8;
}

/**
 * Validates username format
 */
export function isValidUsername(username: string): boolean {
  if (!username || typeof username !== 'string') {
    return false;
  }

  const trimmedUsername = username.trim();

  // Username should be 3-30 characters, alphanumeric with underscores and hyphens
  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/;
  return usernameRegex.test(trimmedUsername);
}

/**
 * Validates that passwords match
 */
export function doPasswordsMatch(password: string, confirmPassword: string): boolean {
  if (!password && !confirmPassword) {
    return false; // Empty passwords should not match
  }

  if (!password || !confirmPassword) {
    return false;
  }

  return password === confirmPassword;
}

/**
 * Validates a name field
 */
export function isValidName(name: string): boolean {
  if (!name || typeof name !== 'string') {
    return false;
  }

  const trimmedName = name.trim();
  return trimmedName.length >= 2 && trimmedName.length <= 100;
}

/**
 * Comprehensive user registration validation
 */
export interface RegistrationData {
  name?: string;
  email: string;
  username?: string;
  password: string;
  confirmPassword: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export function validateRegistrationData(data: RegistrationData): ValidationResult {
  const errors: string[] = [];

  // Validate email
  if (!isValidEmail(data.email)) {
    errors.push('Please provide a valid email address');
  }

  // Validate password
  if (!isValidPassword(data.password)) {
    errors.push('Password must be at least 8 characters long');
  }

  // Validate password confirmation
  if (!doPasswordsMatch(data.password, data.confirmPassword)) {
    errors.push('Passwords do not match');
  }

  // Validate name if provided
  if (data.name && !isValidName(data.name)) {
    errors.push('Name must be between 2 and 100 characters');
  }

  // Validate username if provided
  if (data.username && !isValidUsername(data.username)) {
    errors.push('Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Profile update validation
 */
export interface ProfileUpdateData {
  name?: string;
  username?: string;
  currentPassword?: string;
  newPassword?: string;
  confirmNewPassword?: string;
}

export function validateProfileUpdateData(data: ProfileUpdateData): ValidationResult {
  const errors: string[] = [];

  // Validate name if provided
  if (data.name !== undefined && !isValidName(data.name)) {
    errors.push('Name must be between 2 and 100 characters');
  }

  // Validate username if provided
  if (data.username !== undefined && !isValidUsername(data.username)) {
    errors.push('Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens');
  }

  // If changing password, validate all password fields
  if (data.newPassword || data.confirmNewPassword) {
    if (!data.currentPassword) {
      errors.push('Current password is required to change password');
    }

    if (!isValidPassword(data.newPassword || '')) {
      errors.push('New password must be at least 8 characters long');
    }

    if (!doPasswordsMatch(data.newPassword || '', data.confirmNewPassword || '')) {
      errors.push('New passwords do not match');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
