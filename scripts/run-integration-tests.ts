#!/usr/bin/env tsx
/**
 * Integration Test Runner with Dev Server Management
 * Mirrors the logic of run-all-tests.ts but only for integration tests.
 */
import {
  ensureDevServer,
  cleanupIfStartedByScript,
  runCommand,
  execAsync
} from './test-utils';

async function runIntegrationTests(): Promise<number> {
  const PORT = 4321;
  try {
    // Build first
    await execAsync('npm run build');

    // Ensure dev server is running (use existing or start new)
    await ensureDevServer(PORT);

    // Run integration tests
    const result = await runCommand('npm', ['run', 'test:integration:raw']);
    return result;
  } catch (error) {
    console.error('❌ Integration test runner failed:', error);
    return 1;
  } finally {
    // Only cleanup if we started the server
    cleanupIfStartedByScript();
  }
}

process.on('SIGINT', () => {
  cleanupIfStartedByScript();
  process.exit(1);
});

runIntegrationTests().then(exitCode => process.exit(exitCode));
