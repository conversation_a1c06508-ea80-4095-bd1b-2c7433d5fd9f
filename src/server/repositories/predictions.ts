/**
 * Predictions Repository
 *
 * Direct database access layer for predictions.
 * Contains SQL constants, execution functions, and build-time data functions.
 * Used by build scripts for static site generation.
 */

import type { DbConnection } from '@/server/db';
import { predictions } from '@/server/db';
import { eq, desc, and } from 'drizzle-orm';
import { execSync } from 'child_process';

// ============================================================================
// RAW SQL QUERIES (for build scripts and external tools)
// ============================================================================

export const PREDICTIONS_SQL = {
	/**
	 * Get all predictions with economist and video info
	 */
	all: (limit?: number) =>
		`
    SELECT
      p.id,
      p.title,
      p.description,
      p.category,
      p.prediction_type as predictionType,
      p.target_value as targetValue,
      p.target_date as targetDate,
      p.confidence_level as confidenceLevel,
      p.status,
      p.accuracy_score as accuracyScore,
      p.verification_method as verificationMethod,
      p.verification_source as verificationSource,
      p.verified_at as verifiedAt,
      p.actual_value as actualValue,
      p.accuracy,
      p.extracted_at as extractedAt,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      e.id as economistId,
      e.name as economistName,
      e.avatar_url as economistAvatarUrl,
      e.youtube_channel_name as economistChannelName,
      v.id as videoId,
      v.title as videoTitle,
      v.youtube_video_id as youtubeVideoId
    FROM predictions p
    LEFT JOIN economists e ON p.economist_id = e.id
    LEFT JOIN videos v ON p.video_id = v.id
    ORDER BY p.created_at DESC
    ${limit ? `LIMIT ${limit}` : ''}
  `.trim(),

	/**
	 * Get predictions by economist
	 */
	byEconomist: (economistId: number, limit?: number) =>
		`
    SELECT
      p.id,
      p.title,
      p.description,
      p.category,
      p.prediction_type as predictionType,
      p.target_value as targetValue,
      p.target_date as targetDate,
      p.confidence_level as confidenceLevel,
      p.status,
      p.accuracy_score as accuracyScore,
      p.verification_method as verificationMethod,
      p.verification_source as verificationSource,
      p.verified_at as verifiedAt,
      p.actual_value as actualValue,
      p.accuracy,
      p.extracted_at as extractedAt,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      v.id as videoId,
      v.title as videoTitle,
      v.youtube_video_id as youtubeVideoId
    FROM predictions p
    LEFT JOIN videos v ON p.video_id = v.id
    WHERE p.economist_id = ${economistId}
    ORDER BY p.created_at DESC
    ${limit ? `LIMIT ${limit}` : ''}
  `.trim(),

	/**
	 * Get predictions by video
	 */
	byVideo: (videoId: number) =>
		`
    SELECT
      p.id,
      p.title,
      p.description,
      p.category,
      p.prediction_type as predictionType,
      p.target_value as targetValue,
      p.target_date as targetDate,
      p.confidence_level as confidenceLevel,
      p.status,
      p.accuracy_score as accuracyScore,
      p.verification_method as verificationMethod,
      p.verification_source as verificationSource,
      p.verified_at as verifiedAt,
      p.actual_value as actualValue,
      p.accuracy,
      p.extracted_at as extractedAt,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      e.id as economistId,
      e.name as economistName,
      e.avatar_url as economistAvatarUrl,
      e.youtube_channel_name as economistChannelName
    FROM predictions p
    LEFT JOIN economists e ON p.economist_id = e.id
    WHERE p.video_id = ${videoId}
    ORDER BY p.created_at DESC
  `.trim(),

	/**
	 * Get recent predictions
	 */
	recent: (limit = 20) =>
		`
    SELECT
      p.id,
      p.title,
      p.description,
      p.category,
      p.prediction_type as predictionType,
      p.target_value as targetValue,
      p.target_date as targetDate,
      p.confidence_level as confidenceLevel,
      p.status,
      p.accuracy_score as accuracyScore,
      p.verification_method as verificationMethod,
      p.verification_source as verificationSource,
      p.verified_at as verifiedAt,
      p.actual_value as actualValue,
      p.accuracy,
      p.extracted_at as extractedAt,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      e.id as economistId,
      e.name as economistName,
      e.avatar_url as economistAvatarUrl,
      e.youtube_channel_name as economistChannelName,
      v.id as videoId,
      v.title as videoTitle,
      v.youtube_video_id as youtubeVideoId
    FROM predictions p
    LEFT JOIN economists e ON p.economist_id = e.id
    LEFT JOIN videos v ON p.video_id = v.id
    WHERE p.status IN ('verified', 'pending')
    ORDER BY p.created_at DESC
    LIMIT ${limit}
  `.trim(),
};

// ============================================================================
// SQL EXECUTION FUNCTIONS
// ============================================================================

/**
 * Execute raw SQL query for predictions (used by build scripts)
 */
export function executePredictionSQL(query: string, dbName: string, isLocal = false): unknown {
	const localFlag = isLocal ? '--local' : '--remote';
	const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

	try {
		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Prediction SQL execution failed:', error);
		return [];
	}
}

// ============================================================================
// DRIZZLE ORM QUERY FUNCTIONS
// ============================================================================

/**
 * Find predictions with optional filters
 */
export async function findPredictions(
	db: DbConnection,
	options: {
		economistId?: number;
		videoId?: number;
		status?:
			| 'pending'
			| 'partially_verified'
			| 'verified_correct'
			| 'verified_incorrect'
			| 'expired';
		category?: string;
		limit?: number;
		offset?: number;
	} = {}
) {
	const { economistId, videoId, status, category, limit = 20, offset = 0 } = options;

	// Apply filters
	const conditions = [];
	if (economistId) conditions.push(eq(predictions.economistId, economistId));
	if (videoId) conditions.push(eq(predictions.videoId, videoId));
	if (status) conditions.push(eq(predictions.status, status));
	if (category) conditions.push(eq(predictions.category, category));

	// Build the complete query
	const baseQuery = db
		.select({
			id: predictions.id,
			title: predictions.title,
			description: predictions.description,
			category: predictions.category,
			predictionType: predictions.predictionType,
			targetValue: predictions.targetValue,
			targetDate: predictions.targetDate,
			confidenceLevel: predictions.confidenceLevel,
			status: predictions.status,
			accuracyScore: predictions.accuracyScore,
			verificationMethod: predictions.verificationMethod,
			verificationSource: predictions.verificationSource,
			verifiedAt: predictions.verifiedAt,
			actualValue: predictions.actualValue,
			accuracy: predictions.accuracy,
			extractedAt: predictions.extractedAt,
			createdAt: predictions.createdAt,
			updatedAt: predictions.updatedAt,
			economistId: predictions.economistId,
			videoId: predictions.videoId,
		})
		.from(predictions);

	// Apply conditions if any exist
	const queryWithConditions =
		conditions.length > 0 ? baseQuery.where(and(...conditions)) : baseQuery;

	return await queryWithConditions.orderBy(desc(predictions.createdAt)).limit(limit).offset(offset);
}

/**
 * Find prediction by ID
 */
export async function findPredictionById(db: DbConnection, id: number) {
	return db.select().from(predictions).where(eq(predictions.id, id)).limit(1);
}

/**
 * Get predictions by economist using raw SQL (for build-time)
 */
export function getPredictionsByEconomistSQL(
	dbName: string,
	economistId: number,
	isLocal = false,
	limit = 10
) {
	return executePredictionSQL(PREDICTIONS_SQL.byEconomist(economistId, limit), dbName, isLocal);
}

/**
 * Get predictions by video using raw SQL (for build-time)
 */
export function getPredictionsByVideoSQL(dbName: string, videoId: number, isLocal = false) {
	return executePredictionSQL(PREDICTIONS_SQL.byVideo(videoId), dbName, isLocal);
}

// ============================================================================
// FORMATTED BUILD DATA FUNCTIONS
// ============================================================================

/**
 * Get predictions data for build scripts (formatted)
 */
export function getPredictionsBuildData(dbName: string, isLocal = false, limit = 50) {
	const query = PREDICTIONS_SQL.all(limit);
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const rawPredictions = executePredictionSQL(query, dbName, isLocal) as any[];

	if (!Array.isArray(rawPredictions)) {
		return { predictions: [], totalCount: 0 };
	}

	const formattedPredictions = rawPredictions.map((prediction) => ({
		id: prediction.id,
		title: prediction.title,
		description: prediction.description,
		category: prediction.category,
		predictionType: prediction.predictionType,
		targetValue: prediction.targetValue,
		targetDate:
			prediction.targetDate && !isNaN(prediction.targetDate)
				? new Date(prediction.targetDate * 1000).toISOString()
				: null,
		confidenceLevel: prediction.confidenceLevel || null,
		status: prediction.status,
		accuracyScore: prediction.accuracyScore ? parseFloat(prediction.accuracyScore) : null,
		economistId: prediction.economistId || 0,
		videoId: prediction.videoId || null,
		economistName: prediction.economistName || null,
		createdAt:
			prediction.createdAt && !isNaN(prediction.createdAt)
				? new Date(prediction.createdAt * 1000).toISOString()
				: new Date().toISOString(),
		updatedAt:
			prediction.updatedAt && !isNaN(prediction.updatedAt)
				? new Date(prediction.updatedAt * 1000).toISOString()
				: new Date().toISOString(),
		isActive: Boolean(prediction.isActive || 1),
	}));

	return {
		predictions: formattedPredictions,
		totalCount: formattedPredictions.length,
	};
}
