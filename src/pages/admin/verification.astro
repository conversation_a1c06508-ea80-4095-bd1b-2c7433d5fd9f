---
import Layout from '@/layouts/Layout.astro';
import AutomatedVerificationDashboard from '@/components/vue/AutomatedVerificationDashboard.vue';

// This page should not be prerendered as it's an admin page requiring dynamic data
export const prerender = false;

// Check if user has admin/moderator access
const auth = (Astro.locals as any).auth;
if (!auth?.isAuthenticated) {
	return Astro.redirect('/login?redirect=' + encodeURIComponent(Astro.url.pathname));
}

// Check if user has required role (admin or moderator)
const hasRequiredRole = auth.roles?.includes('ROLE_ADMIN') || auth.roles?.includes('ROLE_MODERATOR');
if (!hasRequiredRole) {
	return Astro.redirect('/dashboard?error=access_denied');
}
---

<Layout title="Automated Verification System - Admin Panel">
	<main class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<!-- Page Header -->
			<div class="mb-8">
				<h1 class="text-3xl font-bold text-gray-900 dark:text-white">
					Automated Verification System
				</h1>
				<p class="mt-2 text-gray-600 dark:text-gray-400">
					Monitor and manage the automated prediction verification system that checks predictions against real data sources.
				</p>
			</div>

			<!-- Verification Dashboard -->
			<AutomatedVerificationDashboard client:load />

			<!-- System Information -->
			<div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
				<h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">System Information</h2>

				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Supported Data Sources</h3>
						<ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
							<li class="flex items-center">
								<span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
								TCMB (Turkish Central Bank) - Exchange rates, interest rates
							</li>
							<li class="flex items-center">
								<span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
								TurkStat - Inflation data, economic indicators
							</li>
							<li class="flex items-center">
								<span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
								Borsa Istanbul (BIST) - Stock market indices
							</li>
							<li class="flex items-center">
								<span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
								Gold Market Data - Precious metals prices
							</li>
							<li class="flex items-center">
								<span class="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
								Cryptocurrency APIs - Bitcoin, altcoins (maintenance)
							</li>
						</ul>
					</div>

					<div>
						<h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Verification Methods</h3>
						<ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
							<li>• <strong>Exact Value:</strong> Direct comparison with target value</li>
							<li>• <strong>Range Predictions:</strong> Check if actual value falls within predicted range</li>
							<li>• <strong>Increase/Decrease:</strong> Compare directional changes with baseline</li>
							<li>• <strong>Threshold Crossing:</strong> Verify if thresholds were crossed correctly</li>
							<li>• <strong>Manual Override:</strong> Administrative verification for edge cases</li>
						</ul>
					</div>
				</div>

				<div class="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
					<h4 class="text-sm font-medium text-amber-900 dark:text-amber-300 mb-2">How It Works</h4>
					<p class="text-sm text-amber-800 dark:text-amber-400">
						The automated verification system runs periodically to check predictions that have passed their target dates.
						It fetches real-time data from official sources, compares actual values with predictions, and calculates accuracy scores.
						This data is then used to update economist trust scores and platform reliability metrics.
					</p>
				</div>
			</div>

			<!-- Technical Notes -->
			<div class="mt-6 bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
				<h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Admin Notes</h3>
				<ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
					<li>• Verification runs automatically every 6 hours for predictions past their target date</li>
					<li>• Manual verification can be triggered for specific predictions or categories</li>
					<li>• Data source failures are logged and fallback mechanisms are used when available</li>
					<li>• Accuracy calculations vary by prediction type and include confidence intervals</li>
					<li>• Trust scores are updated in real-time based on verification results</li>
				</ul>
			</div>
		</div>
	</main>
</Layout>

<style>
	/* Ensure smooth transitions for loading states */
	.fade-enter-active, .fade-leave-active {
		transition: opacity 0.2s;
	}
	.fade-enter-from, .fade-leave-to {
		opacity: 0;
	}
</style>
