/**
 * Economists Queries
 *
 * SQL queries and database operations for economists.
 * Contains both raw SQL for build scripts and Drizzle ORM queries.
 */

import type { DbConnection } from '@/server/db';
import { economists, predictions, videos } from '@/server/db';
import { eq, desc, and, count } from 'drizzle-orm';
import type { QueryOptions } from '../shared/types';
import { execSync } from 'child_process';

// ============================================================================
// DRIZZLE ORM QUERIES (business logic layer)
// ============================================================================

/**
 * Find active economists with optional filtering and ordering
 */
export async function findActiveEconomists(
	db: DbConnection,
	options: QueryOptions & {
		orderBy?: 'accuracy' | 'trust' | 'predictions';
	} = {}
) {
	const { limit, orderBy = 'accuracy' } = options;

	const baseQuery = db.select().from(economists).where(eq(economists.isActive, true));

	// Apply ordering
	let orderedQuery;
	switch (orderBy) {
		case 'accuracy':
			orderedQuery = baseQuery.orderBy(desc(economists.accuracyScore), desc(economists.trustScore));
			break;
		case 'trust':
			orderedQuery = baseQuery.orderBy(desc(economists.trustScore), desc(economists.accuracyScore));
			break;
		case 'predictions':
			orderedQuery = baseQuery.orderBy(
				desc(economists.totalPredictions),
				desc(economists.accuracyScore)
			);
			break;
		default:
			orderedQuery = baseQuery.orderBy(desc(economists.accuracyScore), desc(economists.trustScore));
	}

	if (limit) {
		return await orderedQuery.limit(limit);
	}

	return await orderedQuery;
}

/**
 * Find economist by ID
 */
export async function findEconomistById(db: DbConnection, id: number) {
	const result = await db
		.select()
		.from(economists)
		.where(and(eq(economists.id, id), eq(economists.isActive, true)))
		.limit(1);

	return result[0] || null;
}

/**
 * Get economist prediction statistics
 */
export async function getEconomistPredictionStats(db: DbConnection, economistId: number) {
	const stats = await db
		.select({
			total: count(),
			verified: count(predictions.verifiedAt),
			correct: count(and(predictions.verifiedAt, eq(predictions.status, 'verified_correct'))),
		})
		.from(predictions)
		.where(eq(predictions.economistId, economistId));

	return stats[0] || { total: 0, verified: 0, correct: 0 };
}

/**
 * Get top economists for homepage (optimized query)
 */
export async function findTopEconomists(db: DbConnection, limit = 10) {
	return await db
		.select({
			id: economists.id,
			name: economists.name,
			youtubeChannelName: economists.youtubeChannelName,
			totalPredictions: economists.totalPredictions,
			correctPredictions: economists.correctPredictions,
			accuracyScore: economists.accuracyScore,
			trustScore: economists.trustScore,
			avatarUrl: economists.avatarUrl,
			bio: economists.bio,
			subscriberCount: economists.subscriberCount,
		})
		.from(economists)
		.where(eq(economists.isActive, true))
		.orderBy(desc(economists.accuracyScore), desc(economists.totalPredictions))
		.limit(limit);
}

/**
 * Get economists with recent videos
 */
export async function findEconomistsWithRecentVideos(db: DbConnection, limit = 10) {
	return await db
		.select({
			economist: economists,
			videoCount: count(videos.id),
		})
		.from(economists)
		.leftJoin(videos, and(eq(economists.id, videos.economistId), eq(videos.isActive, true)))
		.where(eq(economists.isActive, true))
		.groupBy(economists.id)
		.orderBy(desc(economists.accuracyScore))
		.limit(limit);
}

/**
 * Count economists with filters
 */
export async function countEconomists(db: DbConnection, _options: QueryOptions = {}) {
	const result = await db
		.select({ count: economists.id })
		.from(economists)
		.where(eq(economists.isActive, true));

	return result.length;
}

// ============================================================================
// SQL EXECUTION HELPER
// ============================================================================

/**
 * Execute raw SQL query for economists (used by build scripts)
 */
export function executeEconomistSQL(query: string, dbName: string, isLocal = false): unknown {
	try {
		const localFlag = isLocal ? '--local' : '--remote';
		const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Economist SQL execution failed:', error);
		return [];
	}
}
