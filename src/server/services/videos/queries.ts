/**
 * Videos Queries
 *
 * SQL queries and database operations for videos.
 * Contains both raw SQL for build scripts and Drizzle ORM queries.
 */

import type { DbConnection } from '@/server/db';
import { videos, economists, predictions } from '@/server/db';
import { eq, desc, and, inArray } from 'drizzle-orm';
import type { QueryOptions } from '../shared/types';
import { execSync } from 'child_process';

// ============================================================================
// DRIZZLE ORM QUERIES (business logic layer)
// ============================================================================

/**
 * Get recent videos with economist information
 */
export async function findRecentVideosWithEconomist(db: DbConnection, limit = 6) {
	return await db
		.select()
		.from(videos)
		.innerJoin(economists, eq(videos.economistId, economists.id))
		.where(and(eq(economists.isActive, true), eq(videos.isActive, true)))
		.orderBy(desc(videos.publishedAt))
		.limit(limit);
}

/**
 * Get videos by economist
 */
export async function findVideosByEconomist(
	db: DbConnection,
	economistId: number,
	options: QueryOptions = {}
) {
	const { limit = 10 } = options;

	return await db
		.select()
		.from(videos)
		.where(and(eq(videos.economistId, economistId), eq(videos.isActive, true)))
		.orderBy(desc(videos.publishedAt))
		.limit(limit);
}

/**
 * Get video by ID
 */
export async function findVideoById(db: DbConnection, id: number) {
	const result = await db
		.select()
		.from(videos)
		.innerJoin(economists, eq(videos.economistId, economists.id))
		.where(and(eq(videos.id, id), eq(videos.isActive, true)))
		.limit(1);

	return result[0] || null;
}

/**
 * Get predictions for videos
 */
export async function findPredictionsForVideos(db: DbConnection, videoIds: number[]) {
	if (videoIds.length === 0) return [];

	return await db.select().from(predictions).where(inArray(predictions.videoId, videoIds));
}

/**
 * Get videos with prediction counts
 */
export async function findVideosWithPredictionCounts(db: DbConnection, options: QueryOptions = {}) {
	const { limit = 20, economistId } = options;

	// Build conditions array
	const conditions = [eq(economists.isActive, true), eq(videos.isActive, true)];

	if (economistId) {
		conditions.push(eq(videos.economistId, economistId));
	}

	return await db
		.select({
			video: videos,
			economist: economists,
			predictionCount: predictions.id,
		})
		.from(videos)
		.innerJoin(economists, eq(videos.economistId, economists.id))
		.leftJoin(predictions, eq(videos.id, predictions.videoId))
		.where(and(...conditions))
		.groupBy(videos.id)
		.orderBy(desc(videos.publishedAt))
		.limit(limit);
}

/**
 * Count videos with filters
 */
export async function countVideos(db: DbConnection, options: QueryOptions = {}) {
	const { economistId } = options;

	// Build conditions array
	const conditions = [eq(economists.isActive, true), eq(videos.isActive, true)];

	if (economistId) {
		conditions.push(eq(videos.economistId, economistId));
	}

	const result = await db
		.select({ count: videos.id })
		.from(videos)
		.innerJoin(economists, eq(videos.economistId, economists.id))
		.where(and(...conditions));

	return result.length;
}

// ============================================================================
// SQL EXECUTION HELPER
// ============================================================================

/**
 * Execute raw SQL query for videos (used by build scripts)
 */
export function executeVideoSQL(query: string, dbName: string, isLocal = false): unknown {
	try {
		const localFlag = isLocal ? '--local' : '--remote';
		const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Video SQL execution failed:', error);
		return [];
	}
}
