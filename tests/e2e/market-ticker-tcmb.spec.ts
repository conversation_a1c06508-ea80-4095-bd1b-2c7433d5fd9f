import { test, expect } from '@playwright/test';

test.describe('Market Ticker (TCMB Integration)', () => {
  test('should display market ticker with USD/TRY and EUR/TRY rates', async ({ page }) => {
    await page.goto('/');

    // Wait for the page to load and ticker to appear
    await page.waitForSelector('text=USD/TRY', { timeout: 10000 });

    // Check that USD/TRY and EUR/TRY are visible
    const usdTicker = page.locator('text=USD/TRY').first();
    const eurTicker = page.locator('text=EUR/TRY').first();

    await expect(usdTicker).toBeVisible();
    await expect(eurTicker).toBeVisible();

    // Check for either dynamic data (if API key is available) or fallback static data
    const tickerContainer = page.locator('[data-testid="market-ticker"], .bg-slate-300').first();
    await expect(tickerContainer).toBeVisible();

    // Wait for component to finish loading
    await page.waitForTimeout(3000);

    // The ticker should show either numeric values (from API) or placeholder "--" (fallback)
    const hasNumericValues = await page.locator('text=/[0-9]+\\.[0-9]+/').count();
    const hasPlaceholders = await page.locator('text=--').count();

    // Should have either real data or placeholders
    expect(hasNumericValues + hasPlaceholders).toBeGreaterThan(0);
  });

  test('should handle loading state gracefully', async ({ page }) => {
    await page.goto('/');

    // Check that the ticker area exists (even if showing loading or fallback)
    const tickerArea = page.locator('.bg-slate-300, [data-testid="market-ticker"]').first();
    await expect(tickerArea).toBeVisible();

    // Should not show error messages in the ticker
    await expect(page.locator('text=error', { hasText: /error/i })).not.toBeVisible();
  });

  test('should display change indicators (arrows) or placeholders', async ({ page }) => {
    await page.goto('/');

    // Wait for ticker to load with data
    await page.waitForSelector('text=USD/TRY', { timeout: 10000 });

    // Wait for the component to finish loading (loading boxes should disappear)
    await page.waitForTimeout(6000); // Increased timeout to allow for API call completion

    // Check for up/down arrows (↑ or ↓) - only present if API data is available
    const hasUpArrow = await page.locator('text=↑').count();
    const hasDownArrow = await page.locator('text=↓').count();
    const totalArrows = hasUpArrow + hasDownArrow;

    // Check for placeholder values when API fails
    const hasPlaceholders = await page.locator('text=--').count();

    // Check for loading placeholders (animated boxes)
    const hasLoadingBoxes = await page.locator('.animate-pulse').count();

    // Should have either arrows (real data), placeholders (fallback), or loading boxes
    // If API is working: arrows > 0, placeholders = 0, loading = 0
    // If API fails: arrows = 0, placeholders > 0, loading = 0
    // If still loading: arrows = 0, placeholders = 0, loading > 0
    expect(totalArrows + hasPlaceholders + hasLoadingBoxes).toBeGreaterThan(0);

    // Ensure we have a consistent state
    if (totalArrows > 0) {
      // If we have arrows, we should have numeric values too
      const hasNumericValues = await page.locator('text=/[0-9]+\\.[0-9]+/').count();
      expect(hasNumericValues).toBeGreaterThan(0);
    } else if (hasLoadingBoxes === 0) {
      // If not loading and no arrows, we should have placeholders
      expect(hasPlaceholders).toBeGreaterThan(0);
    }
  });
});
