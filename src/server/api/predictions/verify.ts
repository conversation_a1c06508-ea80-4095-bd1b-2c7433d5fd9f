import { Hono } from 'hono';
import { eq } from 'drizzle-orm';
import { predictions } from '@/server/db';
import { getDbFromContext } from '../../data';
import { requireModerator } from '../../auth/middleware';

const app = new Hono();

// Verification status update endpoint
app.patch('/:id', requireModerator(), async (c) => {
	const db = getDbFromContext(c);
	if (!db) {
		return c.json({
			success: false,
			error: 'Database connection not available'
		}, 503);
	}

	try {
		const predictionId = parseInt(c.req.param('id'));
		if (isNaN(predictionId)) {
			return c.json({
				success: false,
				error: 'Invalid prediction ID',
			}, 400);
		}

		const body = await c.req.json();
		const {
			status,
			actualValue,
			verificationMethod,
			verificationSource,
			verificationNotes
		} = body;

		// Validate required fields
		if (!status || !['verified_correct', 'verified_incorrect', 'expired', 'partially_verified'].includes(status)) {
			return c.json({
				success: false,
				error: 'Invalid status. Must be one of: verified_correct, verified_incorrect, expired, partially_verified',
			}, 400);
		}

		// Fetch the prediction to calculate accuracy
		const prediction = await db.select().from(predictions).where(eq(predictions.id, predictionId)).get();

		if (!prediction) {
			return c.json({
				success: false,
				error: 'Prediction not found',
			}, 404);
		}

		// Calculate accuracy score
		const accuracyScore = calculateAccuracyScore(prediction, actualValue, status);

		// Update prediction with verification data
		const updatedPrediction = await db
			.update(predictions)
			.set({
				status,
				accuracyScore,
				verificationMethod,
				verificationSource,
				verificationNotes,
				verifiedAt: new Date(),
				updatedAt: new Date(),
			})
			.where(eq(predictions.id, predictionId))
			.returning()
			.get();

		if (!updatedPrediction) {
			throw new Error('Failed to update prediction');
		}

		return c.json({
			success: true,
			data: {
				id: updatedPrediction.id,
				status: updatedPrediction.status,
				accuracyScore: updatedPrediction.accuracyScore ? Math.round(updatedPrediction.accuracyScore * 100) : null,
				verifiedAt: updatedPrediction.verifiedAt,
				verificationMethod: updatedPrediction.verificationMethod,
				verificationSource: updatedPrediction.verificationSource,
				verificationNotes: updatedPrediction.verificationNotes,
			},
			message: 'Prediction verification updated successfully'
		});

	} catch (error) {
		console.error('Error verifying prediction:', error);
		return c.json({
			success: false,
			error: 'Failed to verify prediction',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

/**
 * Calculate accuracy score based on prediction type and actual outcome
 */
function calculateAccuracyScore(
	prediction: typeof predictions.$inferSelect,
	actualValue: number | null,
	status: string
): number | null {
	// Handle simple cases first
	if (status === 'expired') {
		return 0; // Expired predictions get 0 accuracy
	}

	if (status === 'verified_correct') {
		return 1; // Fully correct predictions get 100%
	}

	if (status === 'verified_incorrect') {
		return 0; // Incorrect predictions get 0%
	}

	// Handle partially verified predictions with actual value comparison
	if (status === 'partially_verified' && actualValue !== null && actualValue !== undefined) {
		try {
			const targetValue = prediction.targetValue ? JSON.parse(prediction.targetValue) : null;

			if (!targetValue) {
				return 0.5; // Default partial score if no target value
			}

			// Calculate based on prediction type
			switch (prediction.predictionType) {
				case 'exact_value':
					return calculateExactValueAccuracy(targetValue, actualValue);

				case 'range':
					return calculateRangeAccuracy(targetValue, actualValue);

				case 'increase':
				case 'decrease':
					return calculateDirectionalAccuracy(targetValue, actualValue, prediction.predictionType);

				default:
					return 0.5; // Default partial score for unknown types
			}
		} catch (error) {
			console.error('Error calculating accuracy score:', error);
			return 0.5; // Default partial score on error
		}
	}

	return null; // No score for pending status
}

/**
 * Calculate accuracy for exact value predictions
 */
function calculateExactValueAccuracy(targetValue: Record<string, unknown>, actualValue: number): number {
	if (typeof targetValue !== 'number' || typeof actualValue !== 'number') {
		return 0;
	}

	const percentageError = Math.abs(targetValue - actualValue) / targetValue;

	// Perfect match = 100%, 5% error = 95%, 10% error = 90%, etc.
	// Cap minimum at 0%
	return Math.max(0, 1 - percentageError);
}

/**
 * Calculate accuracy for range predictions
 */
function calculateRangeAccuracy(targetValue: Record<string, unknown>, actualValue: number): number {
	if (!targetValue.min || !targetValue.max || typeof actualValue !== 'number') {
		return 0;
	}

	const min = parseFloat(String(targetValue.min || '0'));
	const max = parseFloat(String(targetValue.max || '0'));

	if (actualValue >= min && actualValue <= max) {
		return 1;
	}

	const rangeSize = max - min;
	const distanceFromRange = actualValue < min ? min - actualValue : actualValue - max;
	const relativeError = distanceFromRange / rangeSize;

	return Math.max(0, 1 - relativeError);
}

/**
 * Calculate accuracy for directional predictions
 */
function calculateDirectionalAccuracy(targetValue: Record<string, unknown>, actualValue: number, predictionType: string): number {
	if (!targetValue.baseline || typeof actualValue !== 'number') {
		return 0;
	}

	const baseline = parseFloat(String(targetValue.baseline || '0'));
	const isIncrease = actualValue > baseline;
	const predictedIncrease = predictionType === 'increase';

	if (isIncrease === predictedIncrease) {
		if (targetValue.magnitude) {
			const actualMagnitude = Math.abs(actualValue - baseline);
			const targetMagnitude = parseFloat(String(targetValue.magnitude || '0'));
			const magnitudeAccuracy = 1 - Math.abs(actualMagnitude - targetMagnitude) / targetMagnitude;
			return Math.max(0.6, Math.min(1, magnitudeAccuracy));
		}
		return 0.8;
	}

	return 0;
}

export default app;
