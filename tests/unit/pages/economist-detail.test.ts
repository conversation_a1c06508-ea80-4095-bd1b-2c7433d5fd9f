import { describe, it, expect } from 'vitest';

describe('Economist Detail Page', () => {
  describe('Page Structure and Configuration', () => {
    it('should have economist detail page file with correct structure', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      expect(fs.existsSync(detailPagePath)).toBe(true);

      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for SSG configuration
      expect(content).toContain('export const prerender = true');

      // Check for getStaticPaths function
      expect(content).toContain('export async function getStaticPaths()');

      // Check for content collection import
      expect(content).toContain("import { getEntry } from 'astro:content'");

      // Check for economists data loading in getStaticPaths
      expect(content).toContain("getEntry('economists', 'data')");
    });

    it('should have proper Astro imports and SSG configuration', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for SSG configuration
      expect(content).toContain('export const prerender = true');
      expect(content).toContain('import Layout from');
      expect(content).toContain('getStaticPaths');
      expect(content).toContain('getEntry');
    });

    it('should validate getStaticPaths implementation', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check that getStaticPaths handles data properly
      expect(content).toContain('economists.map');
      expect(content).toContain('params: { id: economist.id.toString() }');
      expect(content).toContain('props: { economist }');

      // Check error handling in getStaticPaths
      expect(content).toContain('catch (error)');
      expect(content).toContain('return []');
    });
  });

  describe('Page Template Structure', () => {
    it('should have proper page layout and components', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for Layout component
      expect(content).toContain('import Layout from');
      expect(content).toContain('<Layout');

      // Check for static content sections
      expect(content).toContain('Ekonomist Profili');
      expect(content).toContain('Güven Skoru');
      expect(content).toContain('Son Tahminler');
      expect(content).toContain('Son Videolar');
    });

    it('should have proper error handling', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for error handling in getStaticPaths
      expect(content).toContain('catch (error)');
      expect(content).toContain('console.error');

      // Check for redirect handling
      expect(content).toContain('Astro.redirect');
      expect(content).toContain('isNaN(Number(id))');
    });

    it('should have static data loading from content collections', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for content collection data loading
      expect(content).toContain("getEntry('economists', 'data')");
      expect(content).toContain("getEntry('predictions', 'data')");
      expect(content).toContain("getEntry('videos', 'data')");
    });
  });

  describe('Page Metadata Generation', () => {
    it('should have dynamic page title generation', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for page title generation logic
      expect(content).toContain('pageTitle');
      expect(content).toContain('economist?.name');
      expect(content).toContain('Ekonomist Profili');
      expect(content).toContain('Güven Skoru');
    });

    it('should have dynamic page description generation', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for page description generation logic
      expect(content).toContain('pageDescription');
      expect(content).toContain('economist?.bio');
      expect(content).toContain('substring(0, 150)');
    });
  });

  describe('Error Handling and Validation', () => {
    it('should have proper ID validation', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for ID validation
      expect(content).toContain('isNaN(Number(id))');
      expect(content).toContain('Astro.redirect(\'/404\')');
    });

    it('should have error handling in getStaticPaths', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const detailPagePath = path.resolve('./src/pages/economists/[id].astro');
      const content = fs.readFileSync(detailPagePath, 'utf-8');

      // Check for error handling in getStaticPaths
      expect(content).toContain('try {');
      expect(content).toContain('} catch (error) {');
      expect(content).toContain('console.error');
    });
  });
});
