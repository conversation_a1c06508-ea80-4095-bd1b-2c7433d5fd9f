import { describe, it, expect } from 'vitest';

describe('Authentication API Migration', () => {
  it('should have removed Astro auth routes', async () => {
    const fs = await import('fs');
    const path = await import('path');

    // Check that the old Astro auth routes were removed
    const authDir = path.resolve('./src/pages/api/auth');
    expect(fs.existsSync(authDir)).toBe(false);

    const loginPath = path.resolve('./src/pages/api/auth/login.ts');
    expect(fs.existsSync(loginPath)).toBe(false);

    const registerPath = path.resolve('./src/pages/api/auth/register.ts');
    expect(fs.existsSync(registerPath)).toBe(false);

    const logoutPath = path.resolve('./src/pages/api/auth/logout.ts');
    expect(fs.existsSync(logoutPath)).toBe(false);

    const mePath = path.resolve('./src/pages/api/auth/me.ts');
    expect(fs.existsSync(mePath)).toBe(false);

    const updateProfilePath = path.resolve('./src/pages/api/auth/update-profile.ts');
    expect(fs.existsSync(updateProfilePath)).toBe(false);
  });

  it('should have created Hono auth routes', async () => {
    const fs = await import('fs');
    const path = await import('path');

    // Check that the new Hono auth routes exist
    const authRoutesPath = path.resolve('./src/server/auth/routes.ts');
    expect(fs.existsSync(authRoutesPath)).toBe(true);

    // Check that it contains the expected routes
    const content = fs.readFileSync(authRoutesPath, 'utf-8');

    // Should contain all authentication endpoints
    expect(content).toContain("auth.post('/login'");
    expect(content).toContain("auth.post('/register'");
    expect(content).toContain("auth.post('/logout'");
    expect(content).toContain("auth.get('/me'");
    expect(content).toContain("auth.put('/update-profile'");

    // Should use proper Hono patterns
    expect(content).toContain('return c.json(');
    expect(content).toContain('getDbFromContext(c)');
    expect(content).toContain('requireAuth()');
  });

  it('should have proper authentication module export', async () => {
    // Import the auth routes module
    const authModule = await import('../../src/server/auth/routes');

    expect(authModule.default).toBeDefined();
    expect(typeof authModule.default).toBe('object');
  });

  it('should have auth routes mounted in main API', async () => {
    const fs = await import('fs');
    const path = await import('path');

    const apiIndexPath = path.resolve('./src/server/api/index.ts');
    expect(fs.existsSync(apiIndexPath)).toBe(true);

    const content = fs.readFileSync(apiIndexPath, 'utf-8');

    // Should import auth routes
    expect(content).toContain("import auth from '../auth/routes'");

    // Should mount auth routes
    expect(content).toContain("app.route('/auth', auth)");
  });
});
