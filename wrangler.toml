# Base configuration (used for local development with wrangler dev)
# This uses the same dev environment as deployed development
name = "economist-accuracy-tracker-tr-web-app-dev"
main = "./dist/_worker.js/index.js"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]
account_id = "2d0f353bf5f874ed5d98baba20dfcb00"

[assets]
binding = "ASSETS"
directory = "./dist"

[observability]
enabled= true

# Use dev KV namespace for local development
[[kv_namespaces]]
binding = "SESSION"
id = "kv-local"
preview_id = "kv-local"

# Use local D1 database for local development
[[d1_databases]]
binding = "DB"
database_name = "economist-tracker-local"
database_id = "economist-tracker-local"
preview_database_id = "economist-tracker-local"
migrations_dir = "drizzle"



# Local Environment Variables
[vars]
NODE_ENV = "development"
API_BASE_URL = "http://localhost:8787"
CORS_ORIGINS = "http://localhost:8787,http://localhost:4321"

# Development Environment Configuration (deployed to Workers)
[env.development]
name = "economist-accuracy-tracker-tr-web-app-dev"
compatibility_flags = ["nodejs_compat"]

# Development KV Namespace
[[env.development.kv_namespaces]]
binding = "SESSION"
id = "3dcf011110a24c55b924578b8de3a53d"

# Development D1 Database
[[env.development.d1_databases]]
binding = "DB"
database_name = "economist-tracker-dev"
database_id = "7d47167e-8a89-431a-ae1e-fe9f68020e7c"
migrations_dir = "drizzle"

# Development Environment Variables
[env.development.vars]
NODE_ENV = "development"
API_BASE_URL = "https://economist-accuracy-tracker-tr-web-app-dev.cihangir-yildirim-2d0.workers.dev"
CORS_ORIGINS = "https://economist-accuracy-tracker-tr-web-app-dev.cihangir-yildirim-2d0.workers.dev"

# Staging Environment Configuration
[env.staging]
name = "economist-accuracy-tracker-tr-web-app-staging"
compatibility_flags = ["nodejs_compat"]

# Staging KV Namespace
[[env.staging.kv_namespaces]]
binding = "SESSION"
id = "a502cdfe64b04ef88cfb473fd5c5dc5b"

# Staging D1 Database
[[env.staging.d1_databases]]
binding = "DB"
database_name = "economist-tracker-staging"
database_id = "17ec9587-a912-4634-850f-8e77f8e19d4b"
migrations_dir = "drizzle"

# Staging Environment Variables
[env.staging.vars]
NODE_ENV = "staging"
API_BASE_URL = "https://economist-accuracy-tracker-tr-web-app-staging.cihangir-yildirim-2d0.workers.dev"
CORS_ORIGINS = "https://economist-accuracy-tracker-tr-web-app-staging.cihangir-yildirim-2d0.workers.dev"

# Production Environment Configuration
[env.production]
name = "economist-accuracy-tracker-tr-web-app-prod"

# Production KV Namespace
[[env.production.kv_namespaces]]
binding = "SESSION"
id = "your-prod-kv-namespace-id"

# Production D1 Database
[[env.production.d1_databases]]
binding = "DB"
database_name = "economist-tracker-prod"
database_id = "your-prod-d1-database-id"

# Production Environment Variables
[env.production.vars]
NODE_ENV = "production"
API_BASE_URL = "https://economist-tracker.workers.dev"
CORS_ORIGINS = "https://economist-tracker.workers.dev,https://economist-tracker.com"
