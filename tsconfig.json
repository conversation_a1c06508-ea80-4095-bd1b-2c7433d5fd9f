{"extends": "astro/tsconfigs/strict", "include": [".astro/types.d.ts", "**/*", "src/**/*", "src/env.d.ts", "src/types/astro-modules.d.ts"], "exclude": ["dist", "node_modules", ".astro", "drizzle", "playwright-report", "test-results", "playwright/.cache"], "compilerOptions": {"baseUrl": ".", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "preserve", "jsxImportSource": "vue", "skipLibCheck": true, "isolatedModules": true, "resolveJsonModule": true, "allowImportingTsExtensions": false, "noEmit": true, "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/features/*": ["./src/features/*"], "@/layouts/*": ["./src/layouts/*"], "@/server/*": ["./src/server/*"], "@/services/*": ["./src/services/*"], "@/stores/*": ["./src/stores/*"], "@/utils/*": ["./src/utils/*"]}, "types": ["astro/client", "vite/client", "@types/node", "vue", "drizzle-orm"]}}