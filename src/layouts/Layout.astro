---
// Base layout for all pages
import Header from '@/components/astro/Header.astro';
import Footer from '@/components/astro/Footer.astro';
import ClientAuth from '@/components/astro/ClientAuth.astro';

interface Props {
	title?: string;
	description?: string;
	currentPath?: string;
	ogTitle?: string;
	ogDescription?: string;
	ogImage?: string;
}

const {
	title = 'YouTube Economist Trust Score',
	description = 'YouTube ekonomistlerinin tahmin doğ<PERSON>luğunu takip eden platform',
	currentPath,
	ogTitle,
	ogDescription,
	ogImage = '/og-image.jpg',
} = Astro.props;
---

<!doctype html>
<html lang="tr" class="dark">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<title>{title}</title>

		<!-- OpenGraph meta tags -->
		<meta property="og:title" content={ogTitle || title} />
		<meta property="og:description" content={ogDescription || description} />
		<meta property="og:image" content={ogImage} />
		<meta property="og:type" content="website" />
		<meta property="og:site_name" content="YouTube Economist Trust Score" />

		<!-- Twitter Card meta tags -->
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:title" content={ogTitle || title} />
		<meta name="twitter:description" content={ogDescription || description} />
		<meta name="twitter:image" content={ogImage} />
		<script>
			// Theme initialization script to prevent flash - defaults to dark mode
			(function() {
				const savedTheme = localStorage.getItem('theme');
				const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
				// Default to dark mode if no saved preference
				const isDark = savedTheme ? savedTheme === 'dark' : (savedTheme === null ? true : prefersDark);

				if (isDark) {
					document.documentElement.classList.add('dark');
				} else {
					document.documentElement.classList.remove('dark');
				}
			})();
		</script>
	</head>
	<body class="min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 flex flex-col transition-colors duration-200">
		<Header title={title} currentPath={currentPath} />
		<main class="flex-1">
			<slot />
		</main>
		<Footer />
		<ClientAuth />
	</body>
</html>
