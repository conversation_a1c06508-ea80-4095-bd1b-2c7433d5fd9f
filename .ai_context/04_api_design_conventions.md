# 04_api_design_conventions.md

This document outlines the conventions for designing and interacting with the API for this project. Adherence to these conventions will ensure consistency, predictability, and ease of use for both frontend development and AI agent interactions.

## 1. Endpoint Naming

- **Style:** A RESTful approach will be followed for API design.
- **Resource Naming:** Resources will be named using plural nouns.
  &#x20; \* Example: `/users`, `/videos`, `/predictions`.
- **Path Parameters:** Path parameters will use `camelCase`.
  &#x20; \* Example: `/users/{userId}`, `/economists/{economistId}/videos/{videoId}`.
- **Query Parameters:** Query parameters will use `snake_case`.
  &#x20; \* Example: `/videos?sort_by=publish_date&order=desc`.
- **Versioning:** API versioning will be path-based, included in the URL.
  &#x20; \* Example: `/api/v1/users`, `/api/v1/videos`.

## 2. Authentication & Authorization

- **Authentication Method:**
  &#x20; \* **JWT (JSON Web Tokens):** User authentication will be handled via JWTs.
  &#x20; \* **Token Storage (Client):** JWTs should be stored in secure, HTTPOnly cookies on the client-side.
  &#x20; \* **Token Transmission:** For authenticated requests, the JWT will be sent in the `Authorization` header as a Bearer token.
  &#x20; \* Example: `Authorization: Bearer <your_jwt_token>`
  &#x20; \* **Token Lifecycle:** A dedicated Cloudflare Worker service will be responsible for issuing (e.g., upon login/registration), validating, and refreshing JWTs.
- **Authorization Strategy: Role-Based Access Control (RBAC)**
  &#x20; \* **Defined Roles:** The following roles will be used within the system:
  &#x20; \* `ROLE_ANONYMOUS`: For users who are not authenticated (accessing public, non-protected endpoints).
  &#x20; \* `ROLE_GUEST`: (If distinct from `ROLE_ANONYMOUS`, define specific use case, e.g., users who have interacted but not registered).
  &#x20; \* `ROLE_FREE_USER`: Registered users on the free tier.
  &#x20; \* `ROLE_PAID_USER`: Registered users with an active paid subscription.
  &#x20; \* `ROLE_CONTRIBUTOR`: Users who may have special permissions to contribute or manage specific content (details to be defined based on platform features).
  &#x20; \* `ROLE_ADMIN`: Administrators with full access to manage the platform.
  &#x20; \* **Role in JWT:** The JWT issued to an authenticated user will contain a claim specifying their assigned role(s) (e.g., `"roles": ["ROLE_PAID_USER"]`).
  &#x20; \* **Route Protection:** Hono middleware will be implemented to protect routes. This middleware will:
  &#x20; 1\. Verify the JWT.
  &#x20; 2\. Extract the user's role(s) from the JWT payload.
  &#x20; 3\. Check if the role(s) grant permission to access the requested resource or perform the action.
  &#x20; 4\. If authorization fails, a `403 Forbidden` HTTP status code will be returned.
  &#x20; \* **Permissions:** While roles provide a primary means of authorization, specific granular permissions can be defined and associated with roles if finer-grained control is needed as the platform evolves.

## 3. Standard Request/Response Formats

- **Data Format:** All request and response bodies will use **JSON**.
- **Successful Responses (2xx status codes):**
  &#x20; \* A consistent wrapper will be used for successful responses:

```json
{
	"status": "success",
	"data": {
		// Requested data or resource representation
	}
}
```

&#x20; \* For list endpoints, `data` might be an array of items. For single resource endpoints, `data` would be an object. For operations like DELETE that might not return content, `data` could be `null` or an empty object.

- **Error Responses (4xx and 5xx status codes):**
  &#x20; \* A standardized error response format will be used, compatible with custom error classes defined in `03_coding_standards_and_patterns.md` and suitable for logging (Logflare, LGTM stack):

```json
{
	"status": "error", // Or "fail" for client-side errors (4xx), "error" for server-side errors (5xx)
	"message": "A human-readable error message providing context about the error.",
	"errorCode": "SPECIFIC_ERROR_CODE_IF_APPLICABLE", // e.g., "VALIDATION_ERROR", "NOT_FOUND"
	"details": [] // Optional: An array of objects or strings providing more specific details,
	// especially for validation errors (e.g., field-specific error messages).
}
```

- &#x20; \* Validation errors from Drizzle ORM or Hono validators should be mapped to this structure, typically populating the `details` array.
- **HTTP Status Codes:** Standard HTTP status code meanings will be strictly adhered to (e.g., 200, 201, 400, 401, 403, 404, 500).
- **Pagination (for lists of resources):**
  &#x20; \* **Query Parameters:** `page` (1-indexed) and `page_size` (number of items per page).
  &#x20; \* Example: `/api/v1/videos?page=2&page_size=20`
  &#x20; \* **Response Body:** Pagination information will be included in the response body alongside the data:
  json:

```json
{
	"status": "success",
	"data": [
		// Array of items for the current page
	],
	"pagination": {
		"currentPage": 2,
		"pageSize": 20,
		"totalItems": 150,
		"totalPages": 8
	}
}
```

- **Sorting (for lists of resources):**
  &#x20; \* **Query Parameters:** `sort_by` (field name) and `sort_order` (`asc` or `desc`).
  &#x20; \* Example: `/api/v1/economists?sort_by=accuracy_score&sort_order=desc`
- **Filtering (for lists of resources):**
  &#x20; \* **Query Parameters:** Filters will be applied using query parameters named after the field being filtered.
  &#x20; \* Example: `/api/v1/predictions?investment_tool_id=xyz&status=active`
- **Date/Time Format:**
  &#x20; \* All dates and times in requests and responses will be in **UTC**.
  &#x20; \* The format will be ISO 8601: `YYYY-MM-DDTHH:mm:ss.sssZ`.
