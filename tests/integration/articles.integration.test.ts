/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, beforeAll } from 'vitest';

// Integration tests expect the server to be running
// Use `npm run test:integration` which automatically starts the server
describe('Articles API Integration', () => {
	const baseUrl = 'http://localhost:4321/api/v1';

	beforeAll(() => {
		console.log('🧪 Running Articles API integration tests');
	});

	describe('GET /api/v1/articles/curated', () => {
		it('should return curated articles successfully', async () => {
			const response = await fetch(`${baseUrl}/articles/curated`);

			expect(response.status).toBe(200);

			const data = (await response.json()) as any;

			expect(data).toHaveProperty('status', 'success');
			expect(data).toHaveProperty('data');
			expect(data).toHaveProperty('message');
			expect(Array.isArray(data.data)).toBe(true);

			console.log(`✅ Received ${data.data.length} curated articles`);
		});

		it('should return articles with correct structure', async () => {
			const response = await fetch(`${baseUrl}/articles/curated`);

			// If server is not running, we'll get a connection error
			// If server is running but DB is down, we'll get 503
			// Both are valid scenarios to test
			if (response.status === 503) {
				// Database connection issue - this is a valid test scenario
				const data = (await response.json()) as any;
				expect(data.status).toBe('error');
				expect(data.message).toContain('Database connection not available');
				console.log('✅ Database connection error handled properly');
				return;
			}

			// Server should respond with 200 if everything is working
			expect(response.status).toBe(200);
			const data = (await response.json()) as any;

			expect(data.status).toBe('success');
			expect(data).toHaveProperty('data');
			expect(Array.isArray(data.data)).toBe(true);

			if (data.data && data.data.length > 0) {
				const article = data.data[0];

				// Check required fields
				expect(article).toHaveProperty('id');
				expect(article).toHaveProperty('title');
				expect(article).toHaveProperty('excerpt');
				expect(article).toHaveProperty('author');
				expect(article).toHaveProperty('category');
				expect(article).toHaveProperty('readTime');
				expect(article).toHaveProperty('publishedAt');

				// Check optional fields
				expect(article).toHaveProperty('imageUrl');
				expect(article).toHaveProperty('slug');
				expect(article).toHaveProperty('tags');

				// Validate data types
				expect(typeof article.id).toBe('number');
				expect(typeof article.title).toBe('string');
				expect(typeof article.excerpt).toBe('string');
				expect(typeof article.author).toBe('string');
				expect(typeof article.category).toBe('string');
				expect(typeof article.readTime).toBe('string');
				expect(Array.isArray(article.tags)).toBe(true);

				console.log(`✅ Article structure validation passed for: "${article.title}"`);
			} else {
				console.log('⚠️ No articles returned - this might indicate a data issue');
			}
		});

		it('should respect limit parameter', async () => {
			const limit = 3;
			const response = await fetch(`${baseUrl}/articles/curated?limit=${limit}`);

			expect(response.status).toBe(200);
			const data = (await response.json()) as any;

			expect(data.data.length).toBeLessThanOrEqual(limit);
			console.log(`✅ Limit parameter working: requested ${limit}, got ${data.data.length}`);
		});

		it('should return Turkish economic content', async () => {
			const response = await fetch(`${baseUrl}/articles/curated`);

			expect(response.status).toBe(200);
			const data = (await response.json()) as any;

			if (data.data && data.data.length > 0) {
				// Check if we have Turkish economic content
				const hasTurkishContent = data.data.some(
					(article: any) =>
						article.title.includes('Türkiye') ||
						article.title.includes('Merkez Bankası') ||
						article.title.includes('ekonomi') ||
						article.excerpt.includes('Türkiye') ||
						article.excerpt.includes('TCMB')
				);

				expect(hasTurkishContent).toBe(true);
				console.log('✅ Turkish economic content found in articles');
			}
		});

		it('should handle invalid limit parameter gracefully', async () => {
			const response = await fetch(`${baseUrl}/articles/curated?limit=invalid`);

			// Should still return 200 with default limit
			expect(response.status).toBe(200);

			const data = (await response.json()) as any;
			expect(data.status).toBe('success');

			console.log('✅ Invalid limit parameter handled gracefully');
		});

		it('should return articles ordered by published date (newest first)', async () => {
			const response = await fetch(`${baseUrl}/articles/curated`);

			expect(response.status).toBe(200);
			const data = (await response.json()) as any;

			if (data.data && data.data.length > 1) {
				// Check if articles are ordered by publishedAt descending
				for (let i = 0; i < data.data.length - 1; i++) {
					const current = new Date(data.data[i].publishedAt);
					const next = new Date(data.data[i + 1].publishedAt);

					expect(current.getTime()).toBeGreaterThanOrEqual(next.getTime());
				}

				console.log('✅ Articles are properly ordered by published date');
			}
		});

		it('should handle database connection errors', async () => {
			// This test checks that the API handles database errors gracefully
			// In a real scenario where DB is down, it should return 503
			const response = await fetch(`${baseUrl}/articles/curated`);

			// Either success (200) or service unavailable (503) are acceptable
			expect([200, 503]).toContain(response.status);

			if (response.status === 503) {
				const data = (await response.json()) as any;
				expect(data.status).toBe('error');
				expect(data.message).toContain('Database connection not available');
				console.log('✅ Database connection error handled properly');
			} else {
				console.log('✅ Database connection working properly');
			}
		});
	});

	describe('Articles API Performance', () => {
		it('should respond within reasonable time', async () => {
			const startTime = Date.now();
			const response = await fetch(`${baseUrl}/articles/curated?limit=5`);
			const endTime = Date.now();

			const responseTime = endTime - startTime;

			expect(response.status).toBe(200);
			// Should respond within 2 seconds for local testing
			expect(responseTime).toBeLessThan(2000);
			console.log(`✅ API responded in ${responseTime}ms`);
		});
	});

	describe('Articles Content Quality', () => {
		it('should have meaningful content in articles', async () => {
			const response = await fetch(`${baseUrl}/articles/curated`);

			expect(response.status).toBe(200);
			const data = (await response.json()) as any;

			if (data.data && data.data.length > 0) {
				data.data.forEach((article: any) => {
					// Check content quality
					expect(article.title.length).toBeGreaterThan(10);
					expect(article.excerpt.length).toBeGreaterThan(20);
					expect(article.author.length).toBeGreaterThan(3);
					expect(article.category.length).toBeGreaterThan(3);
					expect(article.readTime).toMatch(/\d+\s*(dakika|dk)/);
				});

				console.log('✅ All articles have meaningful content');
			}
		});
	});
});
