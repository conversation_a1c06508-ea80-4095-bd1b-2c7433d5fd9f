#!/usr/bin/env tsx

/**
 * Environment Validation Script
 *
 * Validates that the development and production environments are properly configured
 * for Cloudflare Workers deployment.
 */

import { existsSync, readFileSync } from 'fs';
import { resolve } from 'path';
import { execSync } from 'child_process';

interface ValidationResult {
	category: string;
	test: string;
	status: 'pass' | 'fail' | 'warning';
	message: string;
}

const results: ValidationResult[] = [];

function addResult(category: string, test: string, status: ValidationResult['status'], message: string): void {
	results.push({ category, test, status, message });
}

function executeCommand(command: string): string {
	try {
		return execSync(command, { encoding: 'utf-8', stdio: 'pipe' });
	} catch {
		throw new Error(`Command failed: ${command}`);
	}
}

function validateFiles(): void {
	const requiredFiles = [
		'package.json',
		'wrangler.toml',
		'astro.config.mjs',
		'drizzle.config.ts',
		'src/server/db/index.ts',
		'src/utils/env.ts'
	];

	for (const file of requiredFiles) {
		const filePath = resolve(process.cwd(), file);
		if (existsSync(filePath)) {
			addResult('Files', file, 'pass', 'File exists');
		} else {
			addResult('Files', file, 'fail', 'File missing');
		}
	}
}

function validatePackageJson(): void {
	try {
		const packagePath = resolve(process.cwd(), 'package.json');
		const packageJson = JSON.parse(readFileSync(packagePath, 'utf-8'));

		const requiredScripts = [
			'build:prod',
			'deploy',
			'deploy:staging',
			'db:migrate:dev',
			'db:migrate:prod'
		];

		for (const script of requiredScripts) {
			if (packageJson.scripts?.[script]) {
				addResult('Package.json', `script: ${script}`, 'pass', 'Script defined');
			} else {
				addResult('Package.json', `script: ${script}`, 'fail', 'Script missing');
			}
		}

		const requiredDeps = [
			'wrangler',
			'@astrojs/cloudflare',
			'drizzle-orm'
		];

		for (const dep of requiredDeps) {
			if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
				addResult('Package.json', `dependency: ${dep}`, 'pass', 'Dependency installed');
			} else {
				addResult('Package.json', `dependency: ${dep}`, 'fail', 'Dependency missing');
			}
		}
	} catch (error) {
		addResult('Package.json', 'parse', 'fail', `Failed to parse: ${String(error)}`);
	}
}

function validateWranglerToml(): void {
	try {
		const wranglerPath = resolve(process.cwd(), 'wrangler.toml');
		const wranglerContent = readFileSync(wranglerPath, 'utf-8');

		const requiredSections = [
			'[env.staging]',
			'[env.production]',
			'[[d1_databases]]',
			'[[kv_namespaces]]'
		];

		for (const section of requiredSections) {
			if (wranglerContent.includes(section)) {
				addResult('wrangler.toml', section, 'pass', 'Section exists');
			} else {
				addResult('wrangler.toml', section, 'fail', 'Section missing');
			}
		}

		// Check if placeholder IDs are still present
		if (wranglerContent.includes('your-') || wranglerContent.includes('placeholder')) {
			addResult('wrangler.toml', 'configuration', 'warning', 'Contains placeholder values - run npm run cf:setup');
		} else {
			addResult('wrangler.toml', 'configuration', 'pass', 'No placeholder values found');
		}
	} catch (error) {
		addResult('wrangler.toml', 'parse', 'fail', `Failed to parse: ${String(error)}`);
	}
}

function validateWranglerCLI(): void {
	try {
		executeCommand('wrangler --version');
		addResult('Wrangler CLI', 'installation', 'pass', 'Wrangler CLI installed');

		try {
			executeCommand('wrangler whoami');
			addResult('Wrangler CLI', 'authentication', 'pass', 'Wrangler authenticated');
		} catch {
			addResult('Wrangler CLI', 'authentication', 'fail', 'Wrangler not authenticated - run: wrangler login');
		}
	} catch {
		addResult('Wrangler CLI', 'installation', 'fail', 'Wrangler CLI not installed - run: npm install -g wrangler');
	}
}

function validateAstroConfig(): void {
	try {
		const astroPath = resolve(process.cwd(), 'astro.config.mjs');
		const astroContent = readFileSync(astroPath, 'utf-8');

		if (astroContent.includes('@astrojs/cloudflare')) {
			addResult('Astro Config', 'cloudflare adapter', 'pass', 'Cloudflare adapter configured');
		} else {
			addResult('Astro Config', 'cloudflare adapter', 'fail', 'Cloudflare adapter not configured');
		}

		if (astroContent.includes('adapter: cloudflare()')) {
			addResult('Astro Config', 'adapter usage', 'pass', 'Adapter properly used');
		} else {
			addResult('Astro Config', 'adapter usage', 'fail', 'Adapter not properly configured');
		}
	} catch (error) {
		addResult('Astro Config', 'parse', 'fail', `Failed to parse: ${String(error)}`);
	}
}

function validateEnvironmentFiles(): void {
	const envFiles = ['.env.example', '.env.production'];

	for (const file of envFiles) {
		const filePath = resolve(process.cwd(), file);
		if (existsSync(filePath)) {
			addResult('Environment', file, 'pass', 'Environment file exists');
		} else {
			addResult('Environment', file, 'warning', 'Environment file missing (optional)');
		}
	}
}

function validateBuildOutput(): void {
	try {
		executeCommand('npm run build:prod');

		const distPath = resolve(process.cwd(), 'dist');
		if (existsSync(distPath)) {
			addResult('Build', 'output', 'pass', 'Build successful, dist folder created');
		} else {
			addResult('Build', 'output', 'fail', 'Build completed but no dist folder found');
		}

		const workerPath = resolve(process.cwd(), 'dist/_worker.js');
		if (existsSync(workerPath)) {
			addResult('Build', 'worker file', 'pass', 'Worker file generated');
		} else {
			addResult('Build', 'worker file', 'fail', 'Worker file not generated');
		}
	} catch (error) {
		addResult('Build', 'process', 'fail', `Build failed: ${String(error)}`);
	}
}

function printResults(): void {
	console.log('\n🔍 Environment Validation Results\n');

	const categories = [...new Set(results.map(r => r.category))];

	for (const category of categories) {
		console.log(`\n📂 ${category}`);
		console.log('─'.repeat(category.length + 3));

		const categoryResults = results.filter(r => r.category === category);

		for (const result of categoryResults) {
			const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
			console.log(`  ${icon} ${result.test}: ${result.message}`);
		}
	}

	const summary = {
		pass: results.filter(r => r.status === 'pass').length,
		fail: results.filter(r => r.status === 'fail').length,
		warning: results.filter(r => r.status === 'warning').length
	};

	console.log('\n📊 Summary');
	console.log('─────────');
	console.log(`✅ Passed: ${summary.pass}`);
	console.log(`❌ Failed: ${summary.fail}`);
	console.log(`⚠️  Warnings: ${summary.warning}`);

	if (summary.fail > 0) {
		console.log('\n❌ Validation failed. Please fix the issues above before deploying.');
		console.log('\n📋 Next steps:');
		console.log('1. Install missing dependencies: npm install');
		console.log('2. Set up Cloudflare resources: npm run cf:setup');
		console.log('3. Authenticate with Wrangler: wrangler login');
		console.log('4. Re-run validation: tsx scripts/validate-environment.ts');
		process.exit(1);
	} else {
		console.log('\n🎉 Environment validation passed! Ready for deployment.');
		console.log('\n🚀 To deploy:');
		console.log('• Development: npm run deploy:dev');
		console.log('• Staging: npm run deploy:staging');
		console.log('• Production: npm run deploy');
	}
}

async function main(): Promise<void> {
	console.log('🔍 Validating Cloudflare Workers deployment environment...\n');

	validateFiles();
	validatePackageJson();
	validateWranglerToml();
	validateWranglerCLI();
	validateAstroConfig();
	validateEnvironmentFiles();
	validateBuildOutput();

	printResults();
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
	main();
}

export { main as validateEnvironment };
