---
import Layout from '../../layouts/Layout.astro';

// This page should not be prerendered as it's an admin page requiring dynamic data
export const prerender = false;

// Check if user has admin/moderator access
const auth = (Astro.locals as any).auth;
if (!auth?.isAuthenticated) {
	return Astro.redirect('/login?redirect=' + encodeURIComponent(Astro.url.pathname));
}

// Check if user has required role (admin or moderator)
const hasRequiredRole = auth.roles?.includes('ROLE_ADMIN') || auth.roles?.includes('ROLE_MODERATOR');
if (!hasRequiredRole) {
	return Astro.redirect('/admin/dashboard?error=access_denied');
}

const isAdmin = auth.roles?.includes('ROLE_ADMIN');

// Get database connection
const db = (Astro.locals as any).db;

// Fetch real economists data from database
let economistsData: any[] = [];
let economistStats = {
	total: 0,
	active: 0,
	averageAccuracy: 0,
	totalPredictions: 0
};

try {
	if (db) {
		// Import database functions
		const { desc } = await import('drizzle-orm');
		const { economists } = await import('../../server/db/schema');

		// Fetch economists with their recent activity
		const economistsList = await db
			.select({
				id: economists.id,
				name: economists.name,
				youtubeChannelName: economists.youtubeChannelName,
				subscriberCount: economists.subscriberCount,
				totalPredictions: economists.totalPredictions,
				correctPredictions: economists.correctPredictions,
				accuracyScore: economists.accuracyScore,
				trustScore: economists.trustScore,
				isVerified: economists.isVerified,
				isActive: economists.isActive,
				updatedAt: economists.updatedAt
			})
			.from(economists)
			.orderBy(desc(economists.accuracyScore));

		// Calculate stats
		const totalEconomists = economistsList.length;
		const activeEconomists = economistsList.filter((e: any) => e.isActive).length;
		const totalPredictionsSum = economistsList.reduce((sum: number, e: any) => sum + (e.totalPredictions || 0), 0);
		const avgAccuracy = economistsList.length > 0
			? economistsList.reduce((sum: number, e: any) => sum + (e.accuracyScore || 0), 0) / economistsList.length
			: 0;

		economistsData = economistsList.map((economist: any) => ({
			id: economist.id,
			name: economist.name,
			title: 'Ekonomist', // Default title since we don't have specific titles in DB
			channel: economist.youtubeChannelName || 'N/A',
			subscribers: economist.subscriberCount || 0,
			totalPredictions: economist.totalPredictions || 0,
			accuracyRate: Math.round((economist.accuracyScore || 0) * 100),
			status: economist.isActive ? 'active' : 'inactive',
			lastActivity: economist.updatedAt ? new Date(economist.updatedAt).toISOString().split('T')[0] : 'N/A',
			isVerified: economist.isVerified
		}));

		economistStats = {
			total: totalEconomists,
			active: activeEconomists,
			averageAccuracy: Math.round(avgAccuracy * 100),
			totalPredictions: totalPredictionsSum
		};
	}
} catch (error) {
	console.error('Error fetching economists data:', error);
	// Keep empty data structure for graceful fallback
}
---

<Layout title="Economist Management - Ekonomist Yönetimi">
	<main class="min-h-screen bg-gray-50 dark:bg-gray-900">
		<!-- Header -->
		<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between items-center py-4">
					<div class="flex items-center space-x-4">
						<div class="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
							<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
								<path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
							</svg>
						</div>
						<div>
							<h1 class="text-2xl font-bold text-gray-900 dark:text-white">
								Ekonomist Yönetimi
							</h1>
							<p class="text-sm text-gray-500 dark:text-gray-400">
								Ekonomist profilleri ve performans takibi
							</p>
						</div>
					</div>
					<div class="flex items-center space-x-3">
						<a
							href="/admin/dashboard"
							class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-sm font-medium transition-colors"
						>
							← Admin Panel
						</a>
					</div>
				</div>
			</div>
		</header>

		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
			<!-- Stats -->
			<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Toplam Ekonomist</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{economistStats.total}</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Aktif Ekonomist</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{economistStats.active}</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Ortalama Doğruluk</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">
								{economistStats.averageAccuracy}%
							</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V7H3a1 1 0 01-1-1V5a1 1 0 011-1h4z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Toplam Tahmin</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">
								{economistStats.totalPredictions}
							</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Economist List -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
				<div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
					<div class="flex justify-between items-center">
						<h3 class="text-lg font-medium text-gray-900 dark:text-white">Ekonomist Listesi</h3>
						<button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
							+ Yeni Ekonomist
						</button>
					</div>
				</div>

				<div class="overflow-x-auto">
					<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
						<thead class="bg-gray-50 dark:bg-gray-700">
							<tr>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Ekonomist
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Kanal
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Abone Sayısı
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Tahminler
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Doğruluk
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Durum
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									İşlemler
								</th>
							</tr>
						</thead>
						<tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
							{economistsData.map((economist: any) => (
								<tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
									<td class="px-6 py-4 whitespace-nowrap">
										<div class="flex items-center">
											<div class="flex-shrink-0 h-10 w-10">
												<div class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
													<svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
														<path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
													</svg>
												</div>
											</div>
											<div class="ml-4">
												<div class="text-sm font-medium text-gray-900 dark:text-white">{economist.name}</div>
												<div class="text-sm text-gray-500 dark:text-gray-400">{economist.title}</div>
											</div>
										</div>
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<div class="text-sm text-gray-900 dark:text-white">{economist.channel}</div>
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<div class="text-sm text-gray-900 dark:text-white">
											{economist.subscribers.toLocaleString('tr-TR')}
										</div>
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<div class="text-sm text-gray-900 dark:text-white">{economist.totalPredictions}</div>
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<div class={`text-sm font-semibold ${
											economist.accuracyRate >= 90 ? 'text-green-600 dark:text-green-400' :
											economist.accuracyRate >= 80 ? 'text-blue-600 dark:text-blue-400' :
											economist.accuracyRate >= 70 ? 'text-yellow-600 dark:text-yellow-400' :
											'text-red-600 dark:text-red-400'
										}`}>
											{economist.accuracyRate}%
										</div>
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
											economist.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
											'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
										}`}>
											{economist.status === 'active' ? 'Aktif' : 'Pasif'}
										</span>
									</td>
									<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<div class="flex space-x-2">
											<button class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">Düzenle</button>
											<button class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">Detay</button>
											{isAdmin && (
												<button class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">Sil</button>
											)}
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</main>
</Layout>
