<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 border border-gray-200 dark:border-gray-700 transition-colors">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-xl font-bold text-gray-900 dark:text-white">Editörlük <PERSON></h2>
      <span class="text-sm text-gray-500 dark:text-gray-400">Özenle Seçilmiş</span>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div v-for="n in 4" :key="n" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 animate-pulse">
        <div class="flex items-start space-x-3">
          <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg"></div>
          <div class="flex-1">
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-full mb-2"></div>
            <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-8">
      <p class="text-red-600 dark:text-red-400 text-sm mb-2">Makaleler yüklenirken hata oluştu.</p>
      <button
        class="px-3 py-1 bg-amber-600 hover:bg-amber-700 text-white rounded text-xs transition-colors"
        @click="fetchArticles"
      >
        Tekrar Dene
      </button>
    </div>

    <!-- Articles Grid -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <a
        v-for="article in articles"
        :key="article.id"
        :href="getArticleUrl(article)"
        class="block bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-650 transition-colors cursor-pointer"
      >
        <article class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <h3 class="text-gray-900 dark:text-white font-medium text-sm mb-1 line-clamp-2">{{ article.title }}</h3>
            <p class="text-gray-600 dark:text-gray-400 text-xs mb-2 line-clamp-2">{{ article.summary }}</p>
            <div class="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-500">
              <span>{{ article.category }}</span>
              <span>•</span>
              <span>{{ article.readTime }}</span>
            </div>
          </div>
        </article>
      </a>
    </div>

    <div class="mt-4 text-center">
      <a
        href="/articles"
        class="text-amber-400 hover:text-amber-300 text-sm transition-colors"
      >
        Tüm Makaleler →
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { apiEndpoints } from '@/lib/api-config'
import { parseApiResponse, type Article } from '@/lib/api-types'

const loading = ref(true)
const error = ref(false)
const articles = ref<Article[]>([])

const getArticleUrl = (article: Article) => {
  // If article has a slug, use it; otherwise, create one from title
  if (article.slug) {
    return `/articles/${article.slug}`
  }

  // Create a simple slug from title for fallback articles
  const slug = article.title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()

  return `/articles/${slug}`
}

const fetchArticles = async () => {
  try {
    loading.value = true
    error.value = false

    const response = await fetch(apiEndpoints.articles.curated)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await parseApiResponse<Article[]>(response)

    if (data.status !== 'success' || !data.data) {
      throw new Error(data.message || 'Failed to fetch articles')
    }

    articles.value = data.data
  } catch (err) {
    console.error('Error fetching curated articles:', err)
    error.value = true
    // Fallback to default data
    articles.value = [
      {
        id: 1,
        title: "Merkez Bankası Faiz Kararının Piyasalara Etkisi",
        summary: "Son faiz kararının borsa ve döviz kurları üzerindeki etkilerini detaylı analiz ediyoruz.",
        category: "Para Politikası",
        readTime: "5 dk",
        slug: "merkez-bankasi-faiz-karari-etkisi"
      },
      {
        id: 2,
        title: "Kripto Para Düzenlemelerinde Yeni Gelişmeler",
        summary: "Türkiye ve dünya genelinde kripto para düzenlemelerindeki son değişiklikler.",
        category: "Düzenleme",
        readTime: "3 dk",
        slug: "kripto-para-duzenlemeleri"
      },
      {
        id: 3,
        title: "Enflasyon Hedefi ve Gerçekleşen Veriler",
        summary: "2025 yılı enflasyon hedefleri ve gerçekleşen veriler arasındaki farkın analizi.",
        category: "Enflasyon",
        readTime: "4 dk",
        slug: "enflasyon-hedefi-gerceklesen-veriler"
      },
      {
        id: 4,
        title: "Teknoloji Hisselerinde Yeni Fırsatlar",
        summary: "Borsa İstanbul'da işlem gören teknoloji şirketlerinin değerlendirmesi.",
        category: "Hisse Analizi",
        readTime: "6 dk",
        slug: "teknoloji-hisselerinde-firsatlar"
      }
    ]
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchArticles()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
