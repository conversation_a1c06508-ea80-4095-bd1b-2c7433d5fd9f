import js from '@eslint/js';
import tseslint from '@typescript-eslint/eslint-plugin';
import tsparser from '@typescript-eslint/parser';
import astroEslintParser from 'astro-eslint-parser';
import astroEslintPlugin from 'eslint-plugin-astro';
import vueEslintPlugin from 'eslint-plugin-vue';
import vueEslintParser from 'vue-eslint-parser';
import prettierConfig from 'eslint-config-prettier';

export default [
	js.configs.recommended,
	// Base configuration for JavaScript/TypeScript files
	{
		files: ['**/*.{js,mjs,cjs,ts,tsx}'],
		languageOptions: {
			ecmaVersion: 'latest',
			sourceType: 'module',
			parser: tsparser,
			globals: {
				browser: true,
				es2022: true,
				node: true,
				process: 'readonly',
				__dirname: 'readonly',
				__filename: 'readonly',
				global: 'readonly',
				Buffer: 'readonly',
				console: 'readonly',
				Response: 'readonly',
				Request: 'readonly',
				fetch: 'readonly',
				document: 'readonly',
				window: 'readonly',
				Blob: 'readonly',
				URL: 'readonly',
				confirm: 'readonly',
			},
		},
		plugins: {
			'@typescript-eslint': tseslint,
		},
		rules: {
			// General rules
			'prefer-const': 'error',
			'no-var': 'error',
			'no-unused-vars': 'off', // Turn off base rule for TypeScript
			'@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
			'object-curly-spacing': ['error', 'always'],
			'array-bracket-spacing': ['error', 'never'],
			// TypeScript specific rules
			'@typescript-eslint/no-explicit-any': 'warn',
			'@typescript-eslint/explicit-function-return-type': 'off',
			'@typescript-eslint/explicit-module-boundary-types': 'off',
			'@typescript-eslint/no-inferrable-types': 'error',
		},
	},
	// Astro files configuration
	...astroEslintPlugin.configs.recommended,
	{
		files: ['**/*.astro'],
		languageOptions: {
			parser: astroEslintParser,
			parserOptions: {
				parser: tsparser,
				extraFileExtensions: ['.astro'],
			},
		},
	},
	// Vue files configuration
	...vueEslintPlugin.configs['flat/recommended'],
	{
		files: ['**/*.vue'],
		languageOptions: {
			parser: vueEslintParser,
			parserOptions: {
				parser: tsparser,
			},
			globals: {
				fetch: 'readonly',
				console: 'readonly',
				document: 'readonly',
				window: 'readonly',
				Blob: 'readonly',
				URL: 'readonly',
				confirm: 'readonly',
			},
		},
		rules: {
			// Vue specific rules
			'vue/multi-word-component-names': 'off',
			'vue/no-v-html': 'warn',
			'vue/require-default-prop': 'off',
		},
	},
	// CommonJS files configuration
	{
		files: ['**/*.cjs'],
		languageOptions: {
			ecmaVersion: 'latest',
			sourceType: 'script',
			globals: {
				node: true,
				module: true,
				require: true,
			},
		},
	},
	// Configuration files with relaxed rules
	{
		files: ['**/*.config.{js,ts,mjs}', '**/env.d.ts', '**/shims-*.d.ts'],
		rules: {
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-unused-vars': 'off',
			'no-undef': 'off',
		},
	},
	// Prettier compatibility - disable conflicting rules
	prettierConfig,

	// Node.js script files configuration
	{
		files: ['scripts/**/*.js', 'scripts/**/*.mjs'],
		languageOptions: {
			ecmaVersion: 'latest',
			sourceType: 'module',
			globals: {
				console: 'readonly',
				process: 'readonly',
				Buffer: 'readonly',
				__dirname: 'readonly',
				__filename: 'readonly',
				global: 'readonly',
			},
		},
		rules: {
			'no-console': 'off',
			'no-undef': 'off',
			'@typescript-eslint/no-unused-vars': 'off',
		},
	},

	// Ignored files
	{
		ignores: [
			'dist/',
			'node_modules/',
			'.astro/',
			'drizzle/',
			'wrangler.toml',
			'playwright-report/',
			'test-results/',
			'.wrangler/',
			'**/.wrangler/**',
			'**/tmp/**',
			'**/*.d.ts',
		],
	},
];
