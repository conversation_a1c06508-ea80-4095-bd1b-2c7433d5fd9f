import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.describe('Admin Verification Panel', () => {
  // Helper function to login as admin
  async function loginAsAdmin(page: Page) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'testpass123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  }

  // Helper function to login as moderator
  async function loginAsModerator(page: Page) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'testpass123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  }

  // Helper function to login as regular user
  async function loginAsUser(page: Page) {
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'testpass123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  }

  test.describe('Access Control', () => {
    test('should allow admin access to verification panel', async ({ page }) => {
      await loginAsAdmin(page);

      // Navigate to admin verification page
      await page.goto('/admin/verification');

      // Should successfully load the page
      await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toContainText('Automated Verification System');
      await expect(page.locator('text=Monitor and manage the automated prediction verification system')).toBeVisible();
    });

    test('should allow moderator access to verification panel', async ({ page }) => {
      await loginAsModerator(page);

      // Navigate to admin verification page
      await page.goto('/admin/verification');

      // Should successfully load the page (specific selector to avoid browser dev tools)
      await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toContainText('Automated Verification System');
      await expect(page.locator('text=Monitor and manage the automated prediction verification system')).toBeVisible();
    });

    test('should deny regular user access to verification panel', async ({ page }) => {
      await loginAsUser(page);

      // Try to navigate to admin verification page
      await page.goto('/admin/verification');

      // Should be redirected to dashboard with access denied error
      await page.waitForURL('**/dashboard*');
      expect(page.url()).toContain('error=access_denied');
    });

    test('should redirect unauthenticated users to login', async ({ page }) => {
      // Try to access admin page without login
      await page.goto('/admin/verification');

      // Should be redirected to login page
      await page.waitForURL('**/login*');
      expect(page.url()).toContain('/login');
      expect(page.url()).toContain('redirect=');
    });
  });

  test.describe('Page Content and Components', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto('/admin/verification');
    });

    test('should display main page structure correctly', async ({ page }) => {
      // Check main heading (specific selector to avoid dev tools interference)
      await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toContainText('Automated Verification System');

      // Check description
      await expect(page.locator('text=Monitor and manage the automated prediction verification system')).toBeVisible();

      // Check for main dashboard components (look for specific component content)
      await expect(page.locator('text=Automated Verification Dashboard')).toBeVisible({ timeout: 10000 });

      // Check for data sources status which should mention TCMB
      await expect(page.locator('text=Data Sources Status')).toBeVisible({ timeout: 10000 });
    });

    test('should display system information section', async ({ page }) => {
      // Check System Information section
      await expect(page.locator('text=System Information')).toBeVisible();

      // Check Supported Data Sources
      await expect(page.locator('text=Supported Data Sources')).toBeVisible();
      await expect(page.locator('text=TCMB (Turkish Central Bank)')).toBeVisible();
      await expect(page.locator('text=TurkStat - Inflation data')).toBeVisible();
      await expect(page.locator('text=Borsa Istanbul (BIST)')).toBeVisible();

      // Check Verification Methods
      await expect(page.locator('text=Verification Methods')).toBeVisible();
      await expect(page.locator('text=Exact Value:')).toBeVisible();
      await expect(page.locator('text=Range Predictions:')).toBeVisible();
      await expect(page.locator('text=Increase/Decrease:')).toBeVisible();
    });

    test('should display "How It Works" section', async ({ page }) => {
      await expect(page.locator('text=How It Works')).toBeVisible();
      await expect(page.locator('text=The automated verification system runs periodically')).toBeVisible();
    });

    test('should display admin notes section', async ({ page }) => {
      await expect(page.locator('text=Admin Notes')).toBeVisible();
      await expect(page.locator('text=Verification runs automatically every 6 hours')).toBeVisible();
      await expect(page.locator('text=Manual verification can be triggered')).toBeVisible();
    });
  });

  test.describe('Dashboard Components', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto('/admin/verification');
    });

    test('should load automated verification dashboard component', async ({ page }) => {
      // Wait for the Vue component to load by looking for specific text content
      await page.waitForSelector('text=Automated Verification Dashboard', { timeout: 15000 });

      // The component should be visible
      const dashboard = page.locator('text=Automated Verification Dashboard');
      await expect(dashboard).toBeVisible();

      // Should also show the run verification button
      await expect(page.locator('text=Run Automated Verification')).toBeVisible();
    });

    test('should load data sources status section', async ({ page }) => {
      // Wait for the Vue component to load by looking for data sources content
      await page.waitForSelector('text=Data Sources Status', { timeout: 15000 });

      // The data sources section should be visible
      const dataSourcesSection = page.locator('text=Data Sources Status');
      await expect(dataSourcesSection).toBeVisible();

      // Should show TCMB as one of the data sources - use more specific selector to avoid ambiguity
      await expect(page.locator('text=TCMB (Turkish Central Bank)')).toBeVisible();
    });

    test('should handle component loading states gracefully', async ({ page }) => {
      // Check that the page doesn't show error messages during component loading
      await expect(page.locator('text=Error loading')).not.toBeVisible();
      await expect(page.locator('text=Failed to load')).not.toBeVisible();

      // Components should eventually load (look for their specific content)
      await page.waitForFunction(() => {
        return document.body.textContent?.includes('Automated Verification Dashboard') ||
               document.body.textContent?.includes('TCMB Exchange Rates');
      }, { timeout: 20000 });
    });
  });

  test.describe('Data Sources Integration', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto('/admin/verification');
    });

    test('should display data source status indicators', async ({ page }) => {
      // Check for status indicators (green/yellow dots)
      const greenIndicators = page.locator('.bg-green-500.rounded-full');
      const yellowIndicators = page.locator('.bg-yellow-500.rounded-full');

      // Should have at least some status indicators
      const totalIndicators = await greenIndicators.count() + await yellowIndicators.count();
      expect(totalIndicators).toBeGreaterThan(0);
    });

    test('should show TCMB integration status', async ({ page }) => {
      // TCMB should be listed as a supported data source
      await expect(page.locator('text=TCMB (Turkish Central Bank)')).toBeVisible();

      // Should have a status indicator for TCMB (just check the first one)
      const tcmbLine = page.locator('text=TCMB (Turkish Central Bank)').locator('..');
      await expect(tcmbLine.locator('.rounded-full').first()).toBeVisible();
    });
  });

  test.describe('Navigation and UI', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
    });

    test('should navigate to verification page from dashboard', async ({ page }) => {
      await page.goto('/dashboard');

      // Look for admin panel link or verification link
      // This might be in the header, sidebar, or as a dashboard widget
      const adminLink = page.locator('a[href*="/admin"], a[href*="/verification"]').first();

      if (await adminLink.isVisible()) {
        await adminLink.click();
        await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toContainText('Automated Verification System');
      } else {
        // If no link exists, navigate directly and verify it works
        await page.goto('/admin/verification');
        await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toContainText('Automated Verification System');
      }
    });

    test('should maintain responsive design on mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/admin/verification');

      // Page should still be accessible and readable (specific selector to avoid dev tools)
      await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toBeVisible();
      await expect(page.locator('text=System Information')).toBeVisible();

      // Grid should adapt to mobile (check for stacked layout - just first one)
      const gridContainer = page.locator('.grid-cols-1.md\\:grid-cols-2').first();
      await expect(gridContainer).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
    });

    test('should handle API failures gracefully', async ({ page }) => {
      // Navigate to the page
      await page.goto('/admin/verification');

      // Even if API calls fail, the static content should be visible (specific selector to avoid dev tools)
      await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toContainText('Automated Verification System');
      await expect(page.locator('text=System Information')).toBeVisible();

      // Should not show critical errors that break the page
      await expect(page.locator('text=Internal Server Error')).not.toBeVisible();
      // Use more specific text to avoid matching content with "500"
      await expect(page.locator('text=HTTP 500', { hasText: /error/i })).not.toBeVisible();
    });

    test('should display proper loading states', async ({ page }) => {
      await page.goto('/admin/verification');

      // Page should load without showing broken components
      await page.waitForLoadState('networkidle');

      // Main content should be visible (specific selector to avoid dev tools)
      await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toBeVisible();
      await expect(page.locator('main.min-h-screen')).toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should load admin verification page quickly', async ({ page }) => {
      const startTime = Date.now();

      await loginAsAdmin(page);
      await page.goto('/admin/verification');

      // Wait for main content to be visible (specific selector to avoid dev tools)
      await expect(page.locator('main.min-h-screen .max-w-7xl h1')).toBeVisible();

      const loadTime = Date.now() - startTime;

      // Should load within reasonable time (be generous since it includes login)
      expect(loadTime).toBeLessThan(10000); // 10 seconds including login
    });
  });
});
