<template>
	<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 space-y-4">
		<!-- Prediction Header -->
		<div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
			<div class="flex-1">
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
					{{ prediction.title }}
				</h3>
				<p class="text-sm text-gray-600 dark:text-gray-300 mb-3">
					{{ prediction.description }}
				</p>

				<!-- Economist Info -->
				<div class="flex items-center gap-3 mb-3">
					<div class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
						{{ prediction.economist.name.charAt(0) }}
					</div>
					<div>
						<p class="text-sm font-medium text-gray-900 dark:text-white">
							{{ prediction.economist.name }}
							<span v-if="prediction.economist.isVerified" class="ml-1 text-amber-500">✓</span>
						</p>
						<p v-if="prediction.economist.trustScore" class="text-xs text-gray-500 dark:text-gray-400">
							Trust Score: {{ prediction.economist.trustScore }}%
						</p>
					</div>
				</div>
			</div>

			<!-- Priority Badge -->
			<div class="flex flex-col items-end gap-2">
				<span
					:class="[
						'px-3 py-1 rounded-full text-xs font-medium',
						prediction.verificationPriority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
						prediction.verificationPriority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
						'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
					]"
				>
					{{ prediction.verificationPriority.toUpperCase() }} Priority
				</span>
				<span class="text-xs text-gray-500 dark:text-gray-400">
					{{ prediction.daysSinceTarget > 0 ?
						`${prediction.daysSinceTarget} days overdue` :
						`Due in ${Math.abs(prediction.daysSinceTarget)} days`
					}}
				</span>
			</div>
		</div>

		<!-- Prediction Details -->
		<div class="border-t border-gray-200 dark:border-gray-600 pt-4 space-y-3">
			<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
				<div>
					<span class="font-medium text-gray-700 dark:text-gray-300">Category:</span>
					<span class="ml-2 text-gray-900 dark:text-white">{{ prediction.category }}</span>
				</div>
				<div>
					<span class="font-medium text-gray-700 dark:text-gray-300">Type:</span>
					<span class="ml-2 text-gray-900 dark:text-white">{{ prediction.predictionType }}</span>
				</div>
				<div>
					<span class="font-medium text-gray-700 dark:text-gray-300">Target Date:</span>
					<span class="ml-2 text-gray-900 dark:text-white">
						{{ new Date(prediction.targetDate).toLocaleDateString('tr-TR') }}
					</span>
				</div>
				<div v-if="prediction.confidenceLevel">
					<span class="font-medium text-gray-700 dark:text-gray-300">Confidence:</span>
					<span class="ml-2 text-gray-900 dark:text-white">{{ prediction.confidenceLevel }}%</span>
				</div>
			</div>

			<!-- Target Value Display -->
			<div v-if="prediction.targetValue" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
				<span class="font-medium text-gray-700 dark:text-gray-300 block mb-2">Target Value:</span>
				<div class="text-sm text-gray-900 dark:text-white">
					<pre class="whitespace-pre-wrap">{{ formatTargetValue(prediction.targetValue) }}</pre>
				</div>
			</div>
		</div>

		<!-- Verification Form -->
		<div class="border-t border-gray-200 dark:border-gray-600 pt-4">
			<h4 class="font-medium text-gray-900 dark:text-white mb-3">Verification</h4>

			<div class="space-y-4">
				<!-- Status Selection -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						Verification Status
					</label>
					<select
						v-model="verificationData.status"
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
							   bg-white dark:bg-gray-700 text-gray-900 dark:text-white
							   focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
					>
						<option value="">Select status...</option>
						<option value="verified_correct">Verified Correct</option>
						<option value="verified_incorrect">Verified Incorrect</option>
						<option value="partially_verified">Partially Correct</option>
						<option value="expired">Expired/No Data</option>
					</select>
				</div>

				<!-- Actual Value (for partially verified) -->
				<div v-if="verificationData.status === 'partially_verified'">
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						Actual Value
					</label>
					<input
						v-model="verificationData.actualValue"
						type="number"
						step="any"
						placeholder="Enter the actual value..."
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
							   bg-white dark:bg-gray-700 text-gray-900 dark:text-white
							   focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
					/>
				</div>

				<!-- Verification Source -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						Verification Source
					</label>
					<input
						v-model="verificationData.verificationSource"
						type="url"
						placeholder="https://example.com/official-data"
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
							   bg-white dark:bg-gray-700 text-gray-900 dark:text-white
							   focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
					/>
				</div>

				<!-- Verification Notes -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						Verification Notes
					</label>
					<textarea
						v-model="verificationData.verificationNotes"
						rows="3"
						placeholder="Add any notes about the verification process..."
						class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
							   bg-white dark:bg-gray-700 text-gray-900 dark:text-white
							   focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
					></textarea>
				</div>

				<!-- Action Buttons -->
				<div class="flex flex-col sm:flex-row gap-3 pt-2">
					<button
						:disabled="!verificationData.status || isSubmitting"
						class="flex-1 sm:flex-none px-6 py-2 bg-amber-600 hover:bg-amber-700
							   disabled:bg-gray-400 disabled:cursor-not-allowed
							   text-white font-medium rounded-md transition-colors"
						@click="submitVerification"
					>
						{{ isSubmitting ? 'Submitting...' : 'Submit Verification' }}
					</button>
					<button
						:disabled="isSubmitting"
						class="flex-1 sm:flex-none px-6 py-2 bg-gray-600 hover:bg-gray-700
							   text-white font-medium rounded-md transition-colors"
						@click="skipVerification"
					>
						Skip for Now
					</button>
				</div>
			</div>
		</div>

		<!-- Video Link -->
		<div class="border-t border-gray-200 dark:border-gray-600 pt-4">
			<a
				:href="prediction.video.youtubeUrl"
				target="_blank"
				rel="noopener noreferrer"
				class="inline-flex items-center gap-2 text-amber-600 hover:text-amber-700 dark:text-amber-400
					   dark:hover:text-amber-300 text-sm font-medium"
			>
				<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
					<path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
				</svg>
				Watch Source Video
			</a>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// Define props
interface Prediction {
	id: number
	title: string
	description: string
	category: string
	predictionType: string
	targetValue: any
	targetDate: string
	confidenceLevel: number | null
	daysSinceTarget: number
	verificationPriority: 'high' | 'medium' | 'low'
	economist: {
		id: number
		name: string
		isVerified: boolean
		trustScore: number | null
	}
	video: {
		youtubeUrl: string
	}
}

const props = defineProps<{
	prediction: Prediction
}>()

// Define emits
const emit = defineEmits<{
	verified: [predictionId: number, verificationData: any]
	skipped: [predictionId: number]
}>()

// Reactive state
const isSubmitting = ref(false)
const verificationData = reactive({
	status: '',
	actualValue: null as number | null,
	verificationSource: '',
	verificationNotes: '',
	verificationMethod: 'manual_review'
})

// Methods
const formatTargetValue = (targetValue: any): string => {
	if (typeof targetValue === 'object') {
		return JSON.stringify(targetValue, null, 2)
	}
	return String(targetValue)
}

const submitVerification = async () => {
	if (!verificationData.status) return

	isSubmitting.value = true

	try {
		emit('verified', props.prediction.id, { ...verificationData })
	} finally {
		isSubmitting.value = false
	}
}

const skipVerification = () => {
	emit('skipped', props.prediction.id)
}
</script>
