import type { D1Database } from '@cloudflare/workers-types';
import { initDb, type DbConnection } from './index';

/**
 * Database context manager that provides smart switching between runtime and build-time environments
 */
export class DatabaseContext {
	private static instance: DatabaseContext;
	private runtimeDb: DbConnection | null = null;

	private constructor() {}

	static getInstance(): DatabaseContext {
		if (!DatabaseContext.instance) {
			DatabaseContext.instance = new DatabaseContext();
		}
		return DatabaseContext.instance;
	}

	/**
	 * Set the runtime database connection (from Astro locals or Cloudflare env)
	 */
	setRuntimeDb(d1Database: D1Database): void {
		this.runtimeDb = initDb(d1Database);
	}

	/**
	 * Get the appropriate database connection based on context
	 * Priority: Runtime DB > Global DB (wrangler) > null (fallback to static data)
	 */
	public async getDb(): Promise<DbConnection | null> {
		// Priority 1: Use runtime DB if available (SSR mode)
		if (this.runtimeDb) {
			console.log('[DatabaseContext] Using runtime database connection');
			return this.runtimeDb;
		}

		// Priority 2: Check if we're in a Cloudflare Workers environment during build
		// This happens when running under `wrangler dev` or `wrangler pages dev`
		const globalWithDb = globalThis as { DB?: D1Database };
		if (typeof globalThis.caches !== 'undefined' && globalWithDb.DB) {
			console.log('[DatabaseContext] Using global DB from Wrangler context');
			const db = initDb(globalWithDb.DB as D1Database);
			return db;
		}

		// Priority 3: For local development builds (`astro build`), we don't have access to D1
		// This is expected and we'll fall back to static data
		console.log('[DatabaseContext] No database available, will use static data');
		return null;
	}
}

/**
 * Global database context instance
 */
export const dbContext = DatabaseContext.getInstance();

/**
 * Convenience function to get database connection
 */
export async function getDb(): Promise<DbConnection | null> {
	return await dbContext.getDb();
}
