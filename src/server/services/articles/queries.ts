/**
 * Articles Queries
 *
 * SQL queries and database operations for articles.
 * Contains both raw SQL for build scripts and Drizzle ORM queries.
 */

import type { DbConnection } from '@/server/db';
import { articles } from '@/server/db';
import { eq, desc, and } from 'drizzle-orm';
import type { QueryOptions } from '../shared/types';
import { execSync } from 'child_process';

// ============================================================================
// DRIZZLE ORM QUERIES (business logic layer)
// ============================================================================

/**
 * Find active articles with options
 */
export async function findActiveArticles(db: DbConnection, options: QueryOptions = {}) {
	const { limit = 20, curated, category } = options;

	const conditions = [eq(articles.isActive, true)];

	if (curated !== undefined) {
		conditions.push(eq(articles.isCurated, curated));
	}

	if (category) {
		conditions.push(eq(articles.category, category));
	}

	return await db
		.select()
		.from(articles)
		.where(and(...conditions))
		.orderBy(desc(articles.publishedAt))
		.limit(limit);
}

/**
 * Find article by slug
 */
export async function findArticleBySlug(db: DbConnection, slug: string) {
	const result = await db
		.select()
		.from(articles)
		.where(and(eq(articles.slug, slug), eq(articles.isActive, true)))
		.limit(1);

	return result[0] || null;
}

/**
 * Find article by ID
 */
export async function findArticleById(db: DbConnection, id: number) {
	const result = await db
		.select()
		.from(articles)
		.where(and(eq(articles.id, id), eq(articles.isActive, true)))
		.limit(1);

	return result[0] || null;
}

/**
 * Count articles with filters
 */
export async function countArticles(db: DbConnection, options: QueryOptions = {}) {
	const { curated, category } = options;

	const conditions = [eq(articles.isActive, true)];

	if (curated !== undefined) {
		conditions.push(eq(articles.isCurated, curated));
	}

	if (category) {
		conditions.push(eq(articles.category, category));
	}

	const result = await db
		.select({ count: articles.id })
		.from(articles)
		.where(and(...conditions));

	return result.length;
}

// ============================================================================
// SQL EXECUTION HELPER
// ============================================================================

/**
 * Execute raw SQL query for articles (used by build scripts)
 */
export function executeArticleSQL(query: string, dbName: string, isLocal = false): unknown {
	try {
		const localFlag = isLocal ? '--local' : '--remote';
		const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Article SQL execution failed:', error);
		return [];
	}
}
