import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
	testDir: './tests/e2e',
	fullyParallel: false,
	forbidOnly: !!process.env.CI,
	retries: process.env.CI ? 2 : 1,
	workers: 3,
	globalSetup: './tests/global-setup.ts',
	reporter: [
		['list'], // Use the list reporter for console output
		['html', { open: 'on-failure', outputFolder: 'playwright-report' }], // Add HTML reporter, open when there is a failure
	],
	use: {
		baseURL: 'http://localhost:4321',
		trace: 'on',
		ignoreHTTPSErrors: true,
	},
	projects: [
		{
			name: 'chromium',
			use: { ...devices['Desktop Chrome'] },
		},
		{
			name: 'firefox',
			use: { ...devices['Desktop Firefox'] },
		},
		{
			name: 'webkit',
			use: { ...devices['Desktop Safari'] },
		},
	],
	webServer: {
		command: 'npm run dev',
		url: 'http://localhost:4321',
		reuseExistingServer: !process.env.CI,
		ignoreHTTPSErrors: true,
	},
});
