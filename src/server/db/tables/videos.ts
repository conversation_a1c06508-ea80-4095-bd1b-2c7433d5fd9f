import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { economists } from './economists';

export const videos = sqliteTable('videos', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	youtubeVideoId: text('youtube_video_id').notNull().unique(),
	title: text('title').notNull(),
	description: text('description'),
	thumbnailUrl: text('thumbnail_url'),
	duration: integer('duration'), // in seconds
	viewCount: integer('view_count'),
	likeCount: integer('like_count'),
	commentCount: integer('comment_count'),
	publishedAt: integer('published_at', { mode: 'timestamp' }).notNull(),
	economistId: integer('economist_id').notNull().references(() => economists.id),
	transcriptExtracted: integer('transcript_extracted', { mode: 'boolean' }).default(false),
	transcriptText: text('transcript_text'),
	predictionsExtracted: integer('predictions_extracted', { mode: 'boolean' }).default(false),
	analysisCompleted: integer('analysis_completed', { mode: 'boolean' }).default(false),
	isActive: integer('is_active', { mode: 'boolean' }).default(true),
	createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
	updatedAt: integer('updated_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
});
