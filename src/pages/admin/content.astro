---
import Layout from '../../layouts/Layout.astro';
import { desc, count, eq } from 'drizzle-orm';
import { predictions, videos, economists } from '../../server/db/schema';

// This page should not be prerendered as it's an admin page requiring dynamic data
export const prerender = false;

// Check if user has admin/moderator access
const auth = (Astro.locals as any).auth;
if (!auth?.isAuthenticated) {
	return Astro.redirect('/login?redirect=' + encodeURIComponent(Astro.url.pathname));
}

// Check if user has required role (admin or moderator)
const hasRequiredRole = auth.roles?.includes('ROLE_ADMIN') || auth.roles?.includes('ROLE_MODERATOR');
if (!hasRequiredRole) {
	return Astro.redirect('/dashboard?error=access_denied');
}

const isAdmin = auth.roles?.includes('ROLE_ADMIN');

// Get database connection
const db = (Astro.locals as any).db;

// Fetch real data from database
let contentData = {
	predictions: [] as any[],
	videos: [] as any[],
	stats: {
		totalPredictions: 0,
		totalVideos: 0,
		processedVideos: 0,
		pendingVideos: 0
	}
};

try {
	if (db) {
		// Fetch recent predictions with economist info
		const recentPredictions = await db
			.select({
				id: predictions.id,
				title: predictions.title,
				economist: economists.name,
				category: predictions.category,
				status: predictions.status,
				targetDate: predictions.targetDate,
				createdAt: predictions.createdAt
			})
			.from(predictions)
			.innerJoin(economists, eq(predictions.economistId, economists.id))
			.where(eq(economists.isActive, true))
			.orderBy(desc(predictions.createdAt))
			.limit(10);

		// Fetch recent videos with economist info and prediction counts
		const recentVideos = await db
			.select({
				videoId: videos.id,
				title: videos.title,
				economist: economists.name,
				duration: videos.duration,
				viewCount: videos.viewCount,
				publishedAt: videos.publishedAt,
				transcriptExtracted: videos.transcriptExtracted,
				predictionsExtracted: videos.predictionsExtracted,
				analysisCompleted: videos.analysisCompleted
			})
			.from(videos)
			.innerJoin(economists, eq(videos.economistId, economists.id))
			.where(eq(economists.isActive, true))
			.orderBy(desc(videos.publishedAt))
			.limit(10);

		// Get prediction counts for each video
		const videoPredictionCounts = await db
			.select({
				videoId: predictions.videoId,
				count: count()
			})
			.from(predictions)
			.groupBy(predictions.videoId);

		// Helper function to format duration
		const formatDuration = (seconds: number | null) => {
			if (!seconds) return 'N/A';
			const minutes = Math.floor(seconds / 60);
			const remainingSeconds = seconds % 60;
			return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
		};

		// Helper function to determine video status
		const getVideoStatus = (video: any) => {
			if (video.analysisCompleted) return 'processed';
			if (video.predictionsExtracted) return 'processing';
			if (video.transcriptExtracted) return 'processing';
			return 'pending';
		};

		// Combine videos with prediction counts
		const videosWithCounts = recentVideos.map((video: any) => {
			const predictionCount = videoPredictionCounts.find((p: any) => p.videoId === video.videoId)?.count || 0;
			return {
				id: video.videoId,
				title: video.title,
				economist: video.economist,
				duration: formatDuration(video.duration),
				viewCount: video.viewCount || 0,
				publishedAt: video.publishedAt,
				status: getVideoStatus(video),
				predictionsCount: predictionCount
			};
		});

		// Get stats
		const [predictionStats] = await db.select({ count: count() }).from(predictions);
		const [videoStats] = await db.select({ count: count() }).from(videos);

		const processedCount = videosWithCounts.filter((v: any) => v.status === 'processed').length;
		const pendingCount = videosWithCounts.filter((v: any) => v.status === 'pending' || v.status === 'processing').length;

		contentData = {
			predictions: recentPredictions.map((p: any) => ({
				id: p.id,
				title: p.title,
				economist: p.economist,
				category: p.category,
				status: p.status,
				targetDate: p.targetDate?.toISOString().split('T')[0] || '',
				createdAt: p.createdAt?.toISOString().split('T')[0] || ''
			})),
			videos: videosWithCounts,
			stats: {
				totalPredictions: predictionStats.count,
				totalVideos: videoStats.count,
				processedVideos: processedCount,
				pendingVideos: pendingCount
			}
		};
	}
} catch (error) {
	console.error('Error fetching content data:', error);
	// Keep empty data structure for graceful fallback
}
---

<Layout title="Content Management - İçerik Yönetimi">
	<main class="min-h-screen bg-gray-50 dark:bg-gray-900">
		<!-- Header -->
		<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between items-center py-4">
					<div class="flex items-center space-x-4">
						<div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
							<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
								<path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4zM3 8a1 1 0 000 2v6a2 2 0 002 2h10a2 2 0 002-2v-6a1 1 0 100-2H3zm8 7a1 1 0 11-2 0 1 1 0 012 0z" />
							</svg>
						</div>
						<div>
							<h1 class="text-2xl font-bold text-gray-900 dark:text-white">
								İçerik Yönetimi
							</h1>
							<p class="text-sm text-gray-500 dark:text-gray-400">
								Videolar, tahminler ve içerik denetimi
							</p>
						</div>
					</div>
					<div class="flex items-center space-x-3">
						<a
							href="/admin/dashboard"
							class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-sm font-medium transition-colors"
						>
							← Admin Panel
						</a>
					</div>
				</div>
			</div>
		</header>

		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
			<!-- Stats -->
			<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V7H3a1 1 0 01-1-1V5a1 1 0 011-1h4z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Toplam Tahmin</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{contentData.stats.totalPredictions}</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Toplam Video</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{contentData.stats.totalVideos}</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">İşlenmiş Video</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">
								{contentData.stats.processedVideos}
							</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Bekleyen İşlem</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">
								{contentData.stats.pendingVideos}
							</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Content Tables -->
			<div class="grid grid-cols-1 xl:grid-cols-2 gap-8">
				<!-- Predictions Management -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
					<div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
						<div class="flex justify-between items-center">
							<h3 class="text-lg font-medium text-gray-900 dark:text-white">Son Tahminler</h3>
							<a
								href="/admin/verification"
								class="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 text-sm font-medium"
							>
								Tümünü Gör →
							</a>
						</div>
					</div>

					<div class="overflow-x-auto">
						<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
							<thead class="bg-gray-50 dark:bg-gray-700">
								<tr>
									<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
										Tahmin
									</th>
									<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
										Durum
									</th>
									<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
										İşlem
									</th>
								</tr>
							</thead>
							<tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
								{contentData.predictions.map((prediction: any) => (
									<tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
										<td class="px-6 py-4">
											<div>
												<div class="text-sm font-medium text-gray-900 dark:text-white">{prediction.title}</div>
												<div class="text-sm text-gray-500 dark:text-gray-400">{prediction.economist} • {prediction.category}</div>
											</div>
										</td>
										<td class="px-6 py-4 whitespace-nowrap">
											<span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
												prediction.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
												prediction.status === 'verified_correct' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
												prediction.status === 'verified_incorrect' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
												'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
											}`}>
												{prediction.status === 'pending' ? 'Bekliyor' :
												 prediction.status === 'verified_correct' ? 'Doğru' :
												 prediction.status === 'verified_incorrect' ? 'Yanlış' : prediction.status}
											</span>
										</td>
										<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
											<button class="text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300">Düzenle</button>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>

				<!-- Video Management -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
					<div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
						<div class="flex justify-between items-center">
							<h3 class="text-lg font-medium text-gray-900 dark:text-white">Son Videolar</h3>
							<button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
								+ Video Ekle
							</button>
						</div>
					</div>

					<div class="overflow-x-auto">
						<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
							<thead class="bg-gray-50 dark:bg-gray-700">
								<tr>
									<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
										Video
									</th>
									<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
										Durum
									</th>
									<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
										İşlem
									</th>
								</tr>
							</thead>
							<tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
								{contentData.videos.map((video: any) => (
									<tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
										<td class="px-6 py-4">
											<div>
												<div class="text-sm font-medium text-gray-900 dark:text-white">{video.title}</div>
												<div class="text-sm text-gray-500 dark:text-gray-400">
													{video.economist} • {video.duration} • {video.viewCount.toLocaleString('tr-TR')} görüntülenme
												</div>
												<div class="text-xs text-gray-400 dark:text-gray-500">
													{video.predictionsCount} tahmin
												</div>
											</div>
										</td>
										<td class="px-6 py-4 whitespace-nowrap">
											<span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
												video.status === 'pending' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' :
												video.status === 'processing' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
												video.status === 'processed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
												'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
											}`}>
												{video.status === 'pending' ? 'Bekliyor' :
												 video.status === 'processing' ? 'İşleniyor' :
												 video.status === 'processed' ? 'İşlendi' : video.status}
											</span>
										</td>
										<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
											<div class="flex space-x-2">
												<button class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">Görüntüle</button>
												{isAdmin && (
													<button class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">Sil</button>
												)}
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<!-- Quick Actions -->
			<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
				<!-- Content Processing -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">İçerik İşleme</h3>
					<div class="space-y-3">
						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Toplu Video İşleme</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">Bekleyen videoları işle</p>
								</div>
							</div>
						</button>

						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Tahmin Çıkarma</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">AI tahmin çıkarımı</p>
								</div>
							</div>
						</button>

						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Etiket Yönetimi</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">İçerik etiketleme</p>
								</div>
							</div>
						</button>
					</div>
				</div>

				<!-- Moderation Tools -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Moderasyon Araçları</h3>
					<div class="space-y-3">
						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Şüpheli İçerik</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">Manuel inceleme gereken</p>
								</div>
							</div>
						</button>

						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Raporlanan İçerik</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">Kullanıcı raporları</p>
								</div>
							</div>
						</button>

						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Kalite Kontrolü</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">İçerik kalite denetimi</p>
								</div>
							</div>
						</button>
					</div>
				</div>

				<!-- Reports & Analytics -->
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Raporlar & Analitik</h3>
					<div class="space-y-3">
						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">İçerik İstatistikleri</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">Detaylı analiz raporları</p>
								</div>
							</div>
						</button>

						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Performans Takibi</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">Sistem performansı</p>
								</div>
							</div>
						</button>

						<button class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
									</svg>
								</div>
								<div>
									<p class="font-medium text-gray-900 dark:text-white">Dışa Aktarma</p>
									<p class="text-sm text-gray-500 dark:text-gray-400">Veri dışa aktarım</p>
								</div>
							</div>
						</button>
					</div>
				</div>
			</div>
		</div>
	</main>
</Layout>
