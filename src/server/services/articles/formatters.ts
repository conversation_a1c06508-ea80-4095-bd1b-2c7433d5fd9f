/**
 * Articles Formatters
 *
 * Formatting functions for article data.
 * Transforms raw database data into frontend-ready formats.
 */

import { safeJsonParse, formatDateToISO, normalizeBoolean } from '../shared/formatters';

// ============================================================================
// TYPES
// ============================================================================

export interface RawArticle {
	id: number;
	title: string;
	excerpt: string | null;
	content: string | null;
	author: string;
	publishedAt: Date | string | number;
	imageUrl: string | null;
	category: string;
	readTime: number | string | null;
	slug: string | null;
	metaDescription: string | null;
	tags: string | null;
	isActive: boolean | number;
	isCurated: boolean | number;
	createdAt: Date | string | number | null;
	updatedAt: Date | string | number | null;
}

export interface FormattedArticle {
	id: number;
	title: string;
	excerpt: string | null;
	content: string;
	author: string;
	publishedAt: string;
	imageUrl: string | null;
	category: string;
	readTime: string;
	slug: string;
	metaDescription: string | null;
	tags: string[];
	isActive: boolean;
	isCurated: boolean;
	createdAt: string;
	updatedAt: string;
	url: string;
	readTimeText: string;
}

export interface ArticleForBuild {
	id: number;
	title: string;
	excerpt: string | null;
	author: string;
	publishedAt: string;
	imageUrl: string | null;
	category: string;
	readTime: string;
	slug: string;
	metaDescription: string | null;
	tags: string[];
	isActive: boolean;
	isCurated: boolean;
	createdAt: string;
	updatedAt: string;
	url: string;
	readTimeText: string;
}

export interface ArticleListItem {
	id: number;
	title: string;
	excerpt: string | null;
	author: string;
	publishedAt: string;
	imageUrl: string | null;
	category: string;
	readTime: string;
	slug: string;
	tags: string[];
	url: string;
	readTimeText: string;
}

// ============================================================================
// FORMATTERS
// ============================================================================

/**
 * Format article for frontend display
 */
export function formatArticle(article: RawArticle): FormattedArticle {
	const tags = safeJsonParse(article.tags, [] as string[]);
	const readTimeNumber =
		typeof article.readTime === 'string'
			? parseInt(article.readTime, 10) || 5
			: article.readTime || 5; // Default 5 minutes

	return {
		id: article.id,
		title: article.title,
		excerpt: article.excerpt,
		content: article.content || '',
		author: article.author,
		publishedAt: formatDateToISO(article.publishedAt) || new Date().toISOString(),
		imageUrl: article.imageUrl,
		category: article.category,
		readTime: `${readTimeNumber} dakika`,
		slug: article.slug || '',
		metaDescription: article.metaDescription,
		tags,
		isActive: normalizeBoolean(article.isActive),
		isCurated: normalizeBoolean(article.isCurated),
		createdAt: formatDateToISO(article.createdAt) || new Date().toISOString(),
		updatedAt: formatDateToISO(article.updatedAt) || new Date().toISOString(),
		url: `/articles/${article.slug || ''}`,
		readTimeText: `${readTimeNumber} dakika okuma`,
	};
}

/**
 * Format article for build-time data (lighter version)
 */
export function formatArticleForBuild(article: RawArticle): ArticleForBuild {
	const tags = safeJsonParse(article.tags, [] as string[]);
	const readTimeNumber =
		typeof article.readTime === 'string'
			? parseInt(article.readTime, 10) || 5
			: article.readTime || 5;

	return {
		id: article.id,
		title: article.title,
		excerpt: article.excerpt,
		author: article.author,
		publishedAt: formatDateToISO(article.publishedAt) || new Date().toISOString(),
		imageUrl: article.imageUrl,
		category: article.category,
		readTime: `${readTimeNumber} dakika`,
		slug: article.slug || '',
		metaDescription: article.metaDescription,
		tags,
		isActive: normalizeBoolean(article.isActive),
		isCurated: normalizeBoolean(article.isCurated),
		createdAt: formatDateToISO(article.createdAt) || new Date().toISOString(),
		updatedAt: formatDateToISO(article.updatedAt) || new Date().toISOString(),
		url: `/articles/${article.slug}`,
		readTimeText: `${readTimeNumber} dakika okuma`,
	};
}

/**
 * Format article for list display (minimal data)
 */
export function formatArticleListItem(article: RawArticle): ArticleListItem {
	const tags = safeJsonParse(article.tags, [] as string[]);
	const readTimeNumber =
		typeof article.readTime === 'string'
			? parseInt(article.readTime, 10) || 5
			: article.readTime || 5;

	return {
		id: article.id,
		title: article.title,
		excerpt: article.excerpt,
		author: article.author,
		publishedAt: formatDateToISO(article.publishedAt) || new Date().toISOString(),
		imageUrl: article.imageUrl,
		category: article.category,
		readTime: `${readTimeNumber} dakika`,
		slug: article.slug || '',
		tags,
		url: `/articles/${article.slug || ''}`,
		readTimeText: `${readTimeNumber} dakika okuma`,
	};
}

/**
 * Format article for homepage display
 */
export function formatArticleForHomepage(article: RawArticle): ArticleListItem {
	return formatArticleListItem(article);
}

/**
 * Format articles for RSS feed
 */
export function formatArticleForRSS(article: RawArticle) {
	return {
		title: article.title,
		description: article.excerpt || article.metaDescription || '',
		link: `/articles/${article.slug}`,
		pubDate: new Date(formatDateToISO(article.publishedAt) || new Date()),
		author: article.author,
		category: article.category,
	};
}

/**
 * Format article for sitemap
 */
export function formatArticleForSitemap(article: RawArticle) {
	return {
		url: `/articles/${article.slug}`,
		lastmod:
			formatDateToISO(article.updatedAt) ||
			formatDateToISO(article.publishedAt) ||
			new Date().toISOString(),
		changefreq: 'weekly' as const,
		priority: 0.7,
	};
}

/**
 * Format article for search indexing
 */
export function formatArticleForSearch(article: RawArticle) {
	const tags = safeJsonParse(article.tags, [] as string[]);

	return {
		id: article.id,
		title: article.title,
		excerpt: article.excerpt,
		content: article.content,
		author: article.author,
		category: article.category,
		tags,
		slug: article.slug,
		url: `/articles/${article.slug}`,
		publishedAt: formatDateToISO(article.publishedAt) || new Date().toISOString(),
	};
}
