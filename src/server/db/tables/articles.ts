import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const articles = sqliteTable('articles', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	excerpt: text('excerpt').notNull(),
	content: text('content'), // Full article content (optional for curated articles)
	author: text('author').notNull(),
	publishedAt: integer('published_at', { mode: 'timestamp' }).notNull(),
	imageUrl: text('image_url'),
	category: text('category').notNull(), // e.g., '<PERSON><PERSON><PERSON>', '<PERSON> Politikası', 'Küresel Ekonomi'
	readTime: text('read_time').notNull(), // e.g., '5 dakika', '8 dakika'
	slug: text('slug').unique(), // URL-friendly version of title

	// Status and curation flags
	isActive: integer('is_active', { mode: 'boolean' }).notNull().default(true),
	isCurated: integer('is_curated', { mode: 'boolean' }).notNull().default(false), // Featured articles

	// SEO and metadata
	metaDescription: text('meta_description'),
	tags: text('tags'), // JSON array of tags

	// Timestamps
	createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
	updatedAt: integer('updated_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
});
