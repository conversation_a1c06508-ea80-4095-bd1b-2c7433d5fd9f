declare module '*.vue' {
	import type { DefineComponent } from 'vue';
	const component: DefineComponent<{}, {}, any>;
	export default component;
}

// Vue 3 global API macros
import {
	defineProps,
	defineEmits,
	defineExpose,
	withDefaults
} from 'vue'

declare global {
	const defineProps: typeof import('vue').defineProps
	const defineEmits: typeof import('vue').defineEmits
	const defineExpose: typeof import('vue').defineExpose
	const withDefaults: typeof import('vue').withDefaults
}

// Additional Vue types for our project
declare module '@vue/runtime-core' {
	interface ComponentCustomProperties {
		// Add any global properties here if needed
	}
}
