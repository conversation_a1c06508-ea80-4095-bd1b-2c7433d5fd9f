/**
 * Predictions Service
 *
 * Main service for predictions domain. Provides high-level business logic operations
 * for predictions, combining queries and formatters to deliver clean APIs.
 */

import type { DbConnection } from '@/server/db';
import type { QueryOptions, ServiceResult } from '../shared/types';
import { formatDateToISO } from '../shared/formatters';

// Import queries
import {
	findPredictions,
	findPredictionById,
	findPredictionsByEconomist,
	findVerificationQueue,
	findPredictionsForVideos,
	countPredictions,
} from './queries';

// ============================================================================
// TYPES
// ============================================================================

export interface PredictionData {
	id: number;
	title: string;
	description: string;
	category: string;
	predictionType: string;
	targetValue: string | null;
	targetDate: Date | null;
	confidenceLevel: number | null;
	status: string;
	accuracyScore: number | null;
	verificationMethod: string | null;
	verificationSource: string | null;
	verifiedAt: Date | null;
	actualValue: string | null;
	accuracy: number | null;
	extractedAt: Date;
	createdAt: Date;
	updatedAt: Date;
	economist: {
		id: number;
		name: string;
		avatarUrl: string | null;
		youtubeChannelName: string;
	};
	video?: {
		id: number;
		title: string;
		youtubeVideoId: string;
	};
}

export interface PredictionListItem {
	id: number;
	title: string;
	description: string;
	category: string;
	predictionType: string;
	targetValue: string | null;
	targetDate: string | null;
	confidenceLevel: number | null;
	status: string;
	accuracyScore: number | null;
	extractedAt: string;
	economist: {
		id: number;
		name: string;
		avatarUrl: string | null;
	};
}

export type PredictionStatus =
	| 'pending'
	| 'partially_verified'
	| 'verified_correct'
	| 'verified_incorrect'
	| 'expired';

// ============================================================================
// FORMATTERS
// ============================================================================

/**
 * Format prediction for API responses
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function formatPrediction(predictionData: any): PredictionData {
	const { predictions: prediction, economists: economist, videos: video } = predictionData;

	return {
		id: prediction.id,
		title: prediction.title,
		description: prediction.description,
		category: prediction.category,
		predictionType: prediction.predictionType,
		targetValue: prediction.targetValue,
		targetDate: prediction.targetDate,
		confidenceLevel: prediction.confidenceLevel,
		status: prediction.status,
		accuracyScore: prediction.accuracyScore,
		verificationMethod: prediction.verificationMethod,
		verificationSource: prediction.verificationSource,
		verifiedAt: prediction.verifiedAt,
		actualValue: prediction.actualValue,
		accuracy: prediction.accuracy,
		extractedAt: prediction.extractedAt,
		createdAt: prediction.createdAt,
		updatedAt: prediction.updatedAt,
		economist: {
			id: economist.id,
			name: economist.name,
			avatarUrl: economist.avatarUrl,
			youtubeChannelName: economist.youtubeChannelName,
		},
		video: video
			? {
					id: video.id,
					title: video.title,
					youtubeVideoId: video.youtubeVideoId,
				}
			: undefined,
	};
}

/**
 * Format prediction for list display
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function formatPredictionListItem(predictionData: any): PredictionListItem {
	const { predictions: prediction, economists: economist } = predictionData;

	return {
		id: prediction.id,
		title: prediction.title,
		description: prediction.description,
		category: prediction.category,
		predictionType: prediction.predictionType,
		targetValue: prediction.targetValue,
		targetDate: formatDateToISO(prediction.targetDate),
		confidenceLevel: prediction.confidenceLevel,
		status: prediction.status,
		accuracyScore: prediction.accuracyScore,
		extractedAt: formatDateToISO(prediction.extractedAt) || new Date().toISOString(),
		economist: {
			id: economist.id,
			name: economist.name,
			avatarUrl: economist.avatarUrl,
		},
	};
}

// ============================================================================
// HIGH-LEVEL SERVICE FUNCTIONS
// ============================================================================

/**
 * Get all predictions with pagination
 */
export async function getAllPredictions(
	db: DbConnection,
	options: QueryOptions = {}
): Promise<ServiceResult<PredictionData>> {
	const { page = 1, pageSize = 20 } = options;
	const offset = (page - 1) * pageSize;

	const [predictionsData, total] = await Promise.all([
		findPredictions(db, { ...options, limit: pageSize, offset }),
		countPredictions(db, options),
	]);

	const totalPages = Math.ceil(total / pageSize);

	return {
		data: predictionsData.map(formatPrediction),
		pagination: {
			page,
			pageSize,
			total,
			totalPages,
			hasNext: page < totalPages,
			hasPrev: page > 1,
		},
	};
}

/**
 * Get prediction by ID
 */
export async function getPredictionById(
	db: DbConnection,
	predictionId: number
): Promise<PredictionData | null> {
	const predictionData = await findPredictionById(db, predictionId);
	return predictionData ? formatPrediction(predictionData) : null;
}

/**
 * Get predictions by economist
 */
export async function getPredictionsByEconomist(
	db: DbConnection,
	economistId: number,
	options: QueryOptions = {}
): Promise<PredictionListItem[]> {
	const predictions = await findPredictionsByEconomist(db, economistId, options);
	return predictions.map((prediction) => ({
		id: prediction.id,
		title: prediction.title,
		description: prediction.description,
		category: prediction.category,
		predictionType: prediction.predictionType,
		targetValue: prediction.targetValue,
		targetDate: formatDateToISO(prediction.targetDate),
		confidenceLevel: prediction.confidenceLevel,
		status: prediction.status,
		accuracyScore: prediction.accuracyScore,
		extractedAt: formatDateToISO(prediction.extractedAt) || new Date().toISOString(),
		economist: {
			id: economistId,
			name: '', // Will be filled by the calling function
			avatarUrl: null,
		},
	}));
}

/**
 * Get verification queue
 */
export async function getVerificationQueue(db: DbConnection): Promise<PredictionListItem[]> {
	const predictionsData = await findVerificationQueue(db);
	return predictionsData.map(formatPredictionListItem);
}

/**
 * Get predictions for videos
 */
export async function getPredictionsForVideos(db: DbConnection, videoIds: number[]) {
	const predictions = await findPredictionsForVideos(db, videoIds);
	return predictions.map((p) => ({
		id: p.id,
		title: p.title,
		description: p.description,
		category: p.category,
		predictionType: p.predictionType,
		targetValue: p.targetValue,
		targetDate: p.targetDate,
		confidenceLevel: p.confidenceLevel,
		status: p.status,
		accuracyScore: p.accuracyScore,
		videoId: p.videoId,
	}));
}

// ============================================================================
// PREDICTIONS SERVICE OBJECT
// ============================================================================

export const PredictionsService = {
	// High-level functions
	getAllPredictions,
	getPredictionById,
	getPredictionsByEconomist,
	getVerificationQueue,
	getPredictionsForVideos,

	// Query functions (re-exported for convenience)
	findPredictions,
	findPredictionById,
	findPredictionsByEconomist,
	findVerificationQueue,
	findPredictionsForVideos,
	countPredictions,

	// Formatter functions (re-exported for convenience)
	formatPrediction,
	formatPredictionListItem,
};

export default PredictionsService;
