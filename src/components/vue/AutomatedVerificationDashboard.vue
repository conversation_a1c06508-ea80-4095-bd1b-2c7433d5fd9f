<template>
	<div class="space-y-6">
		<!-- Verification Status Dashboard -->
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
			<div class="flex items-center justify-between mb-4">
				<h2 class="text-xl font-semibold text-gray-900 dark:text-white">
					Automated Verification Dashboard
				</h2>
				<button
					:disabled="isRunningVerification"
					class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
					@click="runAutomatedVerification"
				>
					<svg
						v-if="isRunningVerification"
						class="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
					>
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
						<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
					</svg>
					{{ isRunningVerification ? 'Running Verification...' : 'Run Automated Verification' }}
				</button>
			</div>

			<!-- Statistics Cards -->
			<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
				<div class="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<svg class="h-8 w-8 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
						</div>
						<div class="ml-3">
							<p class="text-sm font-medium text-amber-600 dark:text-amber-400">Ready for Verification</p>
							<p class="text-2xl font-semibold text-amber-900 dark:text-amber-100">{{ stats?.totalReadyForVerification || 0 }}</p>
						</div>
					</div>
				</div>

				<div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<svg class="h-8 w-8 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
						</div>
						<div class="ml-3">
							<p class="text-sm font-medium text-yellow-600 dark:text-yellow-400">Urgent</p>
							<p class="text-2xl font-semibold text-yellow-900 dark:text-yellow-100">{{ stats?.urgentPredictions || 0 }}</p>
						</div>
					</div>
				</div>

				<div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<svg class="h-8 w-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
							</svg>
						</div>
						<div class="ml-3">
							<p class="text-sm font-medium text-green-600 dark:text-green-400">Last Run</p>
							<p class="text-2xl font-semibold text-green-900 dark:text-green-100">{{ lastVerificationResults?.totalProcessed || 0 }}</p>
						</div>
					</div>
				</div>

				<div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<svg class="h-8 w-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
							</svg>
						</div>
						<div class="ml-3">
							<p class="text-sm font-medium text-purple-600 dark:text-purple-400">Accuracy</p>
							<p class="text-2xl font-semibold text-purple-900 dark:text-purple-100">
								{{ lastVerificationResults?.averageAccuracy ? Math.round(lastVerificationResults.averageAccuracy * 100) + '%' : '-' }}
							</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Data Sources Status -->
			<div class="mb-6">
				<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Data Sources Status</h3>
				<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
					<div
						v-for="source in dataSources"
						:key="source.name"
						class="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
						<div
							:class="[
								'w-3 h-3 rounded-full',
								source.available ? 'bg-green-500' : 'bg-red-500'
							]">
						</div>
						<span class="text-sm font-medium text-gray-900 dark:text-white">{{ source.name }}</span>
					</div>
				</div>
			</div>

			<!-- Recent Results -->
			<div v-if="lastVerificationResults" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
				<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Last Verification Run Results</h3>
				<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
					<div>
						<span class="text-gray-600 dark:text-gray-400">Total Processed:</span>
						<span class="ml-2 font-semibold text-gray-900 dark:text-white">{{ lastVerificationResults.totalProcessed }}</span>
					</div>
					<div>
						<span class="text-gray-600 dark:text-gray-400">Correct:</span>
						<span class="ml-2 font-semibold text-green-600 dark:text-green-400">{{ lastVerificationResults.correctPredictions }}</span>
					</div>
					<div>
						<span class="text-gray-600 dark:text-gray-400">Incorrect:</span>
						<span class="ml-2 font-semibold text-red-600 dark:text-red-400">{{ lastVerificationResults.incorrectPredictions }}</span>
					</div>
					<div>
						<span class="text-gray-600 dark:text-gray-400">Sources Used:</span>
						<span class="ml-2 font-semibold text-gray-900 dark:text-white">{{ lastVerificationResults.dataSourcesUsed?.join(', ') || 'None' }}</span>
					</div>
				</div>
			</div>
		</div>

		<!-- Ready Predictions List -->
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
			<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
				Predictions Ready for Verification
			</h3>

			<div v-if="isLoading" class="flex justify-center py-8">
				<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
			</div>

			<div v-else-if="error" class="text-center py-8">
				<p class="text-red-600 dark:text-red-400 mb-4">{{ error }}</p>
				<button
					class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-amber-600 bg-amber-100 hover:bg-amber-200 dark:bg-amber-900 dark:text-amber-300 dark:hover:bg-amber-800"
					@click="fetchVerificationStatus"
				>
					Try Again
				</button>
			</div>

			<div v-else-if="readyPredictions.length === 0" class="text-center py-8">
				<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
				<p class="mt-2 text-gray-600 dark:text-gray-400">No predictions ready for verification</p>
			</div>

			<div v-else class="space-y-3">
				<div
					v-for="prediction in readyPredictions"
					:key="prediction.id"
					class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
				>
					<div class="flex-1">
						<h4 class="font-medium text-gray-900 dark:text-white">{{ prediction.title }}</h4>
						<p class="text-sm text-gray-600 dark:text-gray-400">
							{{ prediction.economistName }} • {{ prediction.category }} •
							Target: {{ formatDate(prediction.targetDate) }}
						</p>
					</div>
					<div class="flex items-center space-x-2">
						<span
							:class="[
								'px-2 py-1 text-xs font-medium rounded-full',
								getDaysOverdue(prediction.targetDate) > 7
									? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
									: getDaysOverdue(prediction.targetDate) > 0
									? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
									: 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
							]">
							{{ getDaysOverdue(prediction.targetDate) > 0 ? `${getDaysOverdue(prediction.targetDate)}d overdue` : 'Ready' }}
						</span>
						<button
							:disabled="isRunningVerification"
							class="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 text-sm font-medium disabled:opacity-50"
							@click="verifySpecificPrediction(prediction.id)"
						>
							Verify
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Verification Queue Section -->
		<div class="mt-10">
			<VerificationQueue />
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import VerificationQueue from './VerificationQueue.vue'
import { apiEndpoints } from '@/lib/api-config'

// Reactive state
const isLoading = ref(false)
const error = ref<string | null>(null)
const isRunningVerification = ref(false)
const stats = ref<any>(null)
const readyPredictions = ref<any[]>([])
const lastVerificationResults = ref<any>(null)

// Data sources status (in real implementation, this would be fetched from the service)
const dataSources = ref([
	{ name: 'TCMB', available: true },
	{ name: 'TurkStat', available: true },
	{ name: 'BIST', available: true },
	{ name: 'Gold API', available: true },
	{ name: 'Crypto API', available: false },
	{ name: 'FRED', available: true }
])

// Fetch data sources status from API
const fetchDataSourcesStatus = async () => {
	try {
		const response = await fetch('/api/admin/data-sources-status')
		const data = await response.json() as any

		if (data.success && data.data) {
			dataSources.value = data.data
		}
	} catch (err) {
		console.error('Error fetching data sources status:', err)
		// Keep default values on error
	}
}

// Fetch verification status and ready predictions
const fetchVerificationStatus = async () => {
	isLoading.value = true
	error.value = null

	try {
		const response = await fetch(apiEndpoints.predictions.verifyAutomated)
		const data = await response.json() as any

		if (data.success) {
			stats.value = data.data?.verificationStats || data.data
			readyPredictions.value = data.data?.readyPredictions || []
		} else {
			error.value = data.message || 'Failed to fetch verification status'
		}
	} catch (err) {
		error.value = err instanceof Error ? err.message : 'Unknown error occurred'
		console.error('Error fetching verification status:', err)
	} finally {
		isLoading.value = false
	}
}

// Run automated verification for all ready predictions
const runAutomatedVerification = async () => {
	isRunningVerification.value = true
	error.value = null

	try {
		const response = await fetch(apiEndpoints.predictions.verifyAutomated, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ runAll: true })
		})

		const data = await response.json() as any

		if (data.success) {
			lastVerificationResults.value = data.data?.summary || data.data
			// Refresh the status after verification
			await fetchVerificationStatus()
		} else {
			error.value = data.message || 'Automated verification failed'
		}
	} catch (err) {
		error.value = err instanceof Error ? err.message : 'Unknown error occurred'
		console.error('Error running automated verification:', err)
	} finally {
		isRunningVerification.value = false
	}
}

// Verify a specific prediction
const verifySpecificPrediction = async (predictionId: number) => {
	try {
		const response = await fetch(apiEndpoints.predictions.verifyAutomated, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ predictionIds: [predictionId] })
		})

		const data = await response.json() as any

		if (data.success) {
			// Refresh the list
			await fetchVerificationStatus()
		} else {
			error.value = data.message || 'Verification failed'
		}
	} catch (err) {
		error.value = err instanceof Error ? err.message : 'Unknown error occurred'
		console.error('Error verifying prediction:', err)
	}
}

// Utility functions
const formatDate = (dateString: string) => {
	return new Date(dateString).toLocaleDateString('tr-TR')
}

const getDaysOverdue = (targetDate: string) => {
	const target = new Date(targetDate)
	const now = new Date()
	const diffTime = now.getTime() - target.getTime()
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
	return Math.max(0, diffDays)
}

// Initialize component
onMounted(() => {
	// Only fetch data if we're in the browser (not during SSR/build)
	if (typeof window !== 'undefined') {
		fetchVerificationStatus()
		fetchDataSourcesStatus()
	}
})
</script>
