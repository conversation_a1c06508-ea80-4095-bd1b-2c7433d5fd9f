import type { D1Database } from '@cloudflare/workers-types';
import { drizzle } from 'drizzle-orm/d1';
import * as schema from './schema';

// Re-export the schema for easy access
export * from './schema';

// Re-export database context utilities
export * from './context';

// Define the type for our database connection
export type DbConnection = ReturnType<typeof drizzle<typeof schema>>;

/**
 * Initializes the database connection.
 * @param d1Database - The D1 database instance from Cloudflare's runtime context.
 * @returns A Drizzle-wrapped database connection.
 */
export function initDb(d1Database: D1Database): DbConnection {
  return drizzle(d1Database, { schema });
}
