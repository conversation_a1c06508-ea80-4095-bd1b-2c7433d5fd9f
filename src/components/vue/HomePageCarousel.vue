<template>
  <div class="relative">
    <!-- Carousel Header -->
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white transition-colors">Haftalık Video Analiz <PERSON>ı</h2>
      <div class="flex space-x-2">
        <button
          class="p-2 rounded-full bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700 text-gray-700 dark:text-white transition-colors"
          :disabled="currentSlide === 0"
          :class="{ 'opacity-50 cursor-not-allowed': currentSlide === 0 }"
          @click="previousSlide"
        >
          <Icon icon="mdi:chevron-left" class="w-5 h-5" />
        </button>
        <button
          class="p-2 rounded-full bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700 text-gray-700 dark:text-white transition-colors"
          :disabled="currentSlide >= analyses.length - 1"
          :class="{ 'opacity-50 cursor-not-allowed': currentSlide >= analyses.length - 1 }"
          @click="nextSlide"
        >
          <Icon icon="mdi:chevron-right" class="w-5 h-5" />
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="bg-gray-100 dark:bg-gray-800 rounded-lg p-8 animate-pulse transition-colors">
      <div class="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
      <div class="h-32 bg-gray-300 dark:bg-gray-600 rounded mb-4"></div>
      <div class="grid grid-cols-2 gap-4">
        <div class="h-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
        <div class="h-20 bg-gray-300 dark:bg-gray-600 rounded"></div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-gray-100 dark:bg-gray-800 rounded-lg p-8 text-center transition-colors">
      <p class="text-red-600 dark:text-red-400 mb-4">Video analizleri yüklenirken hata oluştu.</p>
      <button
        class="px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-lg text-sm transition-colors"
        @click="fetchRecentVideos"
      >
        Tekrar Dene
      </button>
    </div>

    <!-- Empty State -->
    <div v-else-if="analyses.length === 0" class="bg-gray-100 dark:bg-gray-800 rounded-lg p-8 text-center transition-colors">
      <p class="text-gray-600 dark:text-gray-400">Henüz analiz edilmiş video bulunamadı.</p>
    </div>

    <!-- Carousel Container -->
    <div v-else class="overflow-hidden rounded-lg">
      <div
        class="flex transition-transform duration-300 ease-in-out"
        :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
      >
        <div
          v-for="analysis in analyses"
          :key="analysis.id"
          class="w-full flex-shrink-0"
        >
          <!-- Detailed Analysis Presentation Card -->
          <div class="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 rounded-lg overflow-hidden shadow-2xl border border-gray-300 dark:border-gray-700 transition-colors">
            <!-- Analysis Header -->
            <div class="bg-gradient-to-r from-amber-600 to-orange-600 p-6">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-3 mb-3">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <span class="text-white font-bold text-lg">{{ analysis.economist.initials }}</span>
                    </div>
                    <div>
                      <a :href="`/economists/${analysis.economist.id}`" class="block hover:opacity-80 transition-opacity">
                        <h3 class="text-xl font-bold text-white hover:text-amber-100 transition-colors">{{ analysis.economist.name }}</h3>
                        <p class="text-amber-100 text-sm">{{ analysis.economist.title }}</p>
                      </a>
                    </div>
                  </div>
                  <h2 class="text-2xl font-bold text-white mb-2">{{ analysis.videoTitle }}</h2>
                  <p class="text-amber-100 text-sm">{{ analysis.publishDate }} • {{ analysis.videoDuration }}</p>
                </div>
                <div class="text-right">
                  <div class="bg-white bg-opacity-20 rounded-lg p-3 mb-2">
                    <div class="text-2xl font-bold text-white">{{ analysis.overallScore }}%</div>
                    <div class="text-amber-100 text-xs">Genel Skor</div>
                  </div>
                  <div class="bg-white bg-opacity-10 rounded px-2 py-1">
                    <span class="text-amber-100 text-xs font-medium">{{ analysis.analysisDate }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Predictions Analysis Grid -->
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div
                  v-for="prediction in analysis.predictions"
                  :key="prediction.id"
                  class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 transition-colors"
                >
                  <div class="flex items-start justify-between mb-3">
                    <h4 class="font-semibold text-gray-900 dark:text-white text-sm truncate">{{ prediction.topic }}</h4>
                    <div
                      class="px-2 py-1 rounded text-xs font-medium"
                      :class="prediction.accuracy > 0 ? getAccuracyBadgeColor(prediction.accuracy) : 'bg-gray-500 text-white'"
                    >
                      {{ prediction.accuracy > 0 ? prediction.accuracy + '%' : 'Beklemede' }}
                    </div>
                  </div>
                  <div class="space-y-2">
                    <div class="text-sm">
                      <span class="text-gray-500 dark:text-gray-400">Tahmin:</span>
                      <span class="text-gray-700 dark:text-gray-200 ml-2">{{ prediction.prediction }}</span>
                    </div>
                    <div v-if="prediction.confidenceLevel !== undefined" class="text-sm">
                      <span class="text-gray-500 dark:text-gray-400">Güven:</span>
                      <span class="text-gray-700 dark:text-gray-200 ml-2">{{ Math.round((prediction.confidenceLevel || 0) * 100) }}%</span>
                    </div>
                    <div v-if="prediction.timeframe" class="text-xs text-gray-500 dark:text-gray-400">{{ prediction.timeframe }}</div>
                    <div v-if="prediction.targetDate" class="text-xs text-gray-400 dark:text-gray-500">Hedef Tarih: {{ formatDate(prediction.targetDate) }}</div>
                  </div>
                </div>
              </div>

              <!-- Key Insights -->
              <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4 transition-colors">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                  <Icon icon="mdi:lightbulb" class="w-4 h-4 text-amber-500 dark:text-amber-400 mr-2" />
                  Öne Çıkan Bulgular
                </h4>
                <ul class="space-y-2">
                  <li v-for="insight in analysis.keyInsights" :key="insight" class="text-sm text-gray-600 dark:text-gray-300 flex items-start">
                    <span class="text-amber-500 dark:text-amber-400 mr-2">•</span>
                    {{ insight }}
                  </li>
                </ul>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                  <span>{{ analysis.totalPredictions }} tahmin</span>
                  <span>{{ analysis.correctPredictions }} doğru</span>
                  <span>{{ analysis.viewCount }}</span>
                </div>
                <div class="flex space-x-3">
                  <a
                    :href="analysis.youtubeUrl"
                    target="_blank"
                    class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors flex items-center"
                  >
                    <Icon icon="mdi:play" class="w-4 h-4 mr-2" />
                    Video İzle
                  </a>
                  <button class="px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-lg text-sm transition-colors">
                    Detaylı Analiz
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Slide Indicators -->
    <div class="flex justify-center mt-6 space-x-2">
      <button
        v-for="(_, index) in analyses"
        :key="index"
        class="w-3 h-3 rounded-full transition-colors"
        :class="currentSlide === index ? 'bg-amber-400' : 'bg-gray-600'"
        @click="currentSlide = index"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Icon } from '@iconify/vue'
import { apiEndpoints } from '@/lib/api-config'
import { parseApiResponse, type Video } from '@/lib/api-types'

interface VideoAnalysis {
  id: number
  videoTitle: string
  economist: {
    id: number
    name: string
    title: string
    initials: string
  }
  publishDate: string
  videoDuration: string
  analysisDate: string
  overallScore: number
  predictions: Array<{
    id: number
    topic: string
    prediction: string
    actual: string
    accuracy: number
    timeframe: string
    confidenceLevel?: number
    targetDate?: string | number | Date
  }>
  keyInsights: string[]
  totalPredictions: number
  correctPredictions: number
  viewCount: string
  youtubeUrl: string
}

const currentSlide = ref(0)
const analyses = ref<VideoAnalysis[]>([])
const loading = ref(true)
const error = ref(false)

const getAccuracyBadgeColor = (accuracy: number) => {
  if (accuracy >= 80) return 'bg-green-600 text-white'
  if (accuracy >= 60) return 'bg-yellow-600 text-white'
  return 'bg-red-600 text-white'
}

const nextSlide = () => {
  if (currentSlide.value < analyses.value.length - 1) {
    currentSlide.value++
  }
}

const previousSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--
  }
}

const formatDate = (dateInput: string | number | Date) => {
  let date: Date
  if (dateInput instanceof Date) {
    date = dateInput
  } else {
    date = new Date(dateInput)
  }
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) return '1 gün önce'
  if (diffDays < 7) return `${diffDays} gün önce`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} hafta önce`
  return `${Math.floor(diffDays / 30)} ay önce`
}

const getInitials = (name: string) => {
  return name.split(' ')
    .map(word => word.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase()
}

const translateCategory = (category: string) => {
  const translations: Record<string, string> = {
    'inflation': 'Enflasyon',
    'exchange_rate': 'Döviz Kuru',
    'interest_rate': 'Faiz Oranı',
    'gold': 'Altın',
    'stock_market': 'Borsa',
    'crypto': 'Kripto Para',
    'gdp': 'GSYİH',
    'employment': 'İstihdam',
    'oil_price': 'Petrol Fiyatı',
    'economy': 'Ekonomi'
  }
  return translations[category] || category
}

const formatTargetValue = (targetValue: string | null, predictionType: string) => {
  if (!targetValue) return 'Belirtilmemiş'

  try {
    const parsed = JSON.parse(targetValue)

    if (predictionType === 'range' && parsed.min && parsed.max) {
      return `${parsed.min}-${parsed.max} aralığında`
    }

    if (predictionType === 'decrease' && parsed.threshold) {
      return `%${parsed.threshold}'nin altına düşecek`
    }

    if (predictionType === 'increase' && parsed.threshold) {
      return `%${parsed.threshold}'nin üzerine çıkacak`
    }

    if (parsed.preferred) {
      return `Yaklaşık ${parsed.preferred}`
    }

    return targetValue
  } catch {
    return targetValue
  }
}

const transformVideoToAnalysis = (video: Video): VideoAnalysis => {
  const preds = Array.isArray(video.predictions) ? video.predictions : [];
  const correctPredictions = preds.filter((p: any) => (typeof p.accuracyScore === 'number' ? p.accuracyScore : 0) >= 0.7).length;
  const overallScore = preds.length > 0
    ? Math.round(preds.reduce((sum: number, p: any) => sum + ((typeof p.accuracyScore === 'number' ? p.accuracyScore : 0) * 100), 0) / preds.length)
    : 0;

  return {
    id: video.id,
    videoTitle: video.videoTitle,
    economist: {
      id: video.economist?.id || 0,
      name: video.economist?.name || '',
      title: video.economist?.title || 'Ekonomi Uzmanı',
      initials: video.economist?.initials || getInitials(video.economist?.name || '')
    },
    publishDate: video.publishDate || formatDate(video.publishedAt),
    videoDuration: video.duration || '15:00',
    analysisDate: `Analiz: ${video.publishDate || formatDate(video.publishedAt)}`,
    overallScore,
    predictions: preds.map((p: any) => ({
      id: p.id,
      topic: p.title || translateCategory(p.category),
      prediction: p.description || formatTargetValue(p.targetValue, p.predictionType),
      actual: '', // You can add actual value if available
      accuracy: typeof p.accuracyScore === 'number' ? Math.round(p.accuracyScore * 100) : 0,
      timeframe: p.targetDate ? formatDate(p.targetDate) : '',
      confidenceLevel: p.confidenceLevel,
      targetDate: p.targetDate !== undefined && p.targetDate !== null ? String(p.targetDate) : undefined
    })),
    keyInsights: preds.length > 0 ? [
      'Tahmin sayısı: ' + preds.length,
      `Doğru tahmin oranı: ${correctPredictions}/${preds.length}`
    ] : ['Henüz analiz verisi yok'],
    totalPredictions: preds.length,
    correctPredictions,
    viewCount: video.viewCount || '0 görüntülenme',
    youtubeUrl: video.youtubeUrl
  }
}

const fetchRecentVideos = async () => {
  try {
    loading.value = true
    error.value = false

    const response = await fetch(apiEndpoints.videos.recent)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await parseApiResponse<Video[]>(response)

    if (data.status !== 'success' || !data.data) {
      throw new Error(data.message || 'Failed to fetch videos')
    }

    // Transform video data to analysis format
    analyses.value = data.data.map(transformVideoToAnalysis)
  } catch (err) {
    console.error('Error fetching recent videos:', err)
    error.value = true
    // Fallback to show some content even if API fails
    analyses.value = []
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchRecentVideos()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
