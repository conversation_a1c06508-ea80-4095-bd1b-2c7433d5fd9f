import { describe, it, expect } from 'vitest';

describe('Health API Migration', () => {
  it('should have moved health endpoint from Astro to Hono', async () => {
    // Test that the files are in the right places
    const fs = await import('fs');
    const path = await import('path');

    // Check that the old Astro health route was removed
    const oldHealthPath = path.resolve('./src/pages/api/health.ts');
    expect(fs.existsSync(oldHealthPath)).toBe(false);

    // Check that the new Hono health route exists
    const newHealthPath = path.resolve('./src/server/api/health.ts');
    expect(fs.existsSync(newHealthPath)).toBe(true);

    // Check that the data directory was created
    const dataIndexPath = path.resolve('./src/server/data/index.ts');
    expect(fs.existsSync(dataIndexPath)).toBe(true);
  });

  it('should have proper database connection utilities', async () => {
    // Import the data utilities
    const { getDbFromContext, checkDbHealth } = await import('../../src/server/data');

    expect(typeof getDbFromContext).toBe('function');
    expect(typeof checkDbHealth).toBe('function');
  });
});
