import { describe, it, expect } from 'vitest';

describe('Economists Page', () => {
  describe('Page Structure and Configuration', () => {
    it('should have economists page file with correct structure', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const economistsPagePath = path.resolve('./src/pages/economists.astro');
      expect(fs.existsSync(economistsPagePath)).toBe(true);

      const content = fs.readFileSync(economistsPagePath, 'utf-8');

      // Check for SSG configuration
      expect(content).toContain('export const prerender = true');

      // Check for content collection import
      expect(content).toContain("import { getEntry } from 'astro:content'");

      // Check for economists data loading
      expect(content).toContain("getEntry('economists', 'data')");
    });

    it('should have economists content collection data file', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const dataPath = path.resolve('./src/content/economists/data.json');
      expect(fs.existsSync(dataPath)).toBe(true);

      const content = fs.readFileSync(dataPath, 'utf-8');
      const data = JSON.parse(content);

      // Validate data structure
      expect(data).toHaveProperty('data');
      expect(data.data).toHaveProperty('economists');
      expect(Array.isArray(data.data.economists)).toBe(true);
      expect(data.data.economists.length).toBeGreaterThan(0);
    });

    it('should validate economist data structure in content collection', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const dataPath = path.resolve('./src/content/economists/data.json');
      const content = fs.readFileSync(dataPath, 'utf-8');
      const data = JSON.parse(content);

      const economists = data.data.economists;

      // Check that we have economists
      expect(economists.length).toBeGreaterThan(0);

      // Validate first economist has required fields
      const firstEconomist = economists[0];
      expect(firstEconomist).toHaveProperty('id');
      expect(firstEconomist).toHaveProperty('name');
      expect(firstEconomist).toHaveProperty('totalPredictions');
      expect(firstEconomist).toHaveProperty('accuracyScore');

      // Validate data types
      expect(typeof firstEconomist.id).toBe('number');
      expect(typeof firstEconomist.name).toBe('string');
      expect(typeof firstEconomist.totalPredictions).toBe('number');
      expect(typeof firstEconomist.accuracyScore).toBe('number');
    });
  });

  describe('Page Template Structure', () => {
    it('should have proper page layout and components', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const economistsPagePath = path.resolve('./src/pages/economists.astro');
      const content = fs.readFileSync(economistsPagePath, 'utf-8');

      // Check for Layout component
      expect(content).toContain('import Layout from');
      expect(content).toContain('<Layout');

      // Check for proper title
      expect(content).toContain('Ekonomistler');

      // Check for economist listing structure
      expect(content).toContain('topEconomists');
    });

    it('should have proper error handling in template', async () => {
      const fs = await import('fs');
      const path = await import('path');

      const economistsPagePath = path.resolve('./src/pages/economists.astro');
      const content = fs.readFileSync(economistsPagePath, 'utf-8');

      // Check for error handling
      expect(content).toContain('No build-time data found');
      expect(content).toContain('throw new Error');
    });
  });

  describe('Build Integration', () => {
    it('should be included in build process', async () => {
      const fs = await import('fs');
      const path = await import('path');

      // Check that the page exists and will be built
      const economistsPagePath = path.resolve('./src/pages/economists.astro');
      expect(fs.existsSync(economistsPagePath)).toBe(true);

      // Check that content collection schema exists
      const schemaPath = path.resolve('./src/content/config.ts');
      if (fs.existsSync(schemaPath)) {
        const schemaContent = fs.readFileSync(schemaPath, 'utf-8');
        expect(schemaContent).toContain('economists');
      }
    });
  });
});
