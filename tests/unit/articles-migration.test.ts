import { describe, it, expect } from 'vitest';

describe('Articles API Migration', () => {
  it('should have moved articles endpoint from Astro to Hono', async () => {
    // Test that the files are in the right places
    const fs = await import('fs');
    const path = await import('path');

    // Check that the old Astro articles route was removed
    const oldArticlesPath = path.resolve('./src/pages/api/articles/curated.ts');
    expect(fs.existsSync(oldArticlesPath)).toBe(false);

    // Check that the articles directory was removed
    const articlesDir = path.resolve('./src/pages/api/articles');
    expect(fs.existsSync(articlesDir)).toBe(false);

    // Check that the new Hono articles route exists
    const newArticlesPath = path.resolve('./src/server/api/articles.ts');
    expect(fs.existsSync(newArticlesPath)).toBe(true);
  });

  it('should have proper articles module export', async () => {
    // Import the articles module
    const articlesModule = await import('../../src/server/api/articles');

    expect(articlesModule.default).toBeDefined();
    expect(typeof articlesModule.default).toBe('object');
  });
});
