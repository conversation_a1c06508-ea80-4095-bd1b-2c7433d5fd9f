# SSG/SSR Database Access Solution for Astro on Cloudflare

## Problem Summary

When deploying Astro to Cloudflare with `output: 'static'` mode, Static Site Generation (SSG) pages cannot access the database during the build/prerender phase because:

1. **Build-time vs Runtime**: SSG pages are prerendered at build time, before deployment
2. **No Database Access**: Cloudflare D1 database is only available at runtime in the Cloudflare environment
3. **Empty Static Pages**: This results in static pages showing no database data after deployment

## Current Solution: Build-Time Data Fetching (Implemented)

We've implemented **Option 2: Build-Time Data Export** with significant improvements for a foolproof, environment-specific system.

### How It Works

#### 1. **Pre-Build Data Fetching**
Before each build, fresh data is automatically fetched from the correct D1 database using `wrangler d1` commands:

```bash
# Development build
npm run build:dev
# → Fetches from economist-tracker-dev D1 database → Builds static site

# Staging build
npm run build:staging
# → Fetches from economist-tracker-staging D1 database → Builds static site

# Production build
npm run build:prod
# → Fetches from economist-tracker-prod D1 database → Builds static site
```

#### 2. **Data Storage in Content Collections**
Fetched data is saved as JSON files in Astro content collections:

```
src/content/
├── homepage/data.json          # Homepage data with economists & stats
├── predictions/data.json       # Predictions data (placeholder)
└── economists/data.json        # Economists data (placeholder)
```

#### 3. **SSG Pages with Fresh Data**
Pages use the content collections to access fresh database data at build time:

```astro
---
// src/pages/index.astro
export const prerender = true;  // ✅ Fully static

import { getEntry } from 'astro:content';

// Get build-time data from content collections
const cachedData = await getEntry('homepage', 'data');
const homepageData = cachedData.data.data;
---

<!-- Page renders with fresh database content baked in -->
```

### Benefits of Current Implementation

- ✅ **Fully Static**: All pages are pre-rendered for maximum performance
- ✅ **Always Fresh Data**: Each build fetches latest data from database
- ✅ **Environment Isolation**: Each environment uses its own database
- ✅ **Zero Risk**: No possibility of wrong environment data
- ✅ **SEO Perfect**: All content is in initial HTML
- ✅ **Fast Loading**: No runtime database queries needed
- ✅ **Simple Deployment**: One command does everything

### Environment-Specific Builds

Each environment automatically uses the correct database:

### Environment-Specific Builds

Each environment automatically uses the correct database:

| Environment | Command | Database Used | Deployment Target |
|-------------|---------|---------------|-------------------|
| **Local** | `npm run build:local` | `economist-tracker-local` | Local testing |
| **Development** | `npm run build:dev` | `economist-tracker-dev` | Dev Cloudflare Workers |
| **Staging** | `npm run build:staging` | `economist-tracker-staging` | Staging Cloudflare Workers |
| **Production** | `npm run build:prod` | `economist-tracker-prod` | Production Cloudflare Workers |

## Implementation Details

### 1. Data Fetching Script (`scripts/fetch-build-data.ts`)

This script uses `wrangler d1` commands to query the correct database:

```typescript
// Simplified example
function executeD1Query(query: string, dbName: string, isLocal = false): unknown {
  const localFlag = isLocal ? '--local' : '--remote';
  const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

  const output = execSync(command, { encoding: 'utf8' });
  const parsed = JSON.parse(output);
  return parsed[0].results; // Extract results from wrangler output
}

// Fetch economists data
const economists = executeD1Query(`
  SELECT id, name, youtube_channel_name as youtubeChannelName,
         total_predictions, correct_predictions, accuracy_score, trust_score
  FROM economists ORDER BY accuracy_score DESC LIMIT 10
`, dbName, isLocal);
```

### 2. Content Collections Schema (`src/content.config.mjs`)

```javascript
import { defineCollection, z } from 'astro:content';

const homepageCollection = defineCollection({
  type: 'data',
  schema: z.object({
    $schema: z.string(),
    data: z.object({
      topEconomists: z.array(z.object({
        id: z.number(),
        name: z.string(),
        youtubeChannelName: z.string(),
        totalPredictions: z.number(),
        correctPredictions: z.number(),
        accuracyScore: z.number(),
        trustScore: z.number(),
        // ... other fields
      })),
      platformStats: z.object({
        economistsCount: z.number(),
        predictionsCount: z.number(),
        videosCount: z.number(),
        verifiedCount: z.number(),
        accuracyPercentage: z.number(),
      }),
      recentVideos: z.array(z.unknown()).optional(),
      isStatic: z.boolean(),
    }),
    fetchedAt: z.string(),
    environment: z.string(),
  }),
});

export const collections = { homepage: homepageCollection };
```

### 3. SSG Page Implementation (`src/pages/index.astro`)

```astro
---
export const prerender = true;  // ✅ Fully static

import Layout from '@/layouts/Layout.astro';
import { getEntry } from 'astro:content';

// Get build-time data from content collections
const cachedData = await getEntry('homepage', 'data');
const { topEconomists, platformStats } = cachedData.data.data;

console.log(`[Homepage] Loaded data from environment: ${cachedData.data.environment}`);
console.log(`[Homepage] Data fetched at: ${cachedData.data.fetchedAt}`);
---

<Layout title="Ana Sayfa">
  <!-- All content is statically rendered with fresh database data -->
  {topEconomists.map((economist) => (
    <div class="economist-card">
      <h3>{economist.name}</h3>
      <p>Accuracy: {Math.round(economist.accuracyScore * 100)}%</p>
    </div>
  ))}
</Layout>
```

## Current Architecture

### All Pages are Static (SSG):
- ✅ `src/pages/index.astro` - Homepage with fresh economist data (prerender: true)
- ✅ `src/pages/economists.astro` - Economists page (uses service fallback for now)
- ✅ `src/pages/about.astro` - About page
- ✅ `src/pages/terms.astro` - Terms page

## Configuration

### Astro Config
```javascript
// astro.config.mjs
export default defineConfig({
  output: 'static',  // All pages are static by default
  adapter: cloudflare({
    imageService: 'cloudflare',
    platformProxy: {
      enabled: true,
      configPath: 'wrangler.toml',
      persist: true
    }
  })
});
```

### Build Process
```json
// package.json scripts
{
  "build:dev": "tsx scripts/fetch-build-data.ts --env=development && NODE_ENV=production astro build",
  "build:staging": "tsx scripts/fetch-build-data.ts --env=staging && NODE_ENV=production astro build",
  "build:prod": "tsx scripts/fetch-build-data.ts --env=production && NODE_ENV=production astro build",
  "deploy:dev": "tsx scripts/deploy.ts development",
  "deploy:staging": "tsx scripts/deploy.ts staging",
  "deploy": "tsx scripts/deploy.ts production"
}
```

## Benefits of This Approach

### ✅ **Performance**
- **Ultra-fast loading**: All content is pre-rendered HTML
- **Global CDN**: Static files served from Cloudflare edge locations
- **No database latency**: Zero runtime database queries for pages

### ✅ **Reliability**
- **Zero downtime**: Static files are always available
- **Environment isolation**: Each deployment uses correct database
- **Bulletproof builds**: Fresh data guaranteed on every deployment

### ✅ **SEO Excellence**
- **Perfect indexing**: All content in initial HTML response
- **Fast crawling**: No JavaScript required for content
- **Meta tags**: All SEO data available immediately

### ✅ **Developer Experience**
- **Simple deployment**: One command does everything
- **No configuration**: Environment automatically detected
- **Clear debugging**: Build logs show which database was used

## When to Use SSR vs SSG

### Use SSG (Current Implementation) When:
- ✅ Content changes infrequently (daily/weekly updates)
- ✅ SEO is critical
- ✅ Performance is top priority
- ✅ You have predictable build/deployment schedules

### Consider SSR When:
- ⚠️ Content changes very frequently (every few minutes)
- ⚠️ Real-time data is required
- ⚠️ User-specific content needed
- ⚠️ Interactive features require immediate database access

## Troubleshooting

### Common Issues:

#### **"No data found" in built site**
- **Cause**: Data fetching script failed or database was empty
- **Solution**: Check build logs for fetch errors, verify database has data

#### **Wrong environment data showing**
- **Cause**: Used wrong build command
- **Solution**: Use correct build command for target environment

#### **Build fails during data fetch**
- **Cause**: Database connection issues or wrangler auth problems
- **Solution**: Check `npx wrangler whoami` and database existence

### Debug Steps:
1. **Check build logs**: Look for environment name and fetch success messages
2. **Verify data files**: Check `src/content/homepage/data.json` after build
3. **Test data fetching**: Run `npm run fetch:data` separately
4. **Check environment**: Verify correct database exists for environment

## Migration Notes

### From SSR to SSG (Completed)
- ✅ Removed `export const prerender = false` from homepage
- ✅ Added content collections for build-time data
- ✅ Implemented `scripts/fetch-build-data.ts`
- ✅ Updated build process to fetch data before building
- ✅ Added environment-specific deployment commands

### Future Considerations
- **More data types**: Extend fetching script for predictions, videos, etc.
- **Incremental builds**: Consider caching unchanged data
- **Real-time features**: Use SSR for user dashboards, admin pages
- **Hybrid approach**: SSG for public pages, SSR for authenticated areas
