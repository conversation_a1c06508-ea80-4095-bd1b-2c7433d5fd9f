import { describe, it, expect } from 'vitest';

// Simple unit test for market data API response format
describe('Market Data API Response Format', () => {
  it('should have the expected response structure', () => {
    // Test the expected response format
    const mockResponse = {
      status: 'success',
      data: [
        {
          symbol: 'USD/TRY',
          value: '34.8500',
          change: 1.01,
          lastUpdated: '04-07-2025'
        },
        {
          symbol: 'EUR/TRY',
          value: '36.9200',
          change: 1.15,
          lastUpdated: '04-07-2025'
        }
      ],
      timestamp: '2025-07-04T10:30:00.000Z'
    };

    expect(mockResponse).toHaveProperty('status');
    expect(mockResponse).toHaveProperty('data');
    expect(mockResponse).toHaveProperty('timestamp');
    expect(mockResponse.data).toBeInstanceOf(Array);

    // Check data item structure
    const item = mockResponse.data[0];
    expect(item).toHaveProperty('symbol');
    expect(item).toHaveProperty('value');
    expect(item).toHaveProperty('change');
    expect(item).toHaveProperty('lastUpdated');
  });

  it('should handle error responses correctly', () => {
    const errorResponse = {
      status: 'error',
      message: 'TCMB API key not configured',
      data: []
    };

    expect(errorResponse.status).toBe('error');
    expect(errorResponse.message).toContain('API key');
    expect(errorResponse.data).toEqual([]);
  });
});
