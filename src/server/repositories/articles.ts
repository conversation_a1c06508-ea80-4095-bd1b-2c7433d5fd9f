/**
 * Articles Repository
 *
 * Data access layer for articles. Contains both Drizzle ORM queries and raw SQL.
 * This is the single source of truth for all article database operations.
 */

import type { DbConnection } from '@/server/db';
import { articles } from '@/server/db';
import { eq, desc, and } from 'drizzle-orm';
import { execSync } from 'child_process';

// ============================================================================
// RAW SQL QUERIES (for build scripts and external tools)
// ============================================================================

export const ARTICLES_SQL = {
	recent: (limit = 20) =>
		`
    SELECT
      id,
      title,
      excerpt,
      content,
      author,
      published_at as publishedAt,
      image_url as imageUrl,
      category,
      read_time as readTime,
      slug,
      meta_description as metaDescription,
      tags,
      is_active as isActive,
      is_curated as isCurated,
      created_at as createdAt,
      updated_at as updatedAt
    FROM articles
    WHERE is_active = 1
    ORDER BY published_at DESC
    LIMIT ${limit}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	curated: (limit = 10) =>
		`
    SELECT
      id,
      title,
      excerpt,
      content,
      author,
      published_at as publishedAt,
      image_url as imageUrl,
      category,
      read_time as readTime,
      slug,
      meta_description as metaDescription,
      tags,
      is_active as isActive,
      is_curated as isCurated,
      created_at as createdAt,
      updated_at as updatedAt
    FROM articles
    WHERE is_active = 1 AND is_curated = 1
    ORDER BY published_at DESC
    LIMIT ${limit}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	bySlug: (slug: string) =>
		`
    SELECT
      id,
      title,
      excerpt,
      content,
      author,
      published_at as publishedAt,
      image_url as imageUrl,
      category,
      read_time as readTime,
      slug,
      meta_description as metaDescription,
      tags,
      is_active as isActive,
      is_curated as isCurated,
      created_at as createdAt,
      updated_at as updatedAt
    FROM articles
    WHERE slug = '${slug}' AND is_active = 1
    LIMIT 1
  `
			.replace(/\s+/g, ' ')
			.trim(),
} as const;

// ============================================================================
// DRIZZLE ORM QUERIES
// ============================================================================

/**
 * Get active articles with optional filtering
 */
export async function findActiveArticles(
	db: DbConnection,
	options: {
		limit?: number;
		curated?: boolean;
		orderBy?: 'publishedAt' | 'createdAt' | 'updatedAt';
	} = {}
) {
	const { limit, curated = false, orderBy = 'publishedAt' } = options;

	const conditions = [eq(articles.isActive, true)];
	if (curated) {
		conditions.push(eq(articles.isCurated, true));
	}

	// Determine ordering column
	let orderColumn;
	switch (orderBy) {
		case 'publishedAt':
			orderColumn = desc(articles.publishedAt);
			break;
		case 'createdAt':
			orderColumn = desc(articles.createdAt);
			break;
		case 'updatedAt':
			orderColumn = desc(articles.updatedAt);
			break;
		default:
			orderColumn = desc(articles.publishedAt);
	}

	// Build the complete query
	const baseQuery = db
		.select()
		.from(articles)
		.where(and(...conditions))
		.orderBy(orderColumn);

	// Apply limit if specified
	const finalQuery = limit ? baseQuery.limit(limit) : baseQuery;

	return await finalQuery;
}

/**
 * Find article by slug
 */
export async function findArticleBySlug(db: DbConnection, slug: string) {
	const result = await db
		.select()
		.from(articles)
		.where(and(eq(articles.slug, slug), eq(articles.isActive, true)))
		.limit(1);

	return result[0] || null;
}

/**
 * Find article by ID
 */
export async function findArticleById(db: DbConnection, id: number) {
	const result = await db
		.select()
		.from(articles)
		.where(and(eq(articles.id, id), eq(articles.isActive, true)))
		.limit(1);

	return result[0] || null;
}

// ============================================================================
// RAW SQL EXECUTION (for build scripts)
// ============================================================================

/**
 * Execute raw SQL query for articles (used by build scripts)
 */
export function executeArticleSQL(query: string, dbName: string, isLocal = false): unknown {
	const localFlag = isLocal ? '--local' : '--remote';
	const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

	try {
		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Article SQL execution failed:', error);
		return [];
	}
}

/**
 * Get recent articles using raw SQL (for build-time)
 */
export function getRecentArticlesSQL(dbName: string, isLocal = false, limit = 20) {
	return executeArticleSQL(ARTICLES_SQL.recent(limit), dbName, isLocal);
}

/**
 * Get curated articles using raw SQL (for build-time)
 */
export function getCuratedArticlesSQL(dbName: string, isLocal = false, limit = 10) {
	return executeArticleSQL(ARTICLES_SQL.curated(limit), dbName, isLocal);
}

/**
 * Get article by slug using raw SQL (for build-time)
 */
export function getArticleBySlugSQL(dbName: string, slug: string, isLocal = false) {
	return executeArticleSQL(ARTICLES_SQL.bySlug(slug), dbName, isLocal);
}

// ============================================================================
// FORMATTED BUILD DATA FUNCTIONS
// ============================================================================

/**
 * Get articles data for build scripts (formatted)
 */
export function getArticlesBuildData(dbName: string, isLocal = false, limit = 20) {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const rawArticles = executeArticleSQL(ARTICLES_SQL.recent(limit), dbName, isLocal) as any[];

	if (!Array.isArray(rawArticles)) {
		return { articles: [], totalCount: 0 };
	}

	const formattedArticles = rawArticles.map((article) => ({
		...article,
		publishedAt: new Date(article.publishedAt * 1000).toISOString(),
		createdAt: new Date(article.createdAt * 1000).toISOString(),
		updatedAt: new Date(article.updatedAt * 1000).toISOString(),
		tags: article.tags ? JSON.parse(article.tags) : [],
		isActive: Boolean(article.isActive),
		isCurated: Boolean(article.isCurated),
	}));

	return {
		articles: formattedArticles,
		totalCount: formattedArticles.length,
	};
}

/**
 * Get curated articles data for build scripts (formatted)
 */
export function getCuratedArticlesBuildData(dbName: string, isLocal = false, limit = 10) {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const rawArticles = executeArticleSQL(ARTICLES_SQL.curated(limit), dbName, isLocal) as any[];

	if (!Array.isArray(rawArticles)) {
		return [];
	}

	return rawArticles.map((article) => ({
		...article,
		publishedAt: new Date(article.publishedAt * 1000).toISOString(),
		createdAt: new Date(article.createdAt * 1000).toISOString(),
		updatedAt: new Date(article.updatedAt * 1000).toISOString(),
		tags: article.tags ? JSON.parse(article.tags) : [],
		isActive: Boolean(article.isActive),
		isCurated: Boolean(article.isCurated),
	}));
}
