import { Hono } from 'hono';
import { users } from '@/server/db/tables/users';
import { eq } from 'drizzle-orm';
import { generateToken } from './index';
import { getDbFromContext } from '../data';
import { requireAuth } from './middleware';
import type { AuthContext } from './index';
import bcrypt from 'bcryptjs';

// Utility functions for consistent API responses
const successResponse = (data: unknown, message: string, status = 200) => ({
	status: 'success' as const,
	data,
	message,
	statusCode: status
});

const errorResponse = (message: string, errorCode: string, status = 400) => ({
	status: 'error' as const,
	message,
	errorCode,
	statusCode: status
});

const auth = new Hono<{ Variables: { auth: AuthContext } }>();

// POST /api/v1/auth/login
auth.post('/login', async (c) => {
	try {
		const { email, password } = await c.req.json();

		if (!email || !password) {
			return c.json(errorResponse(
				'Email and password are required',
				'MISSING_CREDENTIALS'
			), 400);
		}

		// Get database connection
		const db = getDbFromContext(c);
		if (!db) {
			return c.json(errorResponse(
				'Database connection not available',
				'DATABASE_UNAVAILABLE'
			), 500);
		}

		// Find user by email
		const user = await db.select().from(users).where(eq(users.email, email)).limit(1);

		if (!user.length) {
			return c.json(errorResponse(
				'Invalid credentials',
				'INVALID_CREDENTIALS'
			), 401);
		}

		const userData = user[0];

		// Check if user is active
		if (!userData.isActive) {
			return c.json(errorResponse(
				'Your account has been disabled',
				'ACCOUNT_DISABLED'
			), 401);
		}

		// Verify password
		const isValidPassword = userData.passwordHash
			? await bcrypt.compare(password, userData.passwordHash)
			: false;

		if (!isValidPassword) {
			return c.json(errorResponse(
				'Invalid credentials',
				'INVALID_CREDENTIALS'
			), 401);
		}

		// Update last login
		await db.update(users)
			.set({
				lastLoginAt: new Date(),
				updatedAt: new Date()
			})
			.where(eq(users.id, userData.id));

		// Generate JWT token
		const token = await generateToken(userData);

		// Set httpOnly cookie for secure authentication
		const isProduction = process.env.NODE_ENV === 'production';
		const cookieFlags = [
			'HttpOnly',
			'SameSite=Strict',
			`Max-Age=${7 * 24 * 60 * 60}`,
			'Path=/',
			isProduction ? 'Secure' : ''
		].filter(Boolean).join('; ');

		// Set cookie header
		c.header('Set-Cookie', `auth-token=${token}; ${cookieFlags}`);

		return c.json(successResponse({
			user: {
				id: userData.id,
				email: userData.email,
				username: userData.username,
				role: userData.role,
				emailVerified: userData.emailVerified
			}
		}, 'Login successful'), 200);

	} catch (error) {
		console.error('Login error:', error);
		return c.json(errorResponse(
			'An error occurred during login',
			'LOGIN_ERROR'
		), 500);
	}
});

// POST /api/v1/auth/register
auth.post('/register', async (c) => {
	try {
		const { email, username, password } = await c.req.json();

		if (!email || !password) {
			return c.json(errorResponse(
				'Email and password are required',
				'MISSING_CREDENTIALS'
			), 400);
		}

		// Get database connection
		const db = getDbFromContext(c);
		if (!db) {
			return c.json(errorResponse(
				'Database connection not available',
				'DATABASE_UNAVAILABLE'
			), 500);
		}

		// Validate email format
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			return c.json(errorResponse(
				'Please provide a valid email address',
				'INVALID_EMAIL'
			), 400);
		}

		// Validate password strength
		if (password.length < 8) {
			return c.json(errorResponse(
				'Password must be at least 8 characters long',
				'WEAK_PASSWORD'
			), 400);
		}

		// Check if user already exists
		const existingUser = await db.select().from(users).where(eq(users.email, email)).limit(1);

		if (existingUser.length > 0) {
			return c.json(errorResponse(
				'A user with this email already exists',
				'USER_EXISTS'
			), 409);
		}

		// Check username uniqueness if provided
		if (username) {
			const existingUsername = await db.select().from(users).where(eq(users.username, username)).limit(1);
			if (existingUsername.length > 0) {
				return c.json(errorResponse(
					'This username is already taken',
					'USERNAME_TAKEN'
				), 409);
			}
		}

		// Hash password
		const saltRounds = 12;
		const passwordHash = await bcrypt.hash(password, saltRounds);

		// Create user
		const newUser = await db.insert(users).values({
			email,
			username: username || null,
			passwordHash,
			role: 'user', // Default role
			isActive: true,
			emailVerified: false,
			createdAt: new Date(),
			updatedAt: new Date()
		}).returning();

		if (!newUser.length) {
			return c.json(errorResponse(
				'Failed to create user account',
				'REGISTRATION_FAILED'
			), 500);
		}

		const userData = newUser[0];

		// Generate JWT token
		const token = await generateToken(userData);

		// Set httpOnly cookie for secure authentication
		const isProduction = process.env.NODE_ENV === 'production';
		const cookieFlags = [
			'HttpOnly',
			'SameSite=Strict',
			`Max-Age=${7 * 24 * 60 * 60}`,
			'Path=/',
			isProduction ? 'Secure' : ''
		].filter(Boolean).join('; ');

		// Set cookie header
		c.header('Set-Cookie', `auth-token=${token}; ${cookieFlags}`);

		return c.json(successResponse({
			user: {
				id: userData.id,
				email: userData.email,
				username: userData.username,
				role: userData.role,
				emailVerified: userData.emailVerified
			}
		}, 'Registration successful'), 201);

	} catch (error) {
		console.error('Registration error:', error);
		return c.json(errorResponse(
			'An error occurred during registration',
			'REGISTRATION_ERROR'
		), 500);
	}
});

// POST /api/v1/auth/logout
auth.post('/logout', async (c) => {
	try {
		// Clear the HTTPOnly cookie
		c.header('Set-Cookie', 'auth-token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/');

		return c.json(successResponse({}, 'Logout successful'), 200);

	} catch (error) {
		console.error('Logout error:', error);
		return c.json(errorResponse(
			'An error occurred during logout',
			'LOGOUT_ERROR'
		), 500);
	}
});

// GET /api/v1/auth/me
auth.get('/me', requireAuth(), async (c) => {
	try {
		// Get auth context from middleware
		const authContext = c.get('auth');

		return c.json({
			status: 'success',
			data: {
				user: {
					id: authContext.user?.id,
					email: authContext.user?.email,
					username: authContext.user?.username,
					role: authContext.user?.role,
					emailVerified: authContext.user?.emailVerified,
					createdAt: authContext.user?.createdAt
				},
				roles: authContext.roles,
				isAuthenticated: authContext.isAuthenticated
			},
			message: 'User profile retrieved successfully'
		}, 200);

	} catch (error) {
		console.error('Profile fetch error:', error);
		return c.json({
			status: 'error',
			errorCode: 'PROFILE_FETCH_FAILED',
			message: 'An error occurred while fetching user profile'
		}, 500);
	}
});

// PUT /api/v1/auth/update-profile
auth.put('/update-profile', requireAuth(), async (c) => {
	try {
		const authContext = c.get('auth');
		const user = authContext.user;

		if (!user) {
			return c.json(errorResponse(
				'Your session is invalid or expired',
				'INVALID_TOKEN'
			), 401);
		}

		// Get database connection
		const db = getDbFromContext(c);
		if (!db) {
			return c.json(errorResponse(
				'Database connection not available',
				'DATABASE_UNAVAILABLE'
			), 500);
		}

		const requestData = await c.req.json();
		const { username, currentPassword, newPassword } = requestData;

		// Validate inputs
		if (username && typeof username !== 'string') {
			return c.json(errorResponse(
				'Username must be a string',
				'INVALID_USERNAME'
			), 400);
		}

		if (newPassword) {
			if (!currentPassword) {
				return c.json(errorResponse(
					'Current password is required to set a new password',
					'CURRENT_PASSWORD_REQUIRED'
				), 400);
			}

			if (newPassword.length < 8) {
				return c.json(errorResponse(
					'New password must be at least 8 characters long',
					'WEAK_PASSWORD'
				), 400);
			}

			// Verify current password
			const isValidPassword = user.passwordHash
				? await bcrypt.compare(currentPassword, user.passwordHash)
				: false;

			if (!isValidPassword) {
				return c.json(errorResponse(
					'Current password is incorrect',
					'INVALID_PASSWORD'
				), 400);
			}
		}

		// Check username uniqueness if provided and different
		if (username && username !== user.username) {
			const existingUsername = await db.select().from(users).where(eq(users.username, username)).limit(1);
			if (existingUsername.length > 0) {
				return c.json(errorResponse(
					'This username is already taken',
					'USERNAME_TAKEN'
				), 409);
			}
		}

		// Prepare update data
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		const updateData: Record<string, any> = {
			updatedAt: new Date()
		};

		if (username && username !== user.username) {
			updateData.username = username;
		}

		if (newPassword) {
			const saltRounds = 12;
			updateData.passwordHash = await bcrypt.hash(newPassword, saltRounds);
		}

		// Update user
		const updatedUsers = await db.update(users)
			.set(updateData)
			.where(eq(users.id, user.id))
			.returning();

		if (!updatedUsers.length) {
			return c.json(errorResponse(
				'Failed to update user profile',
				'UPDATE_FAILED'
			), 500);
		}

		const updatedUser = updatedUsers[0];

		return c.json(successResponse({
			user: {
				id: updatedUser.id,
				email: updatedUser.email,
				username: updatedUser.username,
				role: updatedUser.role,
				emailVerified: updatedUser.emailVerified,
				createdAt: updatedUser.createdAt,
				updatedAt: updatedUser.updatedAt
			}
		}, 'Profile updated successfully'), 200);

	} catch (error) {
		console.error('Profile update error:', error);
		return c.json(errorResponse(
			'An error occurred while updating your profile',
			'UPDATE_ERROR'
		), 500);
	}
});

export default auth;
