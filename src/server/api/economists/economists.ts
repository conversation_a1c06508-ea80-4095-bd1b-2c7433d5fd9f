import { Hono } from 'hono';
import { economists, predictions, videos } from '@/server/db/schema';
import { getDbFromContext } from '../../data';
import { requireAuth } from '../../auth/middleware';
import { getEconomistById, getTopEconomists } from '../../services';
import { eq, desc, and, count, avg } from 'drizzle-orm';

const economistsRouter = new Hono();

// GET /api/economists/top - Get top economists
economistsRouter.get('/top', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				status: 'error',
				message: 'Database connection not available'
			}, 503);
		}

		const limit = parseInt(c.req.query('limit') || '6');
		const economists = await getTopEconomists(db, limit);

		return c.json({
			status: 'success',
			data: economists,
			message: 'Top economists fetched successfully',
			count: economists.length,
		});
	} catch (error) {
		console.error('Error fetching top economists:', error);
		return c.json({
			status: 'error',
			message: 'Failed to fetch top economists',
		}, 500);
	}
});

// GET /api/economists - Get all economists
economistsRouter.get('/', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				success: false,
				error: 'Database connection not available'
			}, 503);
		}

		const allEconomists = await db.select().from(economists);

		return c.json({
			success: true,
			data: allEconomists,
			message: 'Economists fetched successfully',
		});
	} catch (error) {
		console.error('Error fetching economists:', error);

		return c.json({
			success: false,
			error: 'Failed to fetch economists',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

// POST /api/economists - Create new economist (requires auth)
economistsRouter.post('/', requireAuth(), async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				success: false,
				error: 'Database connection not available'
			}, 503);
		}

		const economistData = await c.req.json();

		const economist = {
			name: economistData.name || 'Dr. Test Ekonomi',
			bio: economistData.bio || 'Test economist for development',
			youtubeChannelId: economistData.youtubeChannelId || `test${Date.now()}`,
			youtubeChannelName: economistData.youtubeChannelName || 'Test Channel',
			youtubeChannelUrl: economistData.youtubeChannelUrl || 'https://youtube.com/test',
			avatarUrl: economistData.avatarUrl || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
			subscriberCount: economistData.subscriberCount || 1000,
			isVerified: economistData.isVerified ?? false,
			isActive: economistData.isActive ?? true,
		};

		const result = await db.insert(economists).values(economist).returning();

		return c.json({
			success: true,
			data: result[0],
			message: 'Economist created successfully',
		}, 201);
	} catch (error) {
		console.error('Error creating economist:', error);

		return c.json({
			success: false,
			error: 'Failed to create economist',
			message: error instanceof Error ? error.message : 'Unknown error',
		}, 500);
	}
});

// GET /api/economists/:id - Get individual economist with detailed data
economistsRouter.get('/:id', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				status: 'error',
				message: 'Database connection not available'
			}, 503);
		}

		const economistId = parseInt(c.req.param('id'));
		if (isNaN(economistId)) {
			return c.json({
				status: 'error',
				message: 'Invalid economist ID'
			}, 400);
		}

		// Get basic economist data
		const economist = await getEconomistById(db, economistId);
		if (!economist) {
			return c.json({
				status: 'error',
				message: 'Economist not found'
			}, 404);
		}

		// Get recent predictions for this economist
		const recentPredictions = await db
			.select()
			.from(predictions)
			.innerJoin(videos, eq(predictions.videoId, videos.id))
			.where(eq(predictions.economistId, economistId))
			.orderBy(desc(predictions.extractedAt))
			.limit(20);

		// Get recent videos for this economist
		const recentVideos = await db
			.select()
			.from(videos)
			.where(eq(videos.economistId, economistId))
			.orderBy(desc(videos.publishedAt))
			.limit(10);

		// Get prediction statistics by category
		const predictionsByCategory = await db
			.select({
				category: predictions.category,
				total: count(),
				correct: count(and(predictions.verifiedAt, eq(predictions.status, 'verified_correct'))),
				verified: count(predictions.verifiedAt)
			})
			.from(predictions)
			.where(eq(predictions.economistId, economistId))
			.groupBy(predictions.category);

		// Get monthly accuracy trends (last 12 months)
		const monthlyStats = await db
			.select({
				month: predictions.extractedAt,
				total: count(),
				correct: count(and(predictions.verifiedAt, eq(predictions.status, 'verified_correct'))),
				accuracy: avg(predictions.accuracyScore)
			})
			.from(predictions)
			.where(eq(predictions.economistId, economistId))
			.groupBy(predictions.extractedAt)
			.orderBy(desc(predictions.extractedAt))
			.limit(12);

		// Format the response data
		const formattedPredictions = recentPredictions.map(({ predictions: prediction, videos: video }) => ({
			id: prediction.id,
			title: prediction.title,
			description: prediction.description,
			category: prediction.category,
			predictionType: prediction.predictionType,
			targetValue: prediction.targetValue,
			targetDate: prediction.targetDate,
			confidenceLevel: prediction.confidenceLevel,
			status: prediction.status,
			accuracyScore: prediction.accuracyScore,
			verifiedAt: prediction.verifiedAt,
			extractedAt: prediction.extractedAt,
			video: {
				id: video.id,
				title: video.title,
				youtubeVideoId: video.youtubeVideoId,
				thumbnailUrl: video.thumbnailUrl,
				publishedAt: video.publishedAt,
				youtubeUrl: `https://www.youtube.com/watch?v=${video.youtubeVideoId}`
			}
		}));

		const formattedVideos = recentVideos.map(video => ({
			id: video.id,
			title: video.title,
			youtubeVideoId: video.youtubeVideoId,
			thumbnailUrl: video.thumbnailUrl,
			duration: video.duration,
			viewCount: video.viewCount,
			publishedAt: video.publishedAt,
			youtubeUrl: `https://www.youtube.com/watch?v=${video.youtubeVideoId}`,
			predictionsExtracted: video.predictionsExtracted,
			analysisCompleted: video.analysisCompleted
		}));

		return c.json({
			status: 'success',
			data: {
				economist,
				recentPredictions: formattedPredictions,
				recentVideos: formattedVideos,
				statistics: {
					predictionsByCategory,
					monthlyTrends: monthlyStats,
					totalVideos: recentVideos.length,
					totalPredictions: economist.totalPredictions,
					verifiedPredictions: economist.verifiedPredictions,
					correctPredictions: economist.correctPredictions,
					accuracyRate: economist.verifiedPredictions > 0
						? Math.round((economist.correctPredictions / economist.verifiedPredictions) * 100)
						: 0
				}
			},
			message: 'Economist details fetched successfully'
		});

	} catch (error) {
		console.error('Error fetching economist details:', error);
		return c.json({
			status: 'error',
			message: 'Failed to fetch economist details',
			errorCode: 'ECONOMIST_FETCH_ERROR'
		}, 500);
	}
});

export default economistsRouter;
