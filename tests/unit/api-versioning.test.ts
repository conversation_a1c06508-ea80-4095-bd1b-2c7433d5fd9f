/**
 * API Versioning Tests
 * Tests to ensure proper API versioning implementation and backward compatibility
 */

import { describe, it, expect } from 'vitest';
import { apiEndpoints, buildApiUrl } from '@/lib/api-config';

describe('API Configuration', () => {
	describe('API Endpoints', () => {
		it('should provide versioned endpoints', () => {
			expect(apiEndpoints.auth.login).toBe('/api/v1/auth/login');
			expect(apiEndpoints.auth.register).toBe('/api/v1/auth/register');
			expect(apiEndpoints.economists.list).toBe('/api/v1/economists');
			expect(apiEndpoints.predictions.list).toBe('/api/v1/predictions');
			expect(apiEndpoints.videos.list).toBe('/api/v1/videos');
		});

		it('should provide parameterized endpoints', () => {
			expect(apiEndpoints.economists.byId(123)).toBe('/api/v1/economists/123');
			expect(apiEndpoints.predictions.verify(456)).toBe('/api/v1/predictions/456/verify');
			expect(apiEndpoints.videos.byId(789)).toBe('/api/v1/videos/789');
		});
	});

	describe('buildApiUrl', () => {
		it('should return endpoint without params', () => {
			const url = buildApiUrl('/api/v1/test');
			expect(url).toBe('/api/v1/test');
		});

		it('should append query parameters', () => {
			const url = buildApiUrl('/api/v1/test', { page: 1, limit: 10, active: true });
			expect(url).toBe('/api/v1/test?page=1&limit=10&active=true');
		});

		it('should handle empty params object', () => {
			const url = buildApiUrl('/api/v1/test', {});
			expect(url).toBe('/api/v1/test');
		});

		it('should handle undefined params', () => {
			const url = buildApiUrl('/api/v1/test', undefined);
			expect(url).toBe('/api/v1/test');
		});
	});
});

describe('API Versioning Integration', () => {
	// These tests would require setting up a test server
	// For now, we'll test the structure and configuration

	it('should maintain consistent version prefix', () => {
		const allEndpoints = [
			...Object.values(apiEndpoints.auth).filter(ep => typeof ep === 'string'),
			...Object.values(apiEndpoints.economists).filter(ep => typeof ep === 'string'),
			...Object.values(apiEndpoints.predictions).filter(ep => typeof ep === 'string'),
			...Object.values(apiEndpoints.videos).filter(ep => typeof ep === 'string'),
			...Object.values(apiEndpoints.articles).filter(ep => typeof ep === 'string'),
			...Object.values(apiEndpoints.stats).filter(ep => typeof ep === 'string'),
			apiEndpoints.health,
		].filter(ep => typeof ep === 'string');

		allEndpoints.forEach(endpoint => {
			expect(endpoint).toMatch(/^\/api\/v1\//);
		});
	});

	it('should provide all required auth endpoints', () => {
		const requiredAuthEndpoints = ['login', 'register', 'logout', 'me', 'updateProfile'];
		requiredAuthEndpoints.forEach(endpoint => {
			expect(apiEndpoints.auth).toHaveProperty(endpoint);
		});
	});

	it('should provide all required resource endpoints', () => {
		expect(apiEndpoints.economists).toHaveProperty('list');
		expect(apiEndpoints.economists).toHaveProperty('top');
		expect(apiEndpoints.predictions).toHaveProperty('list');
		expect(apiEndpoints.predictions).toHaveProperty('verificationQueue');
		expect(apiEndpoints.videos).toHaveProperty('list');
		expect(apiEndpoints.videos).toHaveProperty('recent');
	});
});
