# 02_tech_stack_and_libraries.md

## 1. Languages & Runtimes

- **Frontend & Backend:** JavaScript / TypeScript.
- **Versions:** AI to assume latest stable versions for TypeScript, Node.js (for local development/build tools like Astro CLI), etc.
- **AI Version Assumption:** AI should always assume latest stable versions of specified technologies unless a specific version is explicitly pinned.

## 2. Frameworks

- **Frontend:**
  &#x20; \* Astro (primary framework).
  &#x20; \* Vue.js (for components within Astro Islands).
- **Backend:**
  &#x20; \* Cloudflare Workers (as the serverless execution environment/framework).
  &#x20; \* **Router:** Hono (for routing within Cloudflare Workers).

## 3. Databases & ORMs

- **Primary SQL Database:** Cloudflare D1.
- **ORM/Query Builder:** Drizzle ORM.
- **Vector Database:** Cloudflare Vectorize (for the chatbot Q\&A feature).
- **Object Storage:** Cloudflare R2 (for potential file storage needs).
- **Fallback:** Self-hosted database options (if Cloudflare costs are prohibitive).

## 4. Key Libraries

- **UI Components/Styling:**
  &#x20; \* **Base Styling:** Tailwind CSS (integrated via Astro).
  &#x20; \* **Component Library:** `shadcn-vue` (to be evaluated; if not suitable, custom Vue components styled with Tailwind CSS will be developed, potentially inspired by Shadcn/HeroUI aesthetics).
- **Frontend Framework Libraries:** Astro, Vue.js.
- **Chatbot/LLM Interaction:**
  &#x20; \* The system will interface with an LLM. This could be via **Cloudflare Workers AI** (using its built-in models or connecting to external ones) or directly calling an **external LLM API** (e.g., OpenAI, Anthropic Claude, etc.).
  &#x20; \* **Adapter Pattern:** An adapter will be implemented to allow the LLM provider to be changed with relative ease in the future.

## 5. Deployment Environment & DevOps

- **Frontend Hosting:** Cloudflare Workers.
- **Backend Hosting:** Cloudflare Workers (serverless).
- **Code Hosting & CI/CD:** Self-hosted Gitea server, utilizing **Gitea Actions** for CI/CD pipelines.
  &#x20; \* Standard CI/CD practices like linting (e.g., ESLint, Prettier for TypeScript/Vue/Astro), testing, and build steps will be defined within Gitea Actions workflows.
- **Fallback Cloud Platform (if serverful components are absolutely needed):** Microsoft Azure (Azure Functions, Azure Container Apps, Azure App Service).
