#!/usr/bin/env tsx

/**
 * Cloudflare Infrastructure Setup Script
 *
 * This script automates the setup of Cloudflare infrastructure for the
 * YouTube Economist Trust Score platform, including:
 * - D1 Databases (dev, staging, prod)
 * - KV Namespaces (sessions)
 * - Environment variable validation
 *
 * Usage:
 *   npm run cf:setup
 *   tsx scripts/cloudflare-setup.ts
 */

import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import { resolve } from 'path';

interface CloudflareResource {
	name: string;
	id?: string;
	created?: boolean;
}

interface SetupConfig {
	projectName: string;
	environments: {
		development: {
			d1Database: CloudflareResource;
			kvNamespace: CloudflareResource;
		};
		staging: {
			d1Database: CloudflareResource;
			kvNamespace: CloudflareResource;
		};
		production: {
			d1Database: CloudflareResource;
			kvNamespace: CloudflareResource;
		};
	};
}

const setupConfig: SetupConfig = {
	projectName: 'economist-accuracy-tracker-tr-web-app',
	environments: {
		development: {
			d1Database: { name: 'economist-tracker-dev' },
			kvNamespace: { name: 'economist-tracker-sessions-dev' }
		},
		staging: {
			d1Database: { name: 'economist-tracker-staging' },
			kvNamespace: { name: 'economist-tracker-sessions-staging' }
		},
		production: {
			d1Database: { name: 'economist-tracker-prod' },
			kvNamespace: { name: 'economist-tracker-sessions-prod' }
		}
	}
};

function log(message: string, level: 'info' | 'success' | 'error' | 'warning' = 'info'): void {
	const colors = {
		info: '\x1b[36m',    // Cyan
		success: '\x1b[32m', // Green
		error: '\x1b[31m',   // Red
		warning: '\x1b[33m', // Yellow
		reset: '\x1b[0m'
	};

	const prefix = {
		info: 'ℹ',
		success: '✅',
		error: '❌',
		warning: '⚠️'
	};

	console.log(`${colors[level]}${prefix[level]} ${message}${colors.reset}`);
}

function executeCommand(command: string, description: string): string {
	try {
		log(`Executing: ${description}...`);
		const result = execSync(command, { encoding: 'utf-8', stdio: 'pipe' });
		log(`✓ ${description} completed`, 'success');
		return result;
	} catch (error) {
		log(`✗ ${description} failed: ${String(error)}`, 'error');
		throw error;
	}
}

function checkWranglerAuth(): void {
	try {
		log('Checking Wrangler authentication...');
		executeCommand('wrangler whoami', 'Checking authentication');
		log('Wrangler is authenticated', 'success');
	} catch {
		log('Wrangler authentication failed. Please run: wrangler login', 'error');
		process.exit(1);
	}
}

function createD1Database(name: string): string {
	try {
		log(`Creating D1 database: ${name}...`);
		const result = executeCommand(
			`wrangler d1 create ${name}`,
			`Creating D1 database ${name}`
		);

		// Extract database ID from the output
		const idMatch = result.match(/database_id\s*=\s*"([^"]+)"/);
		if (!idMatch) {
			throw new Error('Could not extract database ID from wrangler output');
		}

		const databaseId = idMatch[1];
		log(`D1 database ${name} created with ID: ${databaseId}`, 'success');
		return databaseId;
	} catch (error) {
		if (error instanceof Error && error.message.includes('already exists')) {
			log(`D1 database ${name} already exists, retrieving ID...`, 'warning');
			// If database exists, try to get its ID from wrangler.toml or list command
			try {
				const listResult = executeCommand(
					'wrangler d1 list',
					'Listing existing D1 databases'
				);
				// Parse the list to find the database ID
				const lines = listResult.split('\n');
				for (const line of lines) {
					if (line.includes(name)) {
						const parts = line.split('|').map(p => p.trim());
						if (parts.length >= 2) {
							return parts[1]; // Database ID is typically in the second column
						}
					}
				}
			} catch {
				log('Could not retrieve existing database ID', 'warning');
			}
		}
		throw error;
	}
}

function createKVNamespace(name: string, isPreview = false): string {
	try {
		const command = isPreview
			? `wrangler kv:namespace create ${name} --preview`
			: `wrangler kv:namespace create ${name}`;

		log(`Creating KV namespace: ${name}${isPreview ? ' (preview)' : ''}...`);
		const result = executeCommand(command, `Creating KV namespace ${name}`);

		// Extract namespace ID from the output
		const idMatch = result.match(/id\s*=\s*"([^"]+)"/);
		if (!idMatch) {
			throw new Error('Could not extract namespace ID from wrangler output');
		}

		const namespaceId = idMatch[1];
		log(`KV namespace ${name} created with ID: ${namespaceId}`, 'success');
		return namespaceId;
	} catch (error) {
		if (error instanceof Error && error.message.includes('already exists')) {
			log(`KV namespace ${name} already exists`, 'warning');
			// For existing namespaces, we'd need to get the ID from the dashboard or config
			return 'existing-namespace-id-placeholder';
		}
		throw error;
	}
}

function updateWranglerToml(config: SetupConfig): void {
	try {
		log('Updating wrangler.toml with created resource IDs...');

		const wranglerPath = resolve(process.cwd(), 'wrangler.toml');
		let wranglerContent = readFileSync(wranglerPath, 'utf-8');

		// Update development environment
		const devEnv = config.environments.development;
		if (devEnv.d1Database.id) {
			wranglerContent = wranglerContent.replace(
				/database_id = "your-dev-d1-database-id"/g,
				`database_id = "${devEnv.d1Database.id}"`
			);
		}
		if (devEnv.kvNamespace.id) {
			wranglerContent = wranglerContent.replace(
				/id = "your-dev-kv-namespace-id"/g,
				`id = "${devEnv.kvNamespace.id}"`
			);
		}

		// Update staging environment
		const stagingEnv = config.environments.staging;
		if (stagingEnv.d1Database.id) {
			wranglerContent = wranglerContent.replace(
				/database_id = "your-staging-d1-database-id"/g,
				`database_id = "${stagingEnv.d1Database.id}"`
			);
		}
		if (stagingEnv.kvNamespace.id) {
			wranglerContent = wranglerContent.replace(
				/id = "your-staging-kv-namespace-id"/g,
				`id = "${stagingEnv.kvNamespace.id}"`
			);
		}

		// Update production environment
		const prodEnv = config.environments.production;
		if (prodEnv.d1Database.id) {
			wranglerContent = wranglerContent.replace(
				/database_id = "your-prod-d1-database-id"/g,
				`database_id = "${prodEnv.d1Database.id}"`
			);
		}
		if (prodEnv.kvNamespace.id) {
			wranglerContent = wranglerContent.replace(
				/id = "your-prod-kv-namespace-id"/g,
				`id = "${prodEnv.kvNamespace.id}"`
			);
		}

		writeFileSync(wranglerPath, wranglerContent);
		log('wrangler.toml updated successfully', 'success');
	} catch (error) {
		log(`Failed to update wrangler.toml: ${error}`, 'error');
		throw error;
	}
}

function generateEnvironmentFile(): void {
	try {
		log('Generating .env.production file...');

		const envContent = `# Production Environment Variables
# Generated by cloudflare-setup.ts on ${new Date().toISOString()}

# Database Configuration
NODE_ENV=production

# JWT Configuration (IMPORTANT: Set these in Cloudflare Dashboard -> Workers -> Settings -> Environment Variables)
# JWT_SECRET=your_super_secure_jwt_secret_here
# JWT_EXPIRES_IN=7d

# LLM Configuration (Set in Cloudflare Dashboard if needed)
# LLM_PROVIDER=openai
# OPENAI_API_KEY=your_openai_api_key_here

# Cloudflare Configuration (Set in Cloudflare Dashboard)
# CLOUDFLARE_ACCOUNT_ID=your_account_id_here
# CLOUDFLARE_API_TOKEN=your_api_token_here

# Logging (Set in Cloudflare Dashboard if using)
# LOGFLARE_API_KEY=your_logflare_api_key_here
# LOGFLARE_SOURCE_TOKEN=your_logflare_source_token_here

# Application Configuration
API_BASE_URL=https://economist-tracker.workers.dev
CORS_ORIGINS=https://economist-tracker.workers.dev,https://economist-tracker.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
`;

		const envPath = resolve(process.cwd(), '.env.production');
		writeFileSync(envPath, envContent);
		log('.env.production file created', 'success');
	} catch (error) {
		log(`Failed to create .env.production: ${error}`, 'error');
		throw error;
	}
}

async function setupInfrastructure(): Promise<void> {
	try {
		log('🚀 Starting Cloudflare infrastructure setup...', 'info');

		// Check authentication
		checkWranglerAuth();

		// Create D1 databases
		log('\n📊 Creating D1 databases...');
		setupConfig.environments.development.d1Database.id = createD1Database(
			setupConfig.environments.development.d1Database.name
		);
		setupConfig.environments.staging.d1Database.id = createD1Database(
			setupConfig.environments.staging.d1Database.name
		);
		setupConfig.environments.production.d1Database.id = createD1Database(
			setupConfig.environments.production.d1Database.name
		);

		// Create KV namespaces
		log('\n🗂️  Creating KV namespaces...');
		setupConfig.environments.development.kvNamespace.id = createKVNamespace(
			setupConfig.environments.development.kvNamespace.name
		);
		setupConfig.environments.staging.kvNamespace.id = createKVNamespace(
			setupConfig.environments.staging.kvNamespace.name
		);
		setupConfig.environments.production.kvNamespace.id = createKVNamespace(
			setupConfig.environments.production.kvNamespace.name
		);

		// Update wrangler.toml
		log('\n📝 Updating configuration files...');
		updateWranglerToml(setupConfig);

		// Generate environment files
		generateEnvironmentFile();

		log('\n🎉 Cloudflare infrastructure setup completed!', 'success');
		log('\n📋 Next steps:', 'info');
		log('1. Set sensitive environment variables in Cloudflare Dashboard:', 'info');
		log('   - JWT_SECRET', 'info');
		log('   - OPENAI_API_KEY (if using OpenAI)', 'info');
		log('   - LOGFLARE_* keys (if using Logflare)', 'info');
		log('2. Run database migrations: npm run db:migrate:dev', 'info');
		log('3. Deploy to staging: npm run deploy:staging', 'info');
		log('4. Deploy to production: npm run deploy', 'info');

	} catch (error) {
		log(`Infrastructure setup failed: ${error}`, 'error');
		process.exit(1);
	}
}

// Run the setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
	setupInfrastructure();
}

export { setupInfrastructure, setupConfig };
