---
// Enable prerendering for SSG (static generation) by default
export const prerender = true;

import Layout from '@/layouts/Layout.astro';
import { getEntry } from 'astro:content';

// Generate static paths for all economists at build time
export async function getStaticPaths() {
	try {
		// Get build-time data from content collections (fetched via fetch-build-data.ts)
		console.log('[Economist Detail] Loading build-time data from content collections...');
		const cachedData = await getEntry('economists', 'data');

		if (!cachedData?.data) {
			console.error('[Economist Detail] No build-time data found. Run the build process to generate data.');
			return [];
		}

		const economistsData = cachedData.data.data;
		const { economists } = economistsData;

		console.log('[Economist Detail] Generating static paths for economists:', economists.length);

		return economists.map((economist: any) => ({
			params: { id: economist.id.toString() },
			props: { economist }
		}));
	} catch (error) {
		console.error('[Economist Detail] Error generating static paths:', error);
		return [];
	}
}

// Get the economist ID from the URL parameters
const { id } = Astro.params;
const { economist } = Astro.props as { economist?: any };

// Validate economist ID and redirect to 404 if invalid
if (!id || isNaN(Number(id))) {
	// @ts-ignore - Astro allows return statements in top-level script
	return Astro.redirect('/404');
}

// Additional validation - if no economist data is provided, redirect to 404
if (!economist) {
	// @ts-ignore - Astro allows return statements in top-level script
	return Astro.redirect('/404');
}

const economistId = Number(id);

// Generate page title and meta description using economist data
const pageTitle = economist?.name
	? `${economist.name} - Ekonomist Profili | Güven Skoru: ${Math.round((economist.trustScore || economist.accuracyScore || 0) * 100)}/100`
	: `Ekonomist Profili #${economistId}`;

const pageDescription = economist?.bio
	? `${economist.name} - ${economist.bio.substring(0, 150)}${economist.bio.length > 150 ? '...' : ''}`
	: `${economist?.name || 'Ekonomist'} - YouTube ekonomist güven skoru ve tahmin analizi. ${economist?.totalPredictions || 0} tahmin, doğruluk oranı analizi.`;

// Fetch related data for this economist
let economistPredictions: any[] = [];
let economistVideos: any[] = [];

try {
	// Try to get predictions data from content collections
	const predictionsData = await getEntry('predictions', 'data');
	if (predictionsData?.data?.data?.predictions) {
		economistPredictions = predictionsData.data.data.predictions
			.filter((p: any) => p.economistId === economistId)
			.slice(0, 5); // Show latest 5 predictions
	}

	// Try to get videos data from content collections
	const videosData = await getEntry('videos', 'data');
	if (videosData?.data?.data?.videos) {
		economistVideos = videosData.data.data.videos
			.filter((v: any) => v.economistId === economistId)
			.slice(0, 5); // Show latest 5 videos
	}
} catch (error) {
	console.warn('[Economist Detail] Could not fetch related data:', error);
	// Use fallback data if content collections are not available
	economistPredictions = [];
	economistVideos = [];
}

// Utility functions for formatting (from Vue components)
function formatNumber(num: number): string {
	if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
	if (num >= 1000) return `${(num / 1000).toFixed(0)}K`;
	return num.toString();
}

function formatScore(score: number): string {
	return Math.round((score || 0) * 100).toString();
}

function getTrustScoreColor(score: number): string {
	const percentage = (score || 0) * 100;
	if (percentage >= 80) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
	if (percentage >= 60) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
	if (percentage >= 40) return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
	return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
}

function getTrustScoreLabel(score: number): string {
	const percentage = (score || 0) * 100;
	if (percentage >= 80) return 'Yüksek Güven';
	if (percentage >= 60) return 'Orta Güven';
	if (percentage >= 40) return 'Düşük Güven';
	return 'Çok Düşük Güven';
}

function getInitials(name: string): string {
	return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
}

function formatDate(date: string | Date): string {
	return new Date(date).toLocaleDateString('tr-TR', {
		year: 'numeric',
		month: 'long',
		day: 'numeric'
	});
}

function formatRelativeDate(date: string | Date): string {
	const now = new Date();
	const targetDate = new Date(date);
	const diffInMs = now.getTime() - targetDate.getTime();
	const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

	if (diffInDays === 0) return 'Bugün';
	if (diffInDays === 1) return 'Dün';
	if (diffInDays < 7) return `${diffInDays} gün önce`;
	if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} hafta önce`;
	if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} ay önce`;
	return `${Math.floor(diffInDays / 365)} yıl önce`;
}

// Calculate circular progress for trust score (matching Vue component exactly)
function getCircularProgress(score: number) {
	const circumference = 2 * Math.PI * 50; // radius = 50
	const offset = circumference - (score * circumference);
	return { circumference, offset };
}

const trustScoreProgress = getCircularProgress(economist?.trustScore || economist?.accuracyScore || 0);
---

<Layout title={pageTitle} description={pageDescription}>
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
		<!-- Main Content -->
		<!-- Economist Profile Header -->
				<div class="mb-8">
					<div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
						<div class="p-8">
							<!-- Header Section -->
							<div class="flex flex-col md:flex-row items-start md:items-center space-y-6 md:space-y-0 md:space-x-8 mb-8">
								<!-- Avatar -->
								<div class="flex-shrink-0">
									<div class="relative">
										{economist.avatarUrl ? (
											<img
												src={economist.avatarUrl}
												alt={economist.name}
												class="w-24 h-24 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"
											/>
										) : (
											<div class="w-24 h-24 rounded-full bg-gradient-to-br from-amber-400 to-amber-600 flex items-center justify-center text-white text-2xl font-bold shadow-lg">
												{getInitials(economist.name)}
											</div>
										)}

										<!-- Verification Badge -->
										{economist.isVerified && (
											<div class="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-800" title="Doğrulanmış Ekonomist">
												<svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
													<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
												</svg>
											</div>
										)}
									</div>
								</div>

								<!-- Basic Info -->
								<div class="flex-1 min-w-0">
									<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
										<h1 class="text-3xl font-bold text-gray-900 dark:text-white truncate">
											{economist.name}
										</h1>
										<div class="flex items-center space-x-4 mt-2 sm:mt-0">
											<!-- Trust Score Badge -->
											<div class="flex items-center space-x-2">
												<span class="text-sm font-medium text-gray-600 dark:text-gray-400">Güven Skoru:</span>
												<div class="flex items-center space-x-1">
													<div class={`px-3 py-1 rounded-full text-sm font-bold ${getTrustScoreColor(economist.trustScore || economist.accuracyScore || 0)}`}>
														{formatScore(economist.trustScore || economist.accuracyScore || 0)}
													</div>
													<div class="text-xs text-gray-500 dark:text-gray-400">/100</div>
												</div>
											</div>
										</div>
									</div>

									<!-- YouTube Channel Info -->
									<div class="flex flex-wrap items-center gap-4 mb-4">
										{economist.youtubeChannelUrl ? (
											<a
												href={economist.youtubeChannelUrl}
												target="_blank"
												rel="noopener noreferrer"
												class="inline-flex items-center space-x-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors"
											>
												<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
													<path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
												</svg>
												<span class="font-medium">{economist.youtubeChannelName}</span>
											</a>
										) : economist.youtubeChannelName && (
											<div class="inline-flex items-center space-x-2 text-red-600 dark:text-red-400">
												<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
													<path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
												</svg>
												<span class="font-medium">{economist.youtubeChannelName}</span>
											</div>
										)}

										{economist.subscriberCount && (
											<div class="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
												<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
												</svg>
												<span class="text-sm">{formatNumber(economist.subscriberCount)} abone</span>
											</div>
										)}
									</div>

									<!-- Bio -->
									{economist.bio && (
										<p class="text-gray-700 dark:text-gray-300 leading-relaxed">{economist.bio}</p>
									)}
								</div>
							</div>

							<!-- Quick Stats -->
							<div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
								<div class="text-center">
									<div class="text-2xl font-bold text-gray-900 dark:text-white">{economist.totalPredictions || 0}</div>
									<div class="text-sm text-gray-600 dark:text-gray-400">Toplam Tahmin</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-gray-900 dark:text-white">{economist.verifiedPredictions || 0}</div>
									<div class="text-sm text-gray-600 dark:text-gray-400">Doğrulanmış</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-green-600 dark:text-green-400">{economist.correctPredictions || 0}</div>
									<div class="text-sm text-gray-600 dark:text-gray-400">Doğru Tahmin</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-amber-600 dark:text-amber-400">{formatScore(economist.accuracyScore || 0)}%</div>
									<div class="text-sm text-gray-600 dark:text-gray-400">Doğruluk Oranı</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Content Grid -->
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
					<!-- Main Content Area (2/3 width) -->
					<div class="lg:col-span-2 space-y-8">
						<!-- Recent Predictions -->
						<div>
							<h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Son Tahminler</h2>
							<div class="space-y-6">
								{economistPredictions.length > 0 ? (
									economistPredictions.map((prediction: any) => (
										<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
											<div class="flex items-start justify-between mb-4">
												<div class="flex-1">
													<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
														{prediction.title}
													</h3>
													<p class="text-gray-600 dark:text-gray-300 text-sm mb-3">
														{prediction.description}
													</p>
													<div class="flex items-center space-x-4 text-sm">
														<span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
															prediction.category === 'Enflasyon' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
															prediction.category === 'Faiz' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
															prediction.category === 'Döviz' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
															'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
														}`}>
															{prediction.category}
														</span>
														<span class="text-gray-500 dark:text-gray-400">
															{formatRelativeDate(prediction.createdAt)}
														</span>
													</div>
												</div>
												<div class="flex-shrink-0 ml-4">
													{prediction.status === 'verified' ? (
														<div class={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
															prediction.accuracyScore >= 0.8 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
															prediction.accuracyScore >= 0.6 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
															'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
														}`}>
															{prediction.accuracyScore >= 0.8 ? '✓ Doğru' :
															 prediction.accuracyScore >= 0.6 ? '~ Kısmen' : '✗ Yanlış'}
														</div>
													) : (
														<div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
															Beklemede
														</div>
													)}
												</div>
											</div>
											{prediction.targetDate && (
												<div class="text-sm text-gray-500 dark:text-gray-400">
													<strong>Hedef Tarih:</strong> {formatDate(prediction.targetDate)}
												</div>
											)}
										</div>
									))
								) : (
									<div class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
										<div class="text-gray-400 mb-4">
											<svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
											</svg>
										</div>
										<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Henüz Tahmin Yok</h3>
										<p class="text-gray-600 dark:text-gray-400">Bu ekonomist için henüz analiz edilmiş tahmin bulunmuyor.</p>
									</div>
								)}
							</div>
						</div>

						<!-- Recent Videos -->
						<div>
							<h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Son Videolar</h2>
							<div class="space-y-6">
								{economistVideos.length > 0 ? (
									economistVideos.map((video: any) => (
										<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow">
											<div class="flex">
												<!-- Video Thumbnail -->
												<div class="flex-shrink-0">
													<div class="w-32 h-20 bg-gray-200 dark:bg-gray-700 flex items-center justify-center relative">
														{video.thumbnailUrl ? (
															<img
																src={video.thumbnailUrl}
																alt={video.title}
																class="w-full h-full object-cover"
															/>
														) : (
															<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
															</svg>
														)}
														<!-- Play Button Overlay -->
														<div class="absolute inset-0 flex items-center justify-center">
															<div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
																<svg class="w-4 h-4 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
																	<path d="M8 5v14l11-7z"/>
																</svg>
															</div>
														</div>
													</div>
												</div>

												<!-- Video Info -->
												<div class="flex-1 p-4">
													<h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
														{video.title}
													</h3>
													<div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
														<span>{formatRelativeDate(video.publishedAt)}</span>
														{video.duration && (
															<span>{video.duration}</span>
														)}
														{video.viewCount && (
															<span>{formatNumber(video.viewCount)} görüntülenme</span>
														)}
													</div>
													{video.youtubeVideoId && (
														<div class="mt-2">
															<a
																href={`https://www.youtube.com/watch?v=${video.youtubeVideoId}`}
																target="_blank"
																rel="noopener noreferrer"
																class="inline-flex items-center text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
															>
																<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
																	<path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
																</svg>
																YouTube'da İzle
															</a>
														</div>
													)}
												</div>
											</div>
										</div>
									))
								) : (
									<div class="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
										<div class="text-gray-400 mb-4">
											<svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
											</svg>
										</div>
										<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Henüz Video Yok</h3>
										<p class="text-gray-600 dark:text-gray-400">Bu ekonomist için henüz analiz edilmiş video bulunmuyor.</p>
									</div>
								)}
							</div>
						</div>
					</div>

					<!-- Sidebar (1/3 width) -->
					<div class="lg:col-span-1">
						<!-- Performance Statistics -->
						<div class="sticky top-6">
							<h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Performans İstatistikleri</h2>

							<div class="space-y-6">
								<!-- Trust Score Card -->
								<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
									<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Güven Skoru</h3>
									<div class="flex items-center justify-center mb-4">
										<div class="relative w-32 h-32">
											<!-- Circular Progress -->
											<svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
												<circle
													cx="60"
													cy="60"
													r="50"
													stroke="currentColor"
													stroke-width="8"
													fill="none"
													class="text-gray-200 dark:text-gray-700"
												/>
												<circle
													cx="60"
													cy="60"
													r="50"
													stroke="currentColor"
													stroke-width="8"
													fill="none"
													stroke-linecap="round"
													stroke-dasharray={trustScoreProgress.circumference}
													stroke-dashoffset={trustScoreProgress.offset}
													class={`transition-all duration-1000 ease-out ${getTrustScoreColor(economist.trustScore || economist.accuracyScore || 0).includes('green') ? 'text-green-500' : getTrustScoreColor(economist.trustScore || economist.accuracyScore || 0).includes('yellow') ? 'text-yellow-500' : getTrustScoreColor(economist.trustScore || economist.accuracyScore || 0).includes('orange') ? 'text-orange-500' : 'text-red-500'}`}
												/>
											</svg>
											<div class="absolute inset-0 flex items-center justify-center">
												<div class="text-center">
													<div class="text-2xl font-bold text-gray-900 dark:text-white">
														{formatScore(economist.trustScore || economist.accuracyScore || 0)}
													</div>
													<div class="text-xs text-gray-500 dark:text-gray-400">/ 100</div>
												</div>
											</div>
										</div>
									</div>
									<p class="text-sm text-gray-600 dark:text-gray-400 text-center">
										{getTrustScoreLabel(economist.trustScore || economist.accuracyScore || 0)}
									</p>
								</div>

								<!-- Accuracy Rate Card -->
								<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
									<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Doğruluk Oranı</h3>
									<div class="text-center mb-4">
										<div class="text-3xl font-bold text-green-600 dark:text-green-400">
											{formatScore(economist.accuracyScore || 0)}%
										</div>
										<p class="text-sm text-gray-600 dark:text-gray-400">
											{economist.correctPredictions || 0} / {economist.verifiedPredictions || 0} doğru tahmin
										</p>
									</div>
									<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
										<div
											style={`width: ${formatScore(economist.accuracyScore || 0)}%`}
											class="bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out"
										></div>
									</div>
								</div>

								<!-- Channel Stats -->
								{economist.subscriberCount && (
									<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
										<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Kanal İstatistikleri</h3>
										<div class="space-y-4">
											<div class="flex justify-between items-center">
												<span class="text-gray-600 dark:text-gray-400">Abone Sayısı</span>
												<span class="font-semibold text-gray-900 dark:text-white">{formatNumber(economist.subscriberCount)}</span>
											</div>
											{economist.youtubeChannelName && (
												<div class="flex justify-between items-center">
													<span class="text-gray-600 dark:text-gray-400">Kanal Adı</span>
													<span class="font-semibold text-gray-900 dark:text-white truncate ml-2">{economist.youtubeChannelName}</span>
												</div>
											)}
										</div>
									</div>
								)}

								<!-- Performance Metrics -->
								<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
									<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Detaylı Metrikler</h3>
									<div class="space-y-3">
										<div class="flex justify-between items-center">
											<span class="text-gray-600 dark:text-gray-400">Toplam Tahmin</span>
											<span class="font-semibold text-gray-900 dark:text-white">{economist.totalPredictions || 0}</span>
										</div>
										<div class="flex justify-between items-center">
											<span class="text-gray-600 dark:text-gray-400">Doğrulanmış</span>
											<span class="font-semibold text-blue-600 dark:text-blue-400">{economist.verifiedPredictions || 0}</span>
										</div>
										<div class="flex justify-between items-center">
											<span class="text-gray-600 dark:text-gray-400">Doğru Tahmin</span>
											<span class="font-semibold text-green-600 dark:text-green-400">{economist.correctPredictions || 0}</span>
										</div>
										<div class="flex justify-between items-center">
											<span class="text-gray-600 dark:text-gray-400">Yanlış Tahmin</span>
											<span class="font-semibold text-red-600 dark:text-red-400">{(economist.verifiedPredictions || 0) - (economist.correctPredictions || 0)}</span>
										</div>
									</div>
								</div>

								<!-- Quick Stats Summary (matching Vue component) -->
								<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
									<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Özet İstatistikler</h3>
									<div class="grid grid-cols-2 gap-4">
										<div class="text-center">
											<div class="text-xl font-bold text-blue-600 dark:text-blue-400">
												{economist.totalPredictions || 0}
											</div>
											<div class="text-xs text-gray-600 dark:text-gray-400">Toplam Tahmin</div>
										</div>
										<div class="text-center">
											<div class="text-xl font-bold text-purple-600 dark:text-purple-400">
												{economist.totalVideos || 0}
											</div>
											<div class="text-xs text-gray-600 dark:text-gray-400">Analiz Edilen Video</div>
										</div>
										<div class="text-center">
											<div class="text-xl font-bold text-green-600 dark:text-green-400">
												{economist.verifiedPredictions || 0}
											</div>
											<div class="text-xs text-gray-600 dark:text-gray-400">Doğrulanmış</div>
										</div>
										<div class="text-center">
											<div class="text-xl font-bold text-amber-600 dark:text-amber-400">
												{economist.correctPredictions || 0}
											</div>
											<div class="text-xs text-gray-600 dark:text-gray-400">Doğru Tahmin</div>
										</div>
									</div>
								</div>

								<!-- Last Updated -->
								{economist.updatedAt && (
									<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
										<h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Son Güncelleme</h3>
										<p class="text-gray-600 dark:text-gray-400 text-sm">
											{formatRelativeDate(economist.updatedAt)}
										</p>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
	</div>
</Layout>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
