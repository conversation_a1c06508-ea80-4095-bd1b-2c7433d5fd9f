/**
 * Shared Types
 *
 * Common types and interfaces used across different domain services.
 */

import type { DbConnection } from '@/server/db';

// ============================================================================
// COMMON INTERFACES
// ============================================================================

/**
 * Base service interface that all domain services should implement
 */
export interface BaseService {
  /**
   * Database connection used by the service
   */
  db: DbConnection;
}

/**
 * Pagination options for queries
 */
export interface PaginationOptions {
  page?: number;
  pageSize?: number;
  limit?: number;
  offset?: number;
}

/**
 * Pagination result metadata
 */
export interface PaginationResult {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Filter options for queries
 */
export interface FilterOptions {
  isActive?: boolean;
  isCurated?: boolean;
  curated?: boolean;
  category?: string;
  status?: string;
  economistId?: number;
  startDate?: Date;
  endDate?: Date;
  orderBy?: string;
}

/**
 * Sort options for queries
 */
export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

/**
 * Query options combining pagination, filtering, and sorting
 */
export interface QueryOptions extends PaginationOptions, FilterOptions {
  sort?: SortOptions;
}

// ============================================================================
// COMMON RESULT TYPES
// ============================================================================

/**
 * Standard service result with data and pagination
 */
export interface ServiceResult<T> {
  data: T[];
  pagination: PaginationResult;
}

/**
 * Single item service result
 */
export interface SingleServiceResult<T> {
  data: T | null;
}

/**
 * Service result with metadata
 */
export interface ServiceResultWithMeta<T, M = Record<string, unknown>> {
  data: T[];
  pagination: PaginationResult;
  meta: M;
}

// ============================================================================
// COMMON DATA TYPES
// ============================================================================

/**
 * Base entity with common fields
 */
export interface BaseEntity {
  id: number;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

/**
 * Entity with slug
 */
export interface SlugEntity extends BaseEntity {
  slug: string;
}

/**
 * Entity with metadata
 */
export interface MetaEntity extends BaseEntity {
  metaDescription?: string | null;
  metaKeywords?: string | null;
}

// ============================================================================
// DOMAIN-SPECIFIC COMMON TYPES
// ============================================================================

/**
 * YouTube-related data
 */
export interface YouTubeData {
  youtubeVideoId?: string;
  youtubeChannelId?: string;
  youtubeChannelName?: string;
  youtubeUrl?: string;
  thumbnailUrl?: string | null;
  duration?: number;
  viewCount?: number;
  publishedAt?: Date | null;
}

/**
 * Statistics data
 */
export interface StatsData {
  count: number;
  percentage?: number;
  trend?: 'up' | 'down' | 'stable';
  change?: number;
}

/**
 * Accuracy-related data
 */
export interface AccuracyData {
  accuracyScore: number;
  trustScore: number;
  totalPredictions: number;
  correctPredictions: number;
  verifiedPredictions: number;
}

// ============================================================================
// BUILD-TIME TYPES
// ============================================================================

/**
 * Build-time data structure
 */
export interface BuildTimeData<T> {
  data: T;
  fetchedAt: string;
  environment: string;
}

/**
 * SQL query execution options
 */
export interface SQLExecutionOptions {
  dbName: string;
  isLocal: boolean;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

/**
 * Service error with context
 */
export interface ServiceError extends Error {
  code: string;
  context?: Record<string, unknown>;
}

/**
 * Validation error
 */
export interface ValidationError extends ServiceError {
  field: string;
  value: unknown;
}
