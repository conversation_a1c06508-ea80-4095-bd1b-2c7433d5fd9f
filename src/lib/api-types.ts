/**
 * Standard API Response Types
 * Based on the API design conventions in 04_api_design_conventions.md
 */

export interface ApiResponse<T = unknown> {
	status: 'success' | 'error' | 'fail';
	data?: T;
	message?: string;
	errorCode?: string;
	details?: Array<string | object>;
}

export interface PaginatedApiResponse<T = unknown> extends ApiResponse<T[]> {
	pagination?: {
		currentPage: number;
		pageSize: number;
		totalItems: number;
		totalPages: number;
	};
}

/**
 * Specific API Response Types
 */

// Articles API
export interface Article {
	id: number;
	title: string;
	summary: string;
	category: string;
	readTime: string;
	publishedAt?: string;
	slug?: string;
}

// Videos API
export interface Video {
	id: number;
	videoTitle: string;
	youtubeVideoId: string;
	thumbnailUrl: string;
	duration: string;
	viewCount: string;
	publishedAt: string;
	publishDate: string;
	youtubeUrl: string;
	economist: {
		id: number;
		name: string;
		channelName: string;
		avatarUrl: string;
		initials: string;
		title: string;
	};
	predictions: Prediction[];
}

// Predictions API
export interface Prediction {
	id: number;
	title: string;
	description: string;
	category: string;
	predictionType: string;
	targetValue: {
		target?: number;
		min?: number;
		max?: number;
		threshold?: number;
		direction?: string;
		[key: string]: unknown;
	};
	targetDate: string;
	confidenceLevel: number | null;
	status: 'pending' | 'verified_correct' | 'verified_incorrect' | 'expired';
	accuracyScore: number | null;
	verifiedAt: string | null;
	tags: string[];
	extractedAt: string;
	createdAt: string;
	economist: {
		id: number;
		name: string;
		avatarUrl: string;
		isVerified: boolean;
	};
	video: Video;
}

export interface VerificationStats {
	totalPending: number;
	totalVerified: number;
	accuracyRate: number;
	recentVerifications: number;
}

export interface PredictionsResponse extends PaginatedApiResponse<Prediction> {
	verificationStats?: VerificationStats;
}

export interface VerificationResult {
	id: number;
	status: 'verified_correct' | 'verified_incorrect';
	accuracyScore: number;
	verifiedAt: string;
	notes?: string;
}

export interface BulkVerificationSummary {
	totalProcessed: number;
	verified: number;
	failed: number;
	summary: VerificationResult[];
}

/**
 * Type guards for API responses
 */
export function isApiResponse<T>(response: unknown): response is ApiResponse<T> {
	return (
		typeof response === 'object' &&
		response !== null &&
		'status' in response &&
		typeof (response as Record<string, unknown>).status === 'string'
	);
}

export function isSuccessResponse<T>(response: unknown): response is ApiResponse<T> & { status: 'success' } {
	return isApiResponse(response) && response.status === 'success';
}

/**
 * Helper function to safely parse API responses
 */
export async function parseApiResponse<T>(response: Response): Promise<ApiResponse<T>> {
	const data = await response.json();

	if (!isApiResponse<T>(data)) {
		throw new Error('Invalid API response format');
	}

	return data;
}
