// TCMB Service: Fetches economic data from the TCMB EVDS API
// This module is a template for integrating other data sources (e.g., TurkStat, BIST)

import fetch from 'node-fetch';

export interface TcmbOptions {
  series: string; // e.g., 'TP.DK.USD.S.YTL'
  startDate: string; // format: 'DD-MM-YYYY'
  endDate: string; // format: 'DD-MM-YYYY'
}

export interface TcmbDataItem {
  Tarih: string;
  UNIXTIME?: {
    $numberLong: string;
  };
  [key: string]: string | null | { $numberLong: string } | undefined;
}

export interface TcmbApiResponse {
  items: TcmbDataItem[];
}

const BASE_URL = 'https://evds2.tcmb.gov.tr/service/evds/';

export async function fetchTcmbData(options: TcmbOptions, apiKey: string): Promise<TcmbDataItem[]> {
  // Build URL with query parameters (no leading ?)
  const queryParams = `series=${options.series}&startDate=${options.startDate}&endDate=${options.endDate}&type=json`;
  const url = `${BASE_URL}${queryParams}`;

  // Use header-based authentication instead of query parameter
  const res = await fetch(url, {
    method: 'GET',
    headers: {
      'key': apiKey,
      'User-Agent': 'Economist-Tracker/1.0',
      'Accept': 'application/json'
    }
  });

  if (!res.ok) {
    throw new Error(`TCMB API error: ${res.status}`);
  }

  const data = (await res.json()) as TcmbApiResponse;
  if (!data.items) throw new Error('TCMB API response missing items field');
  return data.items;
}

// Example usage (to be removed in production):
// (async () => {
//   const apiKey = process.env.TCMB_API_KEY!;
//   const data = await fetchTcmbData({
//     series: 'TP.DK.USD.S.YTL',
//     startDate: '01-01-2025',
//     endDate: '04-07-2025',
//   }, apiKey);
//   console.log(data);
// })();
