import { describe, it, expect, afterEach, vi, beforeAll } from 'vitest';
import { fetchTcmbData, type TcmbOptions } from '../../../src/server/services/verification/tcmb';
import fetch from 'node-fetch';

vi.mock('node-fetch');
let Response: typeof import('node-fetch').Response;

beforeAll(async () => {
  Response = (await vi.importActual<typeof import('node-fetch')>('node-fetch')).Response;
});

describe('fetchTcmbData', () => {
  const apiKey = 'dummy-key';
  const options: TcmbOptions = {
    series: 'TP.DK.USD.S.YTL',
    startDate: '01-01-2025',
    endDate: '04-07-2025',
  };

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('returns parsed data on success', async () => {
    const mockData = {
      items: [
        { Tarih: '04-07-2025', 'TP.DK.USD.S.YTL': '39.7485' },
      ],
    };
    (fetch as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(
      new Response(JSON.stringify(mockData), { status: 200 })
    );
    const result = await fetchTcmbData(options, apiKey);
    expect(result).toEqual(mockData.items);
  });

  it('throws on API error', async () => {
    (fetch as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(
      new Response('Not found', { status: 404 })
    );
    await expect(fetchTcmbData(options, apiKey)).rejects.toThrow('TCMB API error: 404');
  });

  it('handles empty items array', async () => {
    const mockData = { items: [] };
    (fetch as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(
      new Response(JSON.stringify(mockData), { status: 200 })
    );
    const result = await fetchTcmbData(options, apiKey);
    expect(result).toEqual([]);
  });

  it('throws if response is not valid JSON', async () => {
    (fetch as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(
      new Response('not-json', { status: 200 })
    );
    await expect(fetchTcmbData(options, apiKey)).rejects.toThrow();
  });

  it('throws if items field is missing', async () => {
    const mockData = { notItems: [] };
    (fetch as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(
      new Response(JSON.stringify(mockData), { status: 200 })
    );
    await expect(fetchTcmbData(options, apiKey)).rejects.toThrow();
  });
});
