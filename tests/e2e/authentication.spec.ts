import { test, expect } from '@playwright/test';

/**
 * E2E Authentication Tests - Testing actual user interactions
 * These tests simulate real user behavior through the UI
 */

test.describe('Authentication Flow Tests', () => {
  // Test data - matches the users in global-setup.ts
  const existingUser = {
    email: '<EMAIL>',
    password: 'testpass123'
  };

  test.beforeEach(async ({ page }) => {
    // Start with a clean slate - clear cookies
    await page.context().clearCookies();
    // Add small delay to avoid overwhelming the worker
    await page.waitForTimeout(500);
  });

  test('should show login form when accessing login page', async ({ page }) => {
    await page.goto('/login');

    // Verify we're on the login page
    await expect(page).toHaveURL('/login');

    // Verify login form elements exist
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should authenticate with valid credentials through UI', async ({ page }) => {
    await page.goto('/login');

    // Fill in the login form
    await page.fill('input[name="email"]', existingUser.email);
    await page.fill('input[name="password"]', existingUser.password);

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for navigation or success indicator
    await page.waitForLoadState('networkidle');

    // Should be redirected to dashboard or home page after successful login
    // The exact behavior depends on your app's login flow
    await expect(page).not.toHaveURL('/login');
  });

  test('should show error message for invalid credentials', async ({ page }) => {
    await page.goto('/login');

    // Fill in invalid credentials
    await page.fill('input[name="email"]', existingUser.email);
    await page.fill('input[name="password"]', 'wrongpassword');

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait for error message to appear
    await page.waitForTimeout(1000);

    // Should still be on login page
    await expect(page).toHaveURL('/login');

    // Should show error message div (the error-message element should become visible)
    await expect(page.locator('#error-message')).toBeVisible({ timeout: 5000 });

    // Check that the error message contains relevant text
    await expect(page.locator('#error-message')).toContainText(/Invalid|kimlik|Hatalı/i);
  });

  test('should navigate through complete authentication flow', async ({ page }) => {
    // Start at home page
    await page.goto('/');
    await expect(page).toHaveTitle(/Ana Sayfa - YouTube Economist Trust Score/);

    // Navigate to login page
    await page.goto('/login');
    await expect(page).toHaveURL('/login');

    // Login with valid credentials
    await page.fill('input[name="email"]', existingUser.email);
    await page.fill('input[name="password"]', existingUser.password);
    await page.click('button[type="submit"]');

    // Wait for successful login
    await page.waitForLoadState('networkidle');

    // Should be redirected away from login page
    await expect(page).not.toHaveURL('/login');
  });

  test('should access register page', async ({ page }) => {
    await page.goto('/register');

    // Verify we're on the register page
    await expect(page).toHaveURL('/register');

    // Verify register form elements exist
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });
});
