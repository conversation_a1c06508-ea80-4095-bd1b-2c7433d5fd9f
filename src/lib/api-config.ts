/**
 * API Configuration and Constants
 * Centralizes API versioning and endpoint configuration
 */

// Declare global URLSearchParams for TypeScript compatibility
declare const URLSearchParams: typeof globalThis.URLSearchParams;

export const API_CONFIG_v1 = {
	baseUrl: '/api/v1',
	version: 'v1',
} as const;

/**
 * API endpoint builders for consistent versioning
 */
export const apiEndpoints = {
	// Authentication endpoints
	auth: {
		login: `${API_CONFIG_v1.baseUrl}/auth/login`,
		register: `${API_CONFIG_v1.baseUrl}/auth/register`,
		logout: `${API_CONFIG_v1.baseUrl}/auth/logout`,
		me: `${API_CONFIG_v1.baseUrl}/auth/me`,
		updateProfile: `${API_CONFIG_v1.baseUrl}/auth/update-profile`,
	},

	// Economists endpoints
	economists: {
		list: `${API_CONFIG_v1.baseUrl}/economists`,
		top: `${API_CONFIG_v1.baseUrl}/economists/top`,
		byId: (id: number) => `${API_CONFIG_v1.baseUrl}/economists/${id}`,
	},

	// Predictions endpoints
	predictions: {
		list: `${API_CONFIG_v1.baseUrl}/predictions`,
		byId: (id: number) => `${API_CONFIG_v1.baseUrl}/predictions/${id}`,
		verify: (id: number) => `${API_CONFIG_v1.baseUrl}/predictions/${id}/verify`,
		verificationQueue: `${API_CONFIG_v1.baseUrl}/predictions/verification-queue`,
		verifyBulk: `${API_CONFIG_v1.baseUrl}/predictions/verify-bulk`,
		verifyAutomated: `${API_CONFIG_v1.baseUrl}/predictions/verify-automated`,
		scheduler: `${API_CONFIG_v1.baseUrl}/predictions/scheduler`,
	},

	// Videos endpoints
	videos: {
		list: `${API_CONFIG_v1.baseUrl}/videos`,
		recent: `${API_CONFIG_v1.baseUrl}/videos/recent`,
		byId: (id: number) => `${API_CONFIG_v1.baseUrl}/videos/${id}`,
	},

	// Articles endpoints
	articles: {
		curated: `${API_CONFIG_v1.baseUrl}/articles/curated`,
		bySlug: (slug: string) => `${API_CONFIG_v1.baseUrl}/articles/${slug}`,
	},

	// Stats endpoints
	stats: {
		platform: `${API_CONFIG_v1.baseUrl}/stats/platform`,
	},

	// Health check endpoint
	health: `${API_CONFIG_v1.baseUrl}/health`,
} as const;

/**
 * Build query string for API requests
 */
export function buildApiUrl(endpoint: string, params?: Record<string, string | number | boolean>): string {
	if (!params || Object.keys(params).length === 0) {
		return endpoint;
	}

	const queryParams = new URLSearchParams();
	Object.entries(params).forEach(([key, value]) => {
		queryParams.append(key, String(value));
	});

	return `${endpoint}?${queryParams.toString()}`;
}
