import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';
import { economists } from './economists';
import { videos } from './videos';

export const predictions = sqliteTable('predictions', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	title: text('title').notNull(),
	description: text('description').notNull(),
	category: text('category').notNull(), // e.g., 'economic_indicator', 'market_prediction', 'currency', 'inflation'
	predictionType: text('prediction_type').notNull(), // e.g., 'increase', 'decrease', 'range', 'exact_value'
	targetValue: text('target_value'), // JSON string for complex predictions
	targetDate: integer('target_date', { mode: 'timestamp' }).notNull(),
	confidenceLevel: real('confidence_level'), // 0-1 scale

	// Source information
	economistId: integer('economist_id').notNull().references(() => economists.id),
	videoId: integer('video_id').notNull().references(() => videos.id),
	extractedAt: integer('extracted_at', { mode: 'timestamp' }).notNull(),
	sourceText: text('source_text'), // Original text from which prediction was extracted

	// Verification status
	status: text('status', {
		enum: ['pending', 'partially_verified', 'verified_correct', 'verified_incorrect', 'expired']
	}).notNull().default('pending'),
	verificationMethod: text('verification_method'), // e.g., 'automated', 'manual'
	verificationSource: text('verification_source'), // Data source name or URL
	verificationNotes: text('verification_notes'),
	verifiedAt: integer('verified_at', { mode: 'timestamp' }),

	// Automated verification fields
	actualValue: real('actual_value'), // Actual value fetched from data sources
	accuracy: real('accuracy'), // Calculated accuracy percentage (0-1)
	dataSource: text('data_source'), // Name of the data source used for verification

	// Scoring (legacy fields for compatibility)
	accuracyScore: real('accuracy_score'), // 0-1 scale
	partialCorrectness: real('partial_correctness'), // For partially correct predictions

	// Metadata
	tags: text('tags'), // JSON array of tags
	isActive: integer('is_active', { mode: 'boolean' }).default(true),
	createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
	updatedAt: integer('updated_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
});
