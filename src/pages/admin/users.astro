---
import Layout from '../../layouts/Layout.astro';
import { users } from '@/server/db/schema';
import { count, like, or, and, eq, desc, asc } from 'drizzle-orm';

// This page should not be prerendered as it's an admin page requiring dynamic data
export const prerender = false;

// Check if user has admin access
const auth = (Astro.locals as any).auth;
if (!auth?.isAuthenticated) {
	return Astro.redirect('/login?redirect=' + encodeURIComponent(Astro.url.pathname));
}

// Check if user has admin role (only admins can access user management)
const isAdmin = auth.roles?.includes('ROLE_ADMIN');
if (!isAdmin) {
	return Astro.redirect('/admin/dashboard?error=access_denied');
}

// Get database connection
const db = (Astro.locals as any).db;

// Get query parameters for search and pagination
const url = new URL(Astro.request.url);
const searchQuery = url.searchParams.get('search') || '';
const currentPage = parseInt(url.searchParams.get('page') || '1');
const pageSize = parseInt(url.searchParams.get('limit') || '20');
const sortBy = url.searchParams.get('sortBy') || 'createdAt';
const sortOrder = url.searchParams.get('sortOrder') || 'desc';
const roleFilter = url.searchParams.get('role') || '';
const statusFilter = url.searchParams.get('status') || '';

// Calculate offset for pagination
const offset = (currentPage - 1) * pageSize;

// Fetch real users from the database with pagination and search
let usersList: any[] = [];
let totalUsers = 0;
let userStats = {
	total: 0,
	active: 0,
	suspended: 0,
	moderators: 0
};

try {
	if (db) {
		// Build where conditions based on filters
		const whereConditions = [];

		// Search condition (search in username, email)
		if (searchQuery.trim()) {
			whereConditions.push(
				or(
					like(users.username, `%${searchQuery}%`),
					like(users.email, `%${searchQuery}%`)
				)
			);
		}

		// Role filter
		if (roleFilter) {
			whereConditions.push(eq(users.role, roleFilter as any));
		}

		// Status filter
		if (statusFilter === 'active') {
			whereConditions.push(eq(users.isActive, true));
		} else if (statusFilter === 'suspended') {
			whereConditions.push(eq(users.isActive, false));
		}

		// Combine all conditions
		const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

		// Get total count for pagination
		const countResult = await db
			.select({ count: count() })
			.from(users)
			.where(whereClause);
		totalUsers = countResult[0]?.count || 0;

		// Determine sort order
		const orderBy = sortOrder === 'asc' ? asc : desc;
		let sortField;
		switch (sortBy) {
			case 'username':
				sortField = users.username;
				break;
			case 'email':
				sortField = users.email;
				break;
			case 'role':
				sortField = users.role;
				break;
			case 'lastLoginAt':
				sortField = users.lastLoginAt;
				break;
			default:
				sortField = users.createdAt;
		}

		// Fetch users with pagination, search, and sorting
		usersList = await db.select({
			id: users.id,
			email: users.email,
			username: users.username,
			role: users.role,
			isActive: users.isActive,
			emailVerified: users.emailVerified,
			lastLoginAt: users.lastLoginAt,
			createdAt: users.createdAt,
		})
		.from(users)
		.where(whereClause)
		.orderBy(orderBy(sortField))
		.limit(pageSize)
		.offset(offset);

		// Get overall stats (not filtered)
		const allUsers = await db.select({
			role: users.role,
			isActive: users.isActive,
		}).from(users);

		userStats.total = allUsers.length;
		userStats.active = allUsers.filter((u: any) => u.isActive).length;
		userStats.suspended = allUsers.filter((u: any) => !u.isActive).length;
		userStats.moderators = allUsers.filter((u: any) => u.role === 'moderator' || u.role === 'admin').length;
	}
} catch (error) {
	console.error('Error fetching users:', error);
}

// Calculate pagination info
const totalPages = Math.ceil(totalUsers / pageSize);
const hasNextPage = currentPage < totalPages;
const hasPrevPage = currentPage > 1;

// Format users for display
const formattedUsers = usersList.map((user: any) => ({
	id: user.id,
	username: user.username || 'Belirlenmemiş',
	email: user.email,
	roles: [user.role], // Convert single role to array for consistency with existing template
	status: user.isActive ? 'active' : 'suspended',
	lastLogin: user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString('tr-TR') : 'Hiçbir zaman',
	emailVerified: user.emailVerified,
	createdAt: new Date(user.createdAt!).toLocaleDateString('tr-TR')
}));

// Helper function to build URL with parameters
function buildUrl(params: Record<string, string | number>) {
	const url = new URL('/admin/users', Astro.url.origin);

	// Add existing parameters
	if (searchQuery) url.searchParams.set('search', searchQuery);
	if (roleFilter) url.searchParams.set('role', roleFilter);
	if (statusFilter) url.searchParams.set('status', statusFilter);
	if (sortBy !== 'createdAt') url.searchParams.set('sortBy', sortBy);
	if (sortOrder !== 'desc') url.searchParams.set('sortOrder', sortOrder);
	if (pageSize !== 20) url.searchParams.set('limit', pageSize.toString());

	// Override with new parameters
	Object.entries(params).forEach(([key, value]) => {
		if (value !== '' && value !== null && value !== undefined) {
			url.searchParams.set(key, value.toString());
		} else {
			url.searchParams.delete(key);
		}
	});

	return url.pathname + url.search;
}
---

<Layout title="User Management - Kullanıcı Yönetimi">
	<main class="min-h-screen bg-gray-50 dark:bg-gray-900">
		<!-- Header -->
		<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between items-center py-4">
					<div class="flex items-center space-x-4">
						<div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
							<svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
								<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
						</div>
						<div>
							<h1 class="text-2xl font-bold text-gray-900 dark:text-white">
								Kullanıcı Yönetimi
							</h1>
							<p class="text-sm text-gray-500 dark:text-gray-400">
								Kullanıcılar ve roller
							</p>
						</div>
					</div>
					<div class="flex items-center space-x-3">
						<a
							href="/admin/dashboard"
							class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-sm font-medium transition-colors"
						>
							← Admin Panel
						</a>
					</div>
				</div>
			</div>
		</header>

		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
			<!-- Stats -->
			<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Toplam Kullanıcı</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{userStats.total}</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Aktif Kullanıcı</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{userStats.active}</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.25-4.5a2.25 2.25 0 00-3.182 0L8.25 8.25l1.414 1.414L12 7.328l2.336 2.336 1.414-1.414L13.414 6l-1.414-1.414z" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Moderatörler</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{userStats.moderators}</p>
						</div>
					</div>
				</div>

				<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
								<svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
								</svg>
							</div>
						</div>
						<div class="ml-5">
							<p class="text-sm font-medium text-gray-500 dark:text-gray-400">Askıya Alınmış</p>
							<p class="text-3xl font-bold text-gray-900 dark:text-white">{userStats.suspended}</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Search and Filters -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
				<div class="p-6">
					<form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
						<!-- Search -->
						<div class="md:col-span-2">
							<label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
								Arama
							</label>
							<input
								type="text"
								id="search"
								name="search"
								value={searchQuery}
								placeholder="Kullanıcı adı veya e-posta ara..."
								class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
							/>
						</div>

						<!-- Role Filter -->
						<div>
							<label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
								Rol
							</label>
							<select
								id="role"
								name="role"
								class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
							>
								<option value="">Tüm Roller</option>
								<option value="admin" selected={roleFilter === 'admin'}>Admin</option>
								<option value="moderator" selected={roleFilter === 'moderator'}>Moderator</option>
								<option value="user" selected={roleFilter === 'user'}>Kullanıcı</option>
							</select>
						</div>

						<!-- Status Filter -->
						<div>
							<label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
								Durum
							</label>
							<select
								id="status"
								name="status"
								class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
							>
								<option value="">Tüm Durumlar</option>
								<option value="active" selected={statusFilter === 'active'}>Aktif</option>
								<option value="suspended" selected={statusFilter === 'suspended'}>Askıya Alınmış</option>
							</select>
						</div>

						<!-- Sort By -->
						<div>
							<label for="sortBy" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
								Sırala
							</label>
							<select
								id="sortBy"
								name="sortBy"
								class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
							>
								<option value="createdAt" selected={sortBy === 'createdAt'}>Kayıt Tarihi</option>
								<option value="username" selected={sortBy === 'username'}>Kullanıcı Adı</option>
								<option value="email" selected={sortBy === 'email'}>E-posta</option>
								<option value="lastLoginAt" selected={sortBy === 'lastLoginAt'}>Son Giriş</option>
							</select>
						</div>

						<!-- Search Button -->
						<div class="flex items-end">
							<button
								type="submit"
								class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
							>
								Filtrele
							</button>
						</div>

						<!-- Hidden fields to maintain other parameters -->
						<input type="hidden" name="sortOrder" value={sortOrder} />
						<input type="hidden" name="limit" value={pageSize} />
					</form>
				</div>
			</div>

			<!-- User Table -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
				<div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
					<div class="flex justify-between items-center">
						<div>
							<h3 class="text-lg font-medium text-gray-900 dark:text-white">Kullanıcı Listesi</h3>
							<p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
								{totalUsers} kullanıcıdan {(currentPage - 1) * pageSize + 1}-{Math.min(currentPage * pageSize, totalUsers)} arası gösteriliyor
							</p>
						</div>
						<button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
							+ Yeni Kullanıcı
						</button>
					</div>
				</div>

				<div class="overflow-x-auto">
					<table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
						<thead class="bg-gray-50 dark:bg-gray-700">
							<tr>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Kullanıcı
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Roller
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Durum
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									Son Giriş
								</th>
								<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
									İşlemler
								</th>
							</tr>
						</thead>
						<tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
							{formattedUsers.length > 0 ? formattedUsers.map((user: any) => (
								<tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
									<td class="px-6 py-4 whitespace-nowrap">
										<div class="flex items-center">
											<div class="flex-shrink-0 h-10 w-10">
												<div class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
													<svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
														<path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
													</svg>
												</div>
											</div>
											<div class="ml-4">
												<div class="text-sm font-medium text-gray-900 dark:text-white">{user.username}</div>
												<div class="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
											</div>
										</div>
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<div class="flex flex-wrap gap-1">
											{user.roles.map((role: any) => (
												<span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
													role === 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
													role === 'moderator' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400' :
													'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
												}`}>
													{role}
												</span>
											))}
										</div>
									</td>
									<td class="px-6 py-4 whitespace-nowrap">
										<span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
											user.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
											'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
										}`}>
											{user.status === 'active' ? 'Aktif' : 'Askıya Alınmış'}
										</span>
									</td>
									<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
										{user.lastLogin}
									</td>
									<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<div class="flex space-x-2">
											<button class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">Düzenle</button>
											<button class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
												{user.status === 'active' ? 'Askıya Al' : 'Aktifleştir'}
											</button>
										</div>
									</td>
								</tr>
							)) : (
								<tr>
									<td colspan="5" class="px-6 py-8 text-center text-sm text-gray-500 dark:text-gray-400">
										{db ? 'Henüz kullanıcı bulunmuyor.' : 'Veritabanı bağlantısı bulunamadı.'}
									</td>
								</tr>
							)}
						</tbody>
					</table>
				</div>

				<!-- Pagination -->
				{totalPages > 1 && (
					<div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
						<div class="flex items-center justify-between">
							<div class="text-sm text-gray-700 dark:text-gray-300">
								Sayfa {currentPage} / {totalPages} (Toplam {totalUsers} kullanıcı)
							</div>

							<div class="flex items-center space-x-2">
								{/* Previous Page */}
								{hasPrevPage ? (
									<a
										href={buildUrl({ page: currentPage - 1 })}
										class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
									>
										← Önceki
									</a>
								) : (
									<span class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed">
										← Önceki
									</span>
								)}

								{/* Page Numbers */}
								{currentPage > 1 && (
									<a
										href={buildUrl({ page: 1 })}
										class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
									>
										1
									</a>
								)}

								{currentPage > 3 && (
									<span class="px-3 py-2 text-gray-500">...</span>
								)}

								{currentPage > 2 && (
									<a
										href={buildUrl({ page: currentPage - 1 })}
										class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
									>
										{currentPage - 1}
									</a>
								)}

								<span class="px-3 py-2 border border-blue-500 rounded-md text-sm bg-blue-500 text-white font-medium">
									{currentPage}
								</span>

								{currentPage < totalPages - 1 && (
									<a
										href={buildUrl({ page: currentPage + 1 })}
										class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
									>
										{currentPage + 1}
									</a>
								)}

								{currentPage < totalPages - 2 && (
									<span class="px-3 py-2 text-gray-500">...</span>
								)}

								{currentPage < totalPages && (
									<a
										href={buildUrl({ page: totalPages })}
										class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
									>
										{totalPages}
									</a>
								)}

								{/* Next Page */}
								{hasNextPage ? (
									<a
										href={buildUrl({ page: currentPage + 1 })}
										class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
									>
										Sonraki →
									</a>
								) : (
									<span class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed">
										Sonraki →
									</span>
								)}
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	</main>
</Layout>
