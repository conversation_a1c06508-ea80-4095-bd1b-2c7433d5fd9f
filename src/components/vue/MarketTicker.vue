<template>
  <div data-testid="market-ticker" class="bg-slate-300 dark:bg-gray-900 border-b border-gray-800 dark:border-gray-700 py-2 transition-colors duration-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center space-x-6 text-xs text-gray-400 dark:text-gray-500 overflow-x-auto">

        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center space-x-6">
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">USD/TRY:</span>
            <div class="w-12 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          </div>
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">EUR/TRY:</span>
            <div class="w-12 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          </div>
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">ALTIN:</span>
            <div class="w-12 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          </div>
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">BIST100:</span>
            <div class="w-12 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          </div>
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">BTC:</span>
            <div class="w-12 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          </div>
        </div>

        <!-- Market Data -->
        <div v-else-if="hasData && marketData.length > 0" class="flex items-center space-x-6">
          <div
            v-for="item in marketData"
            :key="item.symbol"
            class="flex items-center space-x-2 whitespace-nowrap"
          >
            <span class="text-gray-900 dark:text-gray-400">{{ item.symbol }}:</span>
            <span :class="item.changeColor">{{ item.value }}</span>
            <span :class="item.changeColor">{{ item.changeText }}</span>
          </div>
        </div>

        <!-- Fallback when API fails - show placeholder values -->
        <div v-else class="flex items-center space-x-6">
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">USD/TRY:</span>
            <span class="text-gray-600 dark:text-gray-500">--</span>
          </div>
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">EUR/TRY:</span>
            <span class="text-gray-600 dark:text-gray-500">--</span>
          </div>
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">ALTIN:</span>
            <span class="text-gray-600 dark:text-gray-500">--</span>
          </div>
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">BIST100:</span>
            <span class="text-gray-600 dark:text-gray-500">--</span>
          </div>
          <div class="flex items-center space-x-2 whitespace-nowrap">
            <span class="text-gray-900 dark:text-gray-400">BTC:</span>
            <span class="text-gray-600 dark:text-gray-500">--</span>
          </div>
        </div>

        <!-- Last Updated -->
        <div v-if="!isLoading && lastUpdated" class="text-xs text-gray-500 dark:text-gray-600">
          {{ formatLastUpdated(lastUpdated) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface MarketDataItem {
  symbol: string
  value: string
  change: number
  changeText: string
  changeColor: string
}

// Reactive state - start with loading state, then show data or fallback
const isLoading = ref(true)
const hasData = ref(false)
const marketData = ref<MarketDataItem[]>([])
const lastUpdated = ref<Date | null>(null)
const refreshInterval = ref<number | null>(null)

// Helper functions
const formatLastUpdated = (date: Date): string => {
  return `Son güncelleme: ${date.toLocaleTimeString('tr-TR', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })}`
}

const formatChangeText = (change: number): string => {
  const arrow = change >= 0 ? '↑' : '↓'
  return `${arrow}${Math.abs(change).toFixed(2)}%`
}

const getChangeColor = (change: number): string => {
  return change >= 0
    ? 'text-green-800 dark:text-green-300'
    : 'text-red-800 dark:text-red-300'
}

// Data fetching function
const fetchMarketData = async (): Promise<void> => {
  try {
    // Simple fetch with basic timeout
    const response = await fetch('/api/market-data')

    if (response.ok) {
      const data = await response.json() as any

      if (data.status === 'success' && data.data && data.data.length > 0) {
        // Transform TCMB data to our format
        marketData.value = data.data.map((item: any) => ({
          symbol: item.symbol,
          value: item.value,
          change: item.change || 0,
          changeText: formatChangeText(item.change || 0),
          changeColor: getChangeColor(item.change || 0)
        }))
        // Use the timestamp from the API response
        lastUpdated.value = new Date(data.timestamp)
        hasData.value = true
      } else {
        console.warn('Invalid market data response:', data)
        // Keep marketData empty to show fallback
        marketData.value = []
        hasData.value = false
      }
    } else {
      console.warn('Failed to fetch market data, using fallback')
      marketData.value = []
      hasData.value = false
    }
  } catch (error) {
    console.error('Error fetching market data:', error)
    marketData.value = []
    hasData.value = false
  } finally {
    // Always stop loading after attempt
    isLoading.value = false
  }
}

// Start periodic updates
const startPeriodicUpdates = (): void => {
  // Update every minute
  refreshInterval.value = window.setInterval(fetchMarketData, 1 * 60 * 1000) as unknown as number
}

const stopPeriodicUpdates = (): void => {
  if (refreshInterval.value) {
    window.clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// Lifecycle
onMounted(async () => {
  await fetchMarketData()
  startPeriodicUpdates()
})

onUnmounted(() => {
  stopPeriodicUpdates()
})
</script>
