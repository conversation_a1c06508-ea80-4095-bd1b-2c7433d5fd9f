import { test, expect } from '@playwright/test';

test.describe('Admin Panel Access Tests', () => {
	test('admin user should see Admin Panel link in dropdown', async ({ page }) => {
		// Navigate to login page
		await page.goto('/login');

		// Login as admin user (using correct test user email)
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'testpass123');
		await page.click('button[type="submit"]');

		// Wait for redirect and auth to complete
		await page.waitForLoadState('networkidle');
		await page.waitForTimeout(3000); // Wait for client auth to initialize

		// Hover over the user dropdown to make it visible
		await page.hover('[data-user-dropdown-trigger]');

		// Check if Admin Panel link exists and is visible
		const adminPanelLink = page.locator('a[href="/admin/dashboard"]');
		await expect(adminPanelLink).toBeVisible();
		await expect(adminPanelLink).toContainText('Admin Panel');
	});

	test('regular user should not see Admin Panel link in dropdown', async ({ page }) => {
		// Navigate to login page
		await page.goto('/login');

		// Login as regular user (using correct test user email)
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'testpass123');
		await page.click('button[type="submit"]');

		// Wait for redirect and auth to complete
		await page.waitForLoadState('networkidle');
		await page.waitForTimeout(3000); // Wait for client auth to initialize

		// Hover over the user dropdown to make it visible
		await page.hover('[data-user-dropdown-trigger]');

		// Check that Admin Panel link does not exist
		const adminPanelLink = page.locator('a[href="/admin/dashboard"]');
		await expect(adminPanelLink).not.toBeVisible();
	});

	test('admin can access admin dashboard directly', async ({ page }) => {
		// Navigate to login page
		await page.goto('/login');

		// Login as admin user (using correct test user email)
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'testpass123');
		await page.click('button[type="submit"]');

		// Wait for redirect and ensure we're logged in
		await page.waitForURL('/dashboard');
		await page.waitForLoadState('networkidle');

		// Navigate directly to admin dashboard
		await page.goto('/admin/dashboard');

		// Wait for the page to load fully
		await page.waitForLoadState('networkidle');

		// Verify we're on the admin dashboard page (not redirected)
		await expect(page).toHaveURL('/admin/dashboard');

		// Should see admin dashboard content - look for the most reliable element first
		// Use more specific selectors to avoid strict mode violations
		await expect(page.locator('text=Bekleyen Doğrulamalar')).toBeVisible();
		await expect(page.locator('text=Bugün Doğrulanan')).toBeVisible();
		await expect(page.locator('text=Haftalık Doğruluk')).toBeVisible();

		// Then check for the heading
		await expect(page.getByRole('heading', { name: 'Yönetici Paneli' })).toBeVisible();
	});

	test('regular user cannot access admin dashboard directly', async ({ page }) => {
		// Navigate to login page
		await page.goto('/login');

		// Login as regular user (using correct test user email)
		await page.fill('input[name="email"]', '<EMAIL>');
		await page.fill('input[name="password"]', 'testpass123');
		await page.click('button[type="submit"]');

		// Wait for redirect
		await page.waitForLoadState('networkidle');

		// Try to navigate directly to admin dashboard
		await page.goto('/admin/dashboard');

		// Should be redirected to login or access denied
		expect(page.url()).not.toContain('/admin/dashboard');
	});
});
