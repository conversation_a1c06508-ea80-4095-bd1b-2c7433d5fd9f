# 05_ui_ux_guidelines.md

This document provides guidelines for the User Interface (UI) and User Experience (UX) design of the "YouTube Economist Trust Score" platform. The goal is to create a modern, trustworthy, accessible, and user-friendly experience.

## 1. Design System & Prototyping

- **Design Philosophy (Suggestion):**
  &#x20; \* **Start Simple & Iterate:** The initial focus will be on delivering core functionalities with a clean, usable, and accessible interface. Extra convenience features and more elaborate UI elements can be added in later iterations based on user feedback and project priorities.
  &#x20; \* **Clarity First:** The design should prioritize making complex information (rankings, predictions, analyses) easy to understand at a glance.
  &#x20; \* **Trustworthiness & Professionalism:** The visual design should evoke a sense of reliability and authority, fitting for a platform that analyzes financial commentary.
  &#x20; \* **Content-Focused:** Similar to the provided examples, the UI should serve the content, making it easy to read and navigate.
  &#x20; \* **Modern & Clean:** A contemporary aesthetic, avoiding clutter and focusing on usability.
- **Primary Language:**
  &#x20; \* All UI text, content, and communication within the application will be in **Turkish**. This includes labels, navigation items, instructional text, error messages, etc. Localization for other languages is not in the initial scope.
- **Inspiration & Moodboard:**
  &#x20; \* The provided example sites will serve as primary inspiration:
  &#x20; \* `https://dipnot.tv/tr-TR/kategori/is-ve-ekonomi` (for its dark theme, content density, and professional feel)
  &#x20; \* `https://astro-news-six.vercel.app/` (for its minimalist, clean Astro-based approach and clear information presentation)
  &#x20; \* We can add more examples or specific UI patterns here as they are identified.
- **Design Tools & Prototyping (Suggestion):**
  &#x20; \* While no initial Figma/mockups exist, if detailed design work is undertaken before development, a tool like **Figma** or **Penpot** (open source) is recommended for creating wireframes and mockups.
  &#x20; \* For now, the AI will be guided to generate UI based on these guidelines and the principles of `shadcn-vue` / Tailwind CSS.
- **Component Library / Showcase (Suggestion):**
  &#x20; \* Consider using **Storybook** integrated with Vue and Astro later in the development process to document, visualize, and test UI components in isolation. This is especially helpful as the component library grows.
- **Branding Elements (Suggestion - To be defined by you later):**
  &#x20; \* **Logo:** (Placeholder - to be designed/sourced)
  &#x20; \* **Color Palette (Suggestion for Dark Mode Default):**
  &#x20; \* **Primary Backgrounds:** Dark grays or near-blacks (e.g., `#121212`, `#1A1A1A`).
  &#x20; \* **Content Backgrounds/Cards:** Slightly lighter grays to create separation (e.g., `#242424`, `#2A2A2A`).
  &#x20; \* **Primary Accent Color:** A clear, trustworthy, and accessible color (e.g., a muted Teal/Blue like `#4FD1C5` or `#3B82F6`). This will be used for links, calls to action, active states, and potentially positive indicators.
  &#x20; \* **Secondary Accent/Neutral Accent:** A lighter gray or off-white for text and icons.
  &#x20; \* **Semantic Colors (for data, scores, trends):**
  &#x20; \* **Positive/Upward Trend/Good Score:** A shade of Green (e.g., `#34D399`).
  &#x20; \* **Negative/Downward Trend/Bad Score:** A shade of Red (e.g., `#F87171`).
  &#x20; \* **Neutral/Warning:** A shade of Yellow/Orange (e.g., `#FBBF24`).
  &#x20; \* Ensure all color combinations meet WCAG AA contrast ratios.
  &#x20; \* **Typography (Suggestion):**
  &#x20; \* **Primary Font:** A clean, modern, readable sans-serif font (e.g., Inter, Roboto, Lato).
  &#x20; \* Clear hierarchy for headings, subheadings, body text, and captions/metadata.
  &#x20; \* Font weights should be used effectively to create emphasis and structure.

## 2. UI Components & Styling

- **Primary UI Approach:**
  &#x20; \* **CSS Framework:** **Tailwind CSS** (integrated via Astro) will be the utility-first foundation.
  &#x20; \* **Component Library Strategy:**
  &#x20; \* Evaluate and use **`shadcn-vue`** as the primary source for pre-built, composable Vue components, styled with Tailwind CSS. These components should be customizable to fit the defined color palette and typography.
  &#x20; \* If `shadcn-vue` lacks specific components, or for simpler Astro-native components, custom components will be built using Tailwind CSS, drawing inspiration from `shadcn-vue`'s composition principles and the aesthetics of HeroUI or the example websites.
- **Layout (Suggestion):**
  &#x20; \* Predominantly **card-based layouts** for displaying lists of economists, video analyses, investment tools, etc.
  &#x20; \* A **grid system** (achievable with Tailwind CSS) will be used for arranging content blocks and cards.
  &#x20; \* Generous use of **whitespace** to improve readability and reduce cognitive load.
- **Navigation:**
  &#x20; \* **Top Navigation Bar:**
  &#x20; \* The primary navigation will be a top bar.
  &#x20; \* **Fixed "Trending" (What's Hot) Item:** The first item in the top navigation will always be "Trending" (or an equivalent Turkish term for "What's Hot"). This links to the section showcasing the most discussed/relevant topics or videos.
  &#x20; \* **Dynamic Navigation Items:** The subsequent items in the top navigation bar will be dynamically generated based on the topics (e.g., specific investment tools, economists, or economic themes) that are most discussed or trending on the platform within the current week. These items will be ordered by their current relevance or discussion volume, appearing after "Trending".
  &#x20; \* The visual design of the navigation should be clean, easy to read, and clearly indicate the active/selected section. It should seamlessly integrate with the overall dark theme.
  &#x20; \* **Other Navigation Elements:**
  &#x20; \* Consider breadcrumbs for deeper sections if the site structure becomes complex.
  &#x20; \* Clear pagination controls for lists of items.
  &#x20; \* User account/profile navigation should be easily accessible (e.g., user avatar/menu in the top bar).
- **Dark Mode:**
  &#x20; \* **Default Theme:** Dark mode will be the default theme for the application.
  &#x20; \* **Light Mode:** Light mode will be an optional theme, potentially implemented in a later phase. The initial design and component styling should prioritize an excellent dark mode experience.
- **Iconography (Suggestion):**
  &#x20; \* **Icon Set:** **Heroicons** or **Lucide Icons**. Both are modern, SVG-based, and work well with Tailwind CSS.
  &#x20; \* **Integration:** Use inline SVGs for better control over styling and performance.
- **Responsiveness:**
  &#x20; \* **Mobile-First Approach:** Design and develop with mobile screen sizes in mind first, then scale up to tablet and desktop.
  &#x20; \* **Breakpoints:** Utilize Tailwind CSS's default breakpoints (`sm`, `md`, `lg`, `xl`, `2xl`), and ensure layouts adapt gracefully.
  &#x20; \* Content should always be readable and interactive elements easily tappable/clickable on all screen sizes.
- **Animation & Transitions (Suggestion):**
  &#x20; \* Keep animations and transitions **subtle and purposeful**.
  &#x20; \* Use for loading states, hover effects, and smooth changes in UI state.
  &#x20; \* Avoid overly distracting or lengthy animations. (e.g., use Tailwind's built-in transition utilities).

## 3. Accessibility (a11y)

Accessibility is a core requirement to ensure the platform is usable by everyone.

- **Target WCAG Level (Suggestion):** Aim for **WCAG 2.1 Level AA** conformance.
- **Key Accessibility Priorities:**
  &#x20; \* **Semantic HTML:** Use HTML elements according to their intended meaning.
  &#x20; \* **Keyboard Navigation:** All interactive elements must be fully navigable and operable using only the keyboard. Logical focus order is essential.
  &#x20; \* **ARIA Attributes:** Apply ARIA attributes correctly where semantic HTML alone is insufficient. Refer to WAI-ARIA Authoring Practices.
  &#x20; \* **Focus Management:** Ensure clear and visible focus indicators for all interactive elements.
  &#x20; \* **Color Contrast:** All text and meaningful graphical elements must meet WCAG AA contrast ratios. Use contrast checking tools.
  &#x20; \* **Alternative Text for Images:** All `<img>` tags or images conveying information must have descriptive `alt` text. Decorative images should have an empty `alt=""`.
  &#x20; \* **Form Accessibility:**
  &#x20; \* All form inputs must have associated, visible labels.
  &#x20; \* Provide clear instructions and feedback for form submission.
  &#x20; \* Ensure error messages are programmatically associated with their respective inputs.
  &#x20; \* **Content Readability:** Ensure typography choices and text layout contribute to easy readability.
- **Testing (Suggestion):**
  &#x20; \* Integrate accessibility checks into the development process.
  &#x20; \* Use browser extensions like **Axe DevTools** for automated testing.
  &#x20; \* Regularly test with **keyboard-only navigation**.
  &#x20; \* Perform basic **screen reader testing** (e.g., using NVDA, VoiceOver, or JAWS).
  &#x20; \* Lighthouse accessibility audits can also provide valuable insights.
