import { Hono } from 'hono';
import { getDbFromContext } from '../data';
import { getCuratedArticles, getFormattedArticleBySlug } from '../services';

const articles = new Hono();



// GET /api/articles/curated
articles.get('/curated', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				status: 'error',
				message: 'Database connection not available'
			}, 503);
		}

		// Extract limit from query params
		const limitParam = c.req.query('limit');
		const limit = limitParam ? parseInt(limitParam, 10) : 10;

		console.log('[API articles/curated] Fetching curated articles with limit:', limit);
		const articles = await getCuratedArticles(db, limit);

		console.log('[API articles/curated] Successfully fetched articles:', articles.length);
		return c.json({
			status: 'success',
			data: articles,
			message: 'Curated articles fetched successfully',
		}, 200);
	} catch (error) {
		console.error('Error fetching curated articles:', error);

		return c.json({
			status: 'error',
			message: error instanceof Error ? error.message : 'Unknown error',
			details: ['Failed to fetch curated articles'],
		}, 500);
	}
});

// GET /api/articles/:slug
articles.get('/:slug', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				status: 'error',
				message: 'Database connection not available'
			}, 503);
		}

		const slug = c.req.param('slug');
		if (!slug) {
			return c.json({
				status: 'error',
				message: 'Article slug is required'
			}, 400);
		}

		console.log('[API articles/:slug] Fetching article with slug:', slug);
		const article = await getFormattedArticleBySlug(db, slug);

		if (!article) {
			return c.json({
				status: 'error',
				message: 'Article not found'
			}, 404);
		}

		console.log('[API articles/:slug] Successfully fetched article:', article.title);
		return c.json({
			status: 'success',
			data: article,
			message: 'Article fetched successfully',
		}, 200);
	} catch (error) {
		console.error('Error fetching article:', error);

		return c.json({
			status: 'error',
			message: error instanceof Error ? error.message : 'Unknown error',
			details: ['Failed to fetch article'],
		}, 500);
	}
});

export default articles;
