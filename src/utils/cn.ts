import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Class Name Utility Function (cn)
 *
 * ⚠️  CURRENTLY UNUSED - This utility is defined but not used anywhere in the codebase.
 *
 * This utility combines clsx and tailwind-merge to provide intelligent CSS class merging.
 * It's a common pattern in modern React/Vue projects using Tailwind CSS.
 *
 * Purpose:
 * - clsx: Conditionally joins classNames together (handles arrays, objects, conditionals)
 * - tailwind-merge: Intelligently merges Tailwind CSS classes, resolving conflicts
 *
 * Example usage:
 * cn('px-2 py-1', 'px-4') // Returns: 'py-1 px-4' (px-4 overrides px-2)
 * cn('text-red-500', condition && 'text-blue-500') // Conditional classes
 * cn(['base-class', { 'active-class': isActive }]) // Array and object syntax
 *
 * This function would be essential for:
 * 1. Component libraries (like shadcn-vue) that need to merge default and custom classes
 * 2. Conditional styling based on props or state
 * 3. Preventing Tailwind class conflicts (e.g., multiple padding/margin classes)
 *
 * Dependencies: clsx (^2.1.1) and tailwind-merge (^3.3.0)
 *
 * STATUS: The project currently uses pure Tailwind CSS classes directly in components
 * without any component library. This file can be safely deleted unless you plan to
 * implement shadcn-vue or similar component library that requires class merging.
 *
 * Related unused dependencies that could also be removed:
 * - shadcn-vue (^2.2.0) - installed but never imported
 * - radix-vue (^1.9.17) - installed but never imported
 * - class-variance-authority (^0.7.1) - installed but never imported
 */
export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}
