/// <reference types="astro/client" />

import { defineMiddleware } from 'astro:middleware';
import { initDb } from './server/db';
import { dbContext } from './server/db/context';
import { getAuthContext } from './server/auth';

export const onRequest = defineMiddleware(async (context, next) => {
	// Handle both wrangler dev and astro dev environments
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const locals = context.locals as any;

	// Try multiple locations for D1 binding
	let D1 = null;

	// 1. Try Astro locals.runtime.env (wrangler dev with Cloudflare adapter)
	if (locals.runtime?.env?.DB) {
		D1 = locals.runtime.env.DB;
	}
	// 2. Try context.platform.env (wrangler dev alternative)
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	else if ((context as any).platform?.env?.DB) {
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		D1 = (context as any).platform.env.DB;
	}
	// 3. Try locals.DB directly (sometimes available)
	else if (locals.DB) {
		D1 = locals.DB;
	}

	if (D1) {
		try {
			const db = initDb(D1);
			locals.db = db;
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			locals.env = locals.runtime?.env || (context as any).platform?.env || {};

			// Initialize the global database context for services
			dbContext.setRuntimeDb(D1);

			// Add authentication context for Astro pages
			try {
				const authContext = await getAuthContext(context.request, db);
				locals.auth = authContext;
			} catch (error) {
				console.error('[Middleware] Error getting auth context:', error);
				// Set default auth context if there's an error
				locals.auth = {
					user: null,
					roles: ['ROLE_ANONYMOUS'],
					isAuthenticated: false
				};
			}
		} catch (error) {
			console.error('[Middleware] Error initializing database:', error);
		}
	} else {
		// If no database connection, still set default auth context
		locals.auth = {
			user: null,
			roles: ['ROLE_ANONYMOUS'],
			isAuthenticated: false
		};
	}

	return next();
});
