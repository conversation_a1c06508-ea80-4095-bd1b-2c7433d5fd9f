import { describe, it, expect } from 'vitest';

describe('PlatformStats Vue to Astro Migration', () => {
  it('should have removed the Vue PlatformStats component', async () => {
    const fs = await import('fs');
    const path = await import('path');

    // Check that the Vue component was removed
    const vueComponentPath = path.resolve('./src/components/vue/PlatformStats.vue');
    expect(fs.existsSync(vueComponentPath)).toBe(false);
  });

  it('should have updated index.astro with the better platform stats design', async () => {
    const fs = await import('fs');
    const path = await import('path');

    // Check that index.astro exists
    const indexPath = path.resolve('./src/pages/index.astro');
    expect(fs.existsSync(indexPath)).toBe(true);

    // Check that it contains the new design elements
    const content = fs.readFileSync(indexPath, 'utf-8');

    // Should contain the formatNumber function
    expect(content).toContain('const formatNumber = (num: number): string =>');

    // Should contain the enhanced platform stats section
    expect(content).toContain('Platform İstatistikleri');

    // Should contain the colored stat boxes
    expect(content).toContain('bg-blue-50 dark:bg-blue-900/20');
    expect(content).toContain('bg-green-50 dark:bg-green-900/20');
    expect(content).toContain('bg-purple-50 dark:bg-purple-900/20');
    expect(content).toContain('bg-orange-50 dark:bg-orange-900/20');

    // Should contain the formatNumber function calls
    expect(content).toContain('formatNumber(platformStats.economistsCount)');
    expect(content).toContain('formatNumber(platformStats.predictionsCount)');
    expect(content).toContain('formatNumber(platformStats.videosCount)');

    // Should contain Turkish labels
    expect(content).toContain('Ekonomist');
    expect(content).toContain('Tahmin');
    expect(content).toContain('Video');
    expect(content).toContain('Doğruluk');
  });

  it('should have proper number formatting logic', () => {
    // Test the number formatting logic
    const formatNumber = (num: number): string => {
      if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
      if (num >= 1000) return `${(num / 1000).toFixed(0)}K`;
      return num.toString();
    };

    expect(formatNumber(500)).toBe('500');
    expect(formatNumber(1500)).toBe('2K');
    expect(formatNumber(1500000)).toBe('1.5M');
    expect(formatNumber(2500000)).toBe('2.5M');
  });
});
