/**
 * Environment variables validation and configuration
 * This ensures all required environment variables are set and provides type safety
 */

import { z } from 'zod';

const envSchema = z.object({
	// Database
	DATABASE_URL: z.string().min(1, 'DATABASE_URL is required').optional(),
	CLOUDFLARE_D1_DATABASE_ID: z.string().optional(),

	// JWT
	JWT_SECRET: z.string().min(1, 'JWT_SECRET is required'),
	JWT_EXPIRES_IN: z.string().default('7d'),

	// LLM
	LLM_PROVIDER: z.enum(['openai', 'anthropic', 'cloudflare']).default('openai'),
	OPENAI_API_KEY: z.string().optional(),
	ANTHROPIC_API_KEY: z.string().optional(),

	// Cloudflare
	CLOUDFLARE_ACCOUNT_ID: z.string().optional(),
	CLOUDFLARE_API_TOKEN: z.string().optional(),
	VECTORIZE_INDEX_NAME: z.string().optional(),

	// Logging
	LOGFLARE_API_KEY: z.string().optional(),
	LOGFLARE_SOURCE_TOKEN: z.string().optional(),

	// Application
	NODE_ENV: z.enum(['development', 'staging', 'production', 'test']).default('development'),
	API_BASE_URL: z.string().url().default('http://localhost:4321'),
	CORS_ORIGINS: z.string().default('http://localhost:4321,http://localhost:3000'),

	// Rate Limiting
	RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
	RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
});

export type EnvConfig = z.infer<typeof envSchema>;

/**
 * Check if running in Cloudflare Workers environment
 */
export function isCloudflareWorkers(): boolean {
	return typeof globalThis.caches !== 'undefined' &&
		   'cloudflare' in globalThis;
}

/**
 * Check if running in local development mode (D1 via wrangler dev)
 */
export function isLocal(): boolean {
	const nodeEnv = getEnvironmentVariable('NODE_ENV');
	return nodeEnv === 'development' && !isCloudflareWorkers();
}

/**
 * Check if running in development mode (deployed to Workers)
 */
export function isDevelopment(): boolean {
	const nodeEnv = getEnvironmentVariable('NODE_ENV');
	return nodeEnv === 'development' && isCloudflareWorkers();
}

/**
 * Check if running in staging mode
 */
export function isStaging(): boolean {
	const nodeEnv = getEnvironmentVariable('NODE_ENV');
	return nodeEnv === 'staging';
}

/**
 * Check if running in production mode
 */
export function isProduction(): boolean {
	const nodeEnv = getEnvironmentVariable('NODE_ENV');
	return nodeEnv === 'production';
}

/**
 * Get the current environment type
 */
export function getEnvironmentType(): 'local' | 'development' | 'staging' | 'production' {
	if (isLocal()) return 'local';
	if (isDevelopment()) return 'development';
	if (isStaging()) return 'staging';
	if (isProduction()) return 'production';
	return 'local'; // fallback
}

/**
 * Check if environment uses D1 (all environments now use D1)
 */
export function usesD1(): boolean {
	return true; // All environments now use D1
}

/**
 * Get environment variable from the appropriate source
 */
export function getEnvironmentVariable(key: string): string | undefined {
	// In Cloudflare Workers, environment variables are passed via context
	// In Node.js, use process.env
	// In browser/development, use import.meta.env
	if (typeof process !== 'undefined' && process.env) {
		return process.env[key];
	}

	if (typeof import.meta !== 'undefined' && import.meta.env) {
		return import.meta.env[key];
	}

	return undefined;
}

/**
 * Validate and parse environment variables
 * @param env - Environment variables object (usually import.meta.env or process.env)
 * @returns Parsed and validated environment configuration
 */
export function validateEnv(env: Record<string, string | undefined>): EnvConfig {
	try {
		return envSchema.parse(env);
	} catch (error) {
		if (error instanceof z.ZodError) {
			const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
			throw new Error(`Environment validation failed:\n${missingVars.join('\n')}`);
		}
		throw error;
	}
}

/**
 * Get validated environment configuration
 * This should be called once at application startup
 */
export function getEnvConfig(): EnvConfig {
	// In production, use process.env; in development/browser, use import.meta.env
	const envSource = typeof process !== 'undefined' ? process.env : import.meta.env;
	return validateEnv(envSource);
}
