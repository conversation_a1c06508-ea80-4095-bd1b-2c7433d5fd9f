/**
 * Stats Queries
 *
 * SQL queries and database operations for statistics.
 * Contains both raw SQL for build scripts and Drizzle ORM queries.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import type { DbConnection } from '@/server/db';
import { economists, predictions, videos } from '@/server/db';
import { eq, count, avg, inArray } from 'drizzle-orm';
import { execSync } from 'child_process';

// ============================================================================
// RAW SQL QUERIES (for build scripts and external tools)
// ============================================================================

export const STATS_SQL = {
	/**
	 * Get comprehensive platform statistics
	 */
	platformStats: `
    SELECT
      (SELECT COUNT(*) FROM economists WHERE is_active = 1) as economistsCount,
      (SELECT COUNT(*) FROM predictions) as predictionsCount,
      (SELECT COUNT(*) FROM videos v INNER JOIN economists e ON v.economist_id = e.id WHERE e.is_active = 1 AND v.is_active = 1) as videosCount,
      (SELECT COUNT(*) FROM predictions WHERE status IN ('verified_correct', 'verified_incorrect')) as verifiedCount,
      (SELECT ROUND(AVG(accuracy_score) * 100) FROM economists WHERE accuracy_score IS NOT NULL AND is_active = 1) as accuracyPercentage
  `
		.replace(/\s+/g, ' ')
		.trim(),

	/**
	 * Get economist-specific statistics
	 */
	economistStats: (economistId: number) =>
		`
    SELECT
      COUNT(*) as total,
      COUNT(verified_at) as verified,
      COUNT(CASE WHEN status = 'verified_correct' THEN 1 END) as correct,
      AVG(CASE WHEN status IN ('verified_correct', 'verified_incorrect') THEN accuracy_score END) as avgAccuracy
    FROM predictions
    WHERE economist_id = ${economistId}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	/**
	 * Get category-wise statistics
	 */
	categoryStats: () =>
		`
    SELECT
      category,
      COUNT(*) as total,
      COUNT(verified_at) as verified,
      COUNT(CASE WHEN status = 'verified_correct' THEN 1 END) as correct,
      ROUND(AVG(CASE WHEN status IN ('verified_correct', 'verified_incorrect') THEN accuracy_score END) * 100) as accuracyRate
    FROM predictions
    WHERE category IS NOT NULL
    GROUP BY category
    ORDER BY total DESC
  `
			.replace(/\s+/g, ' ')
			.trim(),

	/**
	 * Get monthly prediction trends
	 */
	monthlyTrends: (months = 12) =>
		`
    SELECT
      strftime('%Y-%m', created_at) as month,
      COUNT(*) as total,
      COUNT(verified_at) as verified,
      COUNT(CASE WHEN status = 'verified_correct' THEN 1 END) as correct
    FROM predictions
    WHERE created_at >= datetime('now', '-${months} months')
    GROUP BY strftime('%Y-%m', created_at)
    ORDER BY month DESC
  `
			.replace(/\s+/g, ' ')
			.trim(),

	/**
	 * Get top performing economists
	 */
	topEconomists: (limit = 10) =>
		`
    SELECT
      e.id,
      e.name,
      e.accuracy_score as accuracyScore,
      e.trust_score as trustScore,
      e.total_predictions as totalPredictions,
      e.correct_predictions as correctPredictions
    FROM economists e
    WHERE e.is_active = 1 AND e.total_predictions > 0
    ORDER BY e.accuracy_score DESC, e.total_predictions DESC
    LIMIT ${limit}
  `
			.replace(/\s+/g, ' ')
			.trim(),

	/**
	 * Get verification queue statistics
	 */
	verificationQueueStats: () =>
		`
    SELECT
      COUNT(*) as totalPending,
      COUNT(CASE WHEN target_date < datetime('now') THEN 1 END) as overdue,
      COUNT(CASE WHEN target_date BETWEEN datetime('now') AND datetime('now', '+7 days') THEN 1 END) as dueSoon
    FROM predictions
    WHERE status = 'pending'
  `
			.replace(/\s+/g, ' ')
			.trim(),
};

// ============================================================================
// DRIZZLE ORM QUERIES
// ============================================================================

/**
 * Get comprehensive platform statistics
 */
export async function getPlatformStatistics(db: DbConnection) {
	// Execute all stat queries in parallel for better performance
	const [economistsResult, predictionsResult, videosResult, verifiedResult, accuracyResult] =
		await Promise.all([
			db.select({ count: count() }).from(economists).where(eq(economists.isActive, true)),
			db.select({ count: count() }).from(predictions),
			db.select({ count: count() }).from(videos),
			db
				.select({ count: count() })
				.from(predictions)
				.where(inArray(predictions.status, ['verified_correct', 'verified_incorrect'])),
			db
				.select({ value: avg(predictions.accuracyScore) })
				.from(predictions)
				.where(inArray(predictions.status, ['verified_correct', 'verified_incorrect'])),
		]);

	return {
		economistsCount: economistsResult[0]?.count || 0,
		predictionsCount: predictionsResult[0]?.count || 0,
		videosCount: videosResult[0]?.count || 0,
		verifiedCount: verifiedResult[0]?.count || 0,
		accuracyPercentage: Math.round(parseFloat(accuracyResult[0]?.value || '0') * 100),
	};
}

/**
 * Get economist-specific statistics
 */
export async function getEconomistStatistics(db: DbConnection, economistId: number) {
	const stats = await db
		.select({
			total: count(),
			verified: count(predictions.verifiedAt),
			correct: count(eq(predictions.status, 'verified_correct')),
			avgAccuracy: avg(predictions.accuracyScore),
		})
		.from(predictions)
		.where(eq(predictions.economistId, economistId));

	const result = stats[0] || { total: 0, verified: 0, correct: 0, avgAccuracy: 0 };

	return {
		...result,
		accuracyRate: result.verified > 0 ? Math.round((result.correct / result.verified) * 100) : 0,
		pendingPredictions: result.total - result.verified,
	};
}

/**
 * Get category-wise prediction statistics
 */
export async function getCategoryStatistics(db: DbConnection) {
	const categoryStats = await db
		.select({
			category: predictions.category,
			total: count(),
			verified: count(predictions.verifiedAt),
			correct: count(eq(predictions.status, 'verified_correct')),
			avgAccuracy: avg(predictions.accuracyScore),
		})
		.from(predictions)
		.groupBy(predictions.category);

	return categoryStats.map((stat) => ({
		...stat,
		accuracyRate: stat.verified > 0 ? Math.round((stat.correct / stat.verified) * 100) : 0,
		pendingPredictions: stat.total - stat.verified,
	}));
}

/**
 * Get verification queue statistics
 */
export async function getVerificationQueueStats(db: DbConnection) {
	const [totalResult, overdueResult, dueSoonResult] = await Promise.all([
		db.select({ count: count() }).from(predictions).where(eq(predictions.status, 'pending')),
		db.select({ count: count() }).from(predictions).where(eq(predictions.status, 'pending')),
		db.select({ count: count() }).from(predictions).where(eq(predictions.status, 'pending')),
	]);

	return {
		totalPending: totalResult[0]?.count || 0,
		overdue: overdueResult[0]?.count || 0, // TODO: Add proper date filtering
		dueSoon: dueSoonResult[0]?.count || 0, // TODO: Add proper date filtering
	};
}

/**
 * Get top performing economists
 */
export async function getTopPerformingEconomists(db: DbConnection, limit = 10) {
	return await db
		.select({
			id: economists.id,
			name: economists.name,
			accuracyScore: economists.accuracyScore,
			trustScore: economists.trustScore,
			totalPredictions: economists.totalPredictions,
			correctPredictions: economists.correctPredictions,
		})
		.from(economists)
		.where(eq(economists.isActive, true))
		.orderBy(economists.accuracyScore, economists.totalPredictions)
		.limit(limit);
}

// ============================================================================
// CALCULATION HELPERS
// ============================================================================

/**
 * Calculate accuracy score for a prediction
 */
export function calculateAccuracyScore(
	prediction: any,
	actualValue: number,
	status: string
): number {
	if (status !== 'verified_correct' && status !== 'verified_incorrect') {
		return 0;
	}

	if (status === 'verified_incorrect') {
		return 0;
	}

	// For correct predictions, calculate based on how close the prediction was
	try {
		const targetValue = JSON.parse(prediction.targetValue);
		if (typeof targetValue === 'number') {
			const difference = Math.abs(targetValue - actualValue);
			const relativeDifference = difference / Math.abs(actualValue);

			// Score from 0.5 to 1.0 based on accuracy
			return Math.max(0.5, 1.0 - relativeDifference);
		}
	} catch {
		// If we can't parse the target value, just return 1.0 for correct predictions
		return 1.0;
	}

	return 1.0;
}

/**
 * Calculate trust score for an economist
 */
export function calculateTrustScore(economistStats: any): number {
	const { total, verified, correct } = economistStats;

	if (total === 0) return 0;

	// Base score on verification rate and accuracy
	const verificationRate = verified / total;
	const accuracyRate = verified > 0 ? correct / verified : 0;

	// Weight verification rate and accuracy equally
	return Math.round((verificationRate * 0.5 + accuracyRate * 0.5) * 100) / 100;
}

// ============================================================================
// SQL EXECUTION HELPER
// ============================================================================

/**
 * Execute raw SQL query for statistics (used by build scripts)
 */
export function executeStatsSQL(query: string, dbName: string, isLocal = false): unknown {
	try {
		const localFlag = isLocal ? '--local' : '--remote';
		const command = `npx wrangler d1 execute ${dbName} ${localFlag} --json --command="${query}"`;

		const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
		const parsed = JSON.parse(output);
		return parsed[0].results;
	} catch (error) {
		console.error('Stats SQL execution failed:', error);
		return [];
	}
}
