# 06_ai_interaction.instructions.md

This document outlines the preferred interaction model and working guidelines for any AI coding assistant involved in this project. Adhering to these preferences will help ensure a smoother, more efficient, and predictable development workflow.

## Core Interaction Principles:

1. **Planning & Clarification:**
   &#x20; \* Always outline your plan or proposed approach before generating significant amounts of code for a new feature or complex task.
   &#x20; \* Ask clarifying questions if a request is ambiguous or lacks necessary detail. Do not make assumptions on critical aspects.
   &#x20; \* When providing options or different approaches, clearly explain the trade-offs of each.
   &#x20; \* When suggesting multiple approaches, indicate which one aligns best with the established project patterns and guidelines documented in other `.ai_context` files.

2. **Branch Management:**
   - **GitFlow Branching Strategy:** This project uses the [GitFlow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow) branching model. All feature development, releases, and hotfixes must follow the GitFlow workflow:
     - The `main` branch always reflects production-ready code.
     - The `develop` branch is the integration branch for features and the base for releases.
     - **Feature Branches:** Start from `develop` and use the format `feature/description-of-feature` (e.g., `feature/frontend-database-connection`).
     - **Release Branches:** Start from `develop` and use the format `release/x.y.z` for preparing production releases.
     - **Hotfix Branches:** Start from `main` and use the format `hotfix/description-of-fix` for urgent production fixes.
     - **Branch Naming:** Use kebab-case for branch names with clear, descriptive names that indicate the purpose of the work.
     - **Merging:** Feature branches are merged into `develop` via pull requests. Release branches are merged into both `main` and `develop`. Hotfixes are merged into both `main` and `develop`.
   - **Git Best Practices:** Ensure proper git workflow with meaningful commit messages throughout the development process.

3. **Development Approach:**
   &#x20; \* **Agile & Iterative Implementation:** Follow an agile approach. Start with general project structure creation first, then iteratively implement features one by one.
   &#x20; \* **Simplicity First:** Always start implementing features or solutions in the simplest viable way. Complexity can be added later if necessary.
   &#x20; \* **Phased Implementation:** If a requested feature or component seems overly complex, time-consuming for the current stage, or if its requirements are not yet clear, suggest deferring parts of it or the entire item to a later phase.
   &#x20; \* **Focus & Isolation:** Do not make unsolicited changes to unrelated files or parts of the codebase. Focus on the current task.
4. **Code Generation & Quality:**
   &#x20; \* **Modularity:** Prefer modular code. Break down complex logic into smaller, well-defined, and reusable functions, components, or modules, adhering to the architectural patterns in `03_coding_standards_and_patterns.md`.
   &#x20; \* **Adherence to Standards:**
   &#x20; \* Strictly adhere to the guidelines specified in `02_tech_stack_and_libraries.md` for technologies and versions. Prioritize using the exact library versions specified.
   &#x20; \* Follow all coding standards, naming conventions, and architectural patterns outlined in `03_coding_standards_and_patterns.md`.
   &#x20; \* For UI components, strictly adhere to the styling, structure, and accessibility principles outlined in `05_ui_ux_guidelines.md`.
   &#x20; \* Conform to the API design conventions specified in `04_api_design_conventions.md`.
   &#x20; \* **Comments:** Include concise comments explaining complex logic, non-obvious decisions, or the purpose of functions/modules.
   &#x20; \* **Library Awareness:** If a requested library or a specific version mentioned in `02_tech_stack_and_libraries.md` has well-known critical issues, is significantly outdated, or poses security risks, please point this out.
5. **Testing:**
   &#x20; \* **TDD Preference:** A Test-Driven Development (TDD) approach is preferred. Where appropriate, generate tests before the implementation code.
   &#x20; \* **Test Generation:** Strive to generate relevant unit tests (and integration test stubs where applicable) for any new code generated, aiming to maintain good test coverage. Follow the testing guidelines in `03_coding_standards_and_patterns.md`.
6. **Contextual Awareness:**
   &#x20; \* Regularly refer to the information provided in all documents within the `.ai_context` folder to ensure your contributions are aligned with the project's vision, architecture, standards, and UI/UX guidelines.
   &#x20; \* You can always refer to the `01_project_overview.md` file for a high-level understanding of the project, its goals, and its current state.
   &#x20; \* **AI Partner Memories:** Consult `09_memories.md` for established user preferences, architectural patterns, and workflow decisions. This living document captures learned patterns to improve collaboration efficiency and reduce repetitive questions.
   &#x20; \* **Contextual Updates:** If the context changes significantly during development (e.g., new requirements, architectural changes), update the relevant `.ai_context` files after discussing with the user and getting his/her approval. This ensures that all team members are aware of the changes and can adapt their work accordingly.
   &#x20; \* **Memory Updates:** When new patterns or preferences emerge during development, update `09_memories.md` to capture these insights for future reference.
   &#x20; \* **Contextual Relevance:** Ensure that any code or suggestions you provide are relevant to the current task and do not introduce unnecessary complexity or dependencies.

7. **Iteration Reporting:**
   &#x20; \* **Problem Check:** Before considering any task technically complete, always check the Problems tab for compilation errors, linting issues, or warnings. Ask the user if these should be resolved before proceeding.
   &#x20; \* **Completion Confirmation:** Before generating any iteration report, always ask the user to confirm if the current task or iteration is actually completed to their satisfaction. Do not assume completion based solely on technical criteria.
   &#x20; \* **Completion Documentation:** Only after user confirmation, update the table in the `08_development_roadmap` file with completed implementations and update the `07_previous_iterations.md` file with a comprehensive report of what was accomplished, including:
   - Summary of completed tasks and features
   - Key technical decisions made
   - Files created or modified
   - Dependencies installed or updated
   - Any issues encountered and how they were resolved
   - Current state of the project
   - Next steps or remaining tasks
     &#x20; \* **Report Brevity:** Keep iteration reports comprehensive but concise. Include all essential information but avoid excessive detail to maintain file readability and prevent context window overflow.
     &#x20; \* **Progress Tracking:** This helps maintain continuity across development sessions and provides a clear audit trail of project evolution.
8. **Roadmap Table Maintenance:**
   - At the **start and end of every iteration**, update the `08_development_roadmap.md` file in `.ai_context` to reflect the current status, priorities, and any new or completed tasks. This ensures the backlog and roadmap are always up to date and actionable.
