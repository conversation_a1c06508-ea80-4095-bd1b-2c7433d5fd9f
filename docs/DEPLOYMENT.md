# Deployment Guide

## 🚀 Quick Deployment

Deploy to any environment with one command. Each command automatically:
- Fetches fresh data from the correct D1 database
- Builds the site with that data
- Deploys to the correct environment

```bash
# Production
npm run deploy

# Staging
npm run deploy:staging

# Development
npm run deploy:dev
```

## 🏗️ Build Only (No Deploy)

```bash
npm run build:prod      # Production database data
npm run build:staging   # Staging database data
npm run build:dev       # Development database data
npm run build:local     # Local database data
```

## 🛡️ Environment Safety

- **Development**: Uses `economist-tracker-dev` D1 database
- **Staging**: Uses `economist-tracker-staging` D1 database
- **Production**: Uses `economist-tracker-prod` D1 database

Each environment is completely isolated - no risk of wrong data.

## ✅ Verification

After deployment, check:
- Build logs show correct environment name
- Homepage displays fresh data
- Health check passes automatically

## 🔧 Troubleshooting

**Build fails?** Check:
- `npx wrangler whoami` (authentication)
- Database exists and has data
- Network connectivity

**Wrong data?** This shouldn't happen with this system, but re-run the build command to fetch fresh data.

That's it! The system handles everything else automatically.
