{"name": "economist-accuracy-tracker-tr-web-app", "type": "module", "version": "0.0.1", "description": "YouTube Economist Trust Score - A web platform for tracking Turkish YouTube economist prediction accuracy", "scripts": {"dev": "astro dev", "build": "tsx scripts/fetch-build-data.ts --env=local && NODE_ENV=production astro build", "build:dev": "tsx scripts/fetch-build-data.ts --env=development && NODE_ENV=production astro build", "build:staging": "tsx scripts/fetch-build-data.ts --env=staging && NODE_ENV=production astro build", "build:prod": "tsx scripts/fetch-build-data.ts --env=production && NODE_ENV=production astro build", "build:local": "tsx scripts/fetch-build-data.ts --env=local && NODE_ENV=production astro build", "fetch:data": "tsx scripts/fetch-build-data.ts", "preview": "astro preview", "astro": "astro", "test": "npm run test:all", "test:all": "tsx scripts/run-all-tests.ts", "test:watch": "vitest", "test:ui": "vitest --ui", "test:unit": "vitest run tests/unit", "test:integration:raw": "vitest run tests/integration", "test:integration": "tsx scripts/run-integration-tests.ts", "test:e2e": "npm run db:test-users:local && playwright test", "test:e2e:ui": "npm run db:test-users:local && playwright test --ui", "lint": "eslint . --ext .js,.ts,.astro,.vue", "lint:fix": "eslint . --ext .js,.ts,.astro,.vue --fix", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "npx drizzle-kit generate", "db:migrate:local": "npx wrangler d1 migrations apply economist-tracker-local --local", "db:migrate:dev": "npx wrangler d1 migrations apply economist-tracker-dev --remote --env development", "db:migrate:staging": "npx wrangler d1 migrations apply economist-tracker-staging --remote --env staging", "db:migrate:prod": "npx wrangler d1 migrations apply economist-tracker-prod --remote --env production", "db:studio": "NODE_ENV=development npx drizzle-kit studio", "db:studio:prod": "NODE_ENV=production npx drizzle-kit studio", "db:seed:local": "npx wrangler d1 execute economist-tracker-local --local --file=./scripts/seed.sql", "db:seed:dev": "npx wrangler d1 execute economist-tracker-dev --remote --file=./scripts/seed.sql", "db:seed:staging": "npx wrangler d1 execute economist-tracker-staging --remote --file=./scripts/seed.sql", "db:test-users:local": "npx wrangler d1 execute economist-tracker-local --local --file=./scripts/create-e2e-test-users.sql", "db:test-users:dev": "npx wrangler d1 execute economist-tracker-dev --remote --file=./scripts/create-e2e-test-users.sql", "db:test-users:staging": "npx wrangler d1 execute economist-tracker-staging --remote --file=./scripts/create-e2e-test-users.sql", "setup:cloudflare": "tsx scripts/cloudflare-setup.ts", "setup:cloudflare:dev-staging": "tsx scripts/cloudflare-dev-staging-setup.ts", "setup:db": "tsx scripts/create-d1-databases.ts", "setup:validate-env": "tsx scripts/validate-environment.ts", "deploy": "tsx scripts/deploy.ts production", "deploy:staging": "tsx scripts/deploy.ts staging", "deploy:dev": "tsx scripts/deploy.ts development", "deploy:full:dev": "npm run db:migrate:dev && npm run db:seed:dev && npm run deploy:dev", "deploy:full:staging": "npm run db:migrate:staging && npm run db:seed:staging && npm run deploy:staging", "deploy:full:prod": "npm run db:migrate:prod && npm run deploy"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/cloudflare": "^12.5.3", "@astrojs/tailwind": "^6.0.2", "@astrojs/vue": "^5.1.0", "@iconify-json/mdi": "^1.2.3", "@iconify/vue": "^5.0.0", "@libsql/client": "^0.15.9", "@vueuse/core": "^13.3.0", "astro": "^5.8.1", "astro-icon": "^1.1.5", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.1", "drizzle-orm": "^0.44.1", "hono": "^4.7.11", "jose": "^6.0.11", "lucide-vue-next": "^0.511.0", "radix-vue": "^1.9.17", "shadcn-vue": "^2.2.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vue": "^3.5.16", "zod": "^3.25.49"}, "devDependencies": {"@eslint/js": "^9.28.0", "@playwright/test": "^1.52.0", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.15.29", "@types/node-fetch": "^2.6.12", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitest/ui": "^3.2.0", "astro-eslint-parser": "^1.2.2", "drizzle-kit": "^0.31.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-vue": "^10.1.0", "jsdom": "^26.1.0", "node-fetch": "^3.3.2", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "tsx": "^4.19.4", "vitest": "^3.2.0", "vue-eslint-parser": "^10.1.3", "wrangler": "^4.18.0"}}