import { Hono } from 'hono';
import { count, sum, eq } from 'drizzle-orm';
import { economists, predictions, videos } from '@/server/db/schema';
import { getDbFromContext } from '../data';
import type { PlatformStats } from '@/types/index';

const statsRouter = new Hono();

// GET /api/stats - Get platform statistics
statsRouter.get('/', async (c) => {
	try {
		const db = getDbFromContext(c);
		if (!db) {
			return c.json({
				success: false,
				error: 'Database connection not available'
			}, 503);
		}

		const [
			economistsResult,
			predictionsResult,
			videosResult,
			verifiedResult,
			correctPredictionsResult,
			totalPredictionsResult
		] = await Promise.all([
			db.select({ count: count() }).from(economists),
			db.select({ count: count() }).from(predictions),
			db.select({ count: count() }).from(videos),
			db.select({ count: count() }).from(economists).where(eq(economists.isVerified, true)),
			db.select({ sum: sum(economists.correctPredictions) }).from(economists),
			db.select({ sum: sum(economists.totalPredictions) }).from(economists)
		]);

		const totalCorrect = Number(correctPredictionsResult[0]?.sum || 0);
		const totalPredictions = Number(totalPredictionsResult[0]?.sum || 0);
		const accuracyPercentage = totalPredictions > 0 ? Math.round((totalCorrect / totalPredictions) * 100) : 0;

		const stats: PlatformStats = {
			economistsCount: economistsResult[0]?.count || 0,
			predictionsCount: predictionsResult[0]?.count || 0,
			videosCount: videosResult[0]?.count || 0,
			verifiedCount: verifiedResult[0]?.count || 0,
			accuracyPercentage: accuracyPercentage,
		};

		return c.json({
			success: true,
			data: stats
		});

	} catch (error) {
		console.error('Error fetching platform stats:', error);
		return c.json({
			success: false,
			error: 'Failed to fetch platform stats',
			message: error instanceof Error ? error.message : 'Internal Server Error'
		}, 500);
	}
});

export default statsRouter;
