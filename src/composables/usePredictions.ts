import { ref, reactive } from 'vue';
import { apiEndpoints, buildApiUrl } from '@/lib/api-config';

// Ensure global types are available
// Temporarily commented out to debug Worker fetch issue
// declare const fetch: typeof globalThis.fetch;
declare const URLSearchParams: typeof globalThis.URLSearchParams;

interface TargetValue {
	target?: number;
	min?: number;
	max?: number;
	threshold?: number;
	direction?: string;
	[key: string]: unknown;
}

interface Economist {
	id: number;
	name: string;
	avatarUrl: string;
	isVerified: boolean;
}

interface Video {
	id: number;
	title: string;
	youtubeId: string;
	thumbnailUrl: string;
	publishedAt: string;
	youtubeUrl: string;
}

interface Prediction {
	id: number;
	title: string;
	description: string;
	category: string;
	predictionType: string;
	targetValue: TargetValue;
	targetDate: string;
	confidenceLevel: number | null;
	status: 'pending' | 'verified_correct' | 'verified_incorrect' | 'expired';
	accuracyScore: number | null;
	verifiedAt: string | null;
	tags: string[];
	extractedAt: string;
	createdAt: string;
	economist: Economist;
	video: Video;
}

interface PredictionsResponse {
	success: boolean;
	data: Prediction[];
	pagination: {
		page: number;
		limit: number;
		totalCount: number;
		totalPages: number;
		hasNextPage: boolean;
		hasPreviousPage: boolean;
	};
	stats: {
		totalPredictions: number;
		verifiedPredictions: number;
		averageAccuracy: number | null;
		categories: string[];
	};
	filters: {
		status: string | null;
		category: string | null;
		economistId: string | null;
	};
	message: string;
}

interface PredictionsFilters {
	page?: number;
	limit?: number;
	status?: string;
	category?: string;
	economistId?: string;
}

export function usePredictions() {
	const predictions = ref<Prediction[]>([]);
	const loading = ref(false);
	const error = ref<string | null>(null);
	const pagination = reactive({
		page: 1,
		limit: 10,
		totalCount: 0,
		totalPages: 0,
		hasNextPage: false,
		hasPreviousPage: false,
	});
	const stats = reactive({
		totalPredictions: 0,
		verifiedPredictions: 0,
		averageAccuracy: null as number | null,
		categories: [] as string[],
	});

	const fetchPredictions = async (filters: PredictionsFilters = {}) => {
		loading.value = true;
		error.value = null;

		try {
			const params = new URLSearchParams();

			if (filters.page) params.append('page', filters.page.toString());
			if (filters.limit) params.append('limit', filters.limit.toString());
			if (filters.status) params.append('status', filters.status);
			if (filters.category) params.append('category', filters.category);
			if (filters.economistId) params.append('economistId', filters.economistId);

			const response = await fetch(buildApiUrl(apiEndpoints.predictions.list, Object.fromEntries(params)));

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data: PredictionsResponse = await response.json();

			if (data.success) {
				predictions.value = data.data;

				// Update pagination
				Object.assign(pagination, data.pagination);

				// Update stats
				Object.assign(stats, data.stats);
			} else {
				throw new Error(data.message || 'Failed to fetch predictions');
			}
		} catch (err) {
			console.error('Error fetching predictions:', err);
			error.value = err instanceof Error ? err.message : 'An unknown error occurred';
			predictions.value = [];
		} finally {
			loading.value = false;
		}
	};

	const nextPage = () => {
		if (pagination.hasNextPage) {
			fetchPredictions({ page: pagination.page + 1, limit: pagination.limit });
		}
	};

	const previousPage = () => {
		if (pagination.hasPreviousPage) {
			fetchPredictions({ page: pagination.page - 1, limit: pagination.limit });
		}
	};

	const goToPage = (page: number) => {
		if (page >= 1 && page <= pagination.totalPages) {
			fetchPredictions({ page, limit: pagination.limit });
		}
	};

	const filterByStatus = (status: string) => {
		fetchPredictions({ status, page: 1, limit: pagination.limit });
	};

	const filterByCategory = (category: string) => {
		fetchPredictions({ category, page: 1, limit: pagination.limit });
	};

	const filterByEconomist = (economistId: string) => {
		fetchPredictions({ economistId, page: 1, limit: pagination.limit });
	};

	const clearFilters = () => {
		fetchPredictions({ page: 1, limit: pagination.limit });
	};

	return {
		predictions,
		loading,
		error,
		pagination,
		stats,
		fetchPredictions,
		nextPage,
		previousPage,
		goToPage,
		filterByStatus,
		filterByCategory,
		filterByEconomist,
		clearFilters,
	};
}
