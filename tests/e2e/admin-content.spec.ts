import { test, expect } from '@playwright/test';

// Test the admin content management page
test.describe('Admin Content Management', () => {
  test.describe('Access Control', () => {
    test('should allow admin access to content management page', async ({ page }) => {
      // Log in as admin
      await page.goto('/login');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'testpass123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');

      // Navigate to content management
      await page.goto('/admin/content');

      // Should successfully load the page
      await expect(page.getByRole('heading', { name: '<PERSON><PERSON><PERSON><PERSON>önetim<PERSON>' })).toBeVisible();
      await expect(page.locator('text=Videolar, tahminler ve içerik denetimi')).toBeVisible();
    });

    test('should allow moderator access to content management page', async ({ page }) => {
      // Log in as moderator
      await page.goto('/login');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'testpass123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');

      // Navigate to content management
      await page.goto('/admin/content');

      // Should successfully load the page
      await expect(page.getByRole('heading', { name: 'İçerik Yönetimi' })).toBeVisible();
      await expect(page.locator('text=Videolar, tahminler ve içerik denetimi')).toBeVisible();
    });

    test('should deny regular user access to content management page', async ({ page }) => {
      // Log in as regular user
      await page.goto('/login');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'testpass123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');

      // Try to access content management
      await page.goto('/admin/content');

      // Should be redirected to dashboard with error
      await page.waitForURL('/dashboard?error=access_denied');
    });
  });

  test.describe('Content Statistics', () => {
    test('should display content statistics correctly', async ({ page }) => {
      // Log in as admin
      await page.goto('/login');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'testpass123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');

      // Navigate to content management
      await page.goto('/admin/content');
      await expect(page.getByRole('heading', { name: 'İçerik Yönetimi' })).toBeVisible();

      // Check that statistics are displayed
      await expect(page.locator('text=Toplam Tahmin')).toBeVisible();
      await expect(page.locator('text=Toplam Video')).toBeVisible();
      await expect(page.locator('text=İşlenmiş Video')).toBeVisible();
      await expect(page.locator('text=Bekleyen İşlem')).toBeVisible();

      // Verify that statistics show numbers (not just 0)
      const stats = await page.locator('.text-3xl.font-bold').allTextContents();
      const hasData = stats.some(stat => stat !== '0');
      expect(hasData).toBe(true); // At least one stat should have data from seeded database
    });
  });

  test.describe('Content Tables', () => {
    test('should display predictions table', async ({ page }) => {
      // Log in as admin
      await page.goto('/login');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'testpass123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');

      // Navigate to content management
      await page.goto('/admin/content');
      await expect(page.getByRole('heading', { name: 'İçerik Yönetimi' })).toBeVisible();

      // Find the "Son Tahminler" heading and locate the table in the same card
      const predictionsHeading = page.getByRole('heading', { name: 'Son Tahminler' });
      await expect(predictionsHeading).toBeVisible();

      // Find the card containing the predictions table
      const predictionsCard = page.locator('div.bg-white:has(h3:text("Son Tahminler"))');
      const predictionsTable = predictionsCard.locator('table');
      await expect(predictionsTable).toBeVisible();

      // Check table headers using text content within thead
      await expect(predictionsTable.locator('thead th:has-text("Tahmin")')).toBeVisible();
      await expect(predictionsTable.locator('thead th:has-text("Durum")')).toBeVisible();
      await expect(predictionsTable.locator('thead th:has-text("İşlem")')).toBeVisible();

      // Check if there are rows in the table (should be at least 1 from seeded data)
      const rows = await predictionsTable.locator('tbody tr').count();
      expect(rows).toBeGreaterThan(0);
    });

    test('should display videos table', async ({ page }) => {
      // Log in as admin
      await page.goto('/login');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'testpass123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');

      // Navigate to content management
      await page.goto('/admin/content');
      await expect(page.getByRole('heading', { name: 'İçerik Yönetimi' })).toBeVisible();

      // Find the "Son Videolar" heading and locate the table in the same card
      const videosHeading = page.getByRole('heading', { name: 'Son Videolar' });
      await expect(videosHeading).toBeVisible();

      // Find the card containing the videos table
      const videosCard = page.locator('div.bg-white:has(h3:text("Son Videolar"))');
      const videosTable = videosCard.locator('table');
      await expect(videosTable).toBeVisible();

      // Check table headers using text content within thead
      await expect(videosTable.locator('thead th:has-text("Video")')).toBeVisible();
      await expect(videosTable.locator('thead th:has-text("Durum")')).toBeVisible();
      await expect(videosTable.locator('thead th:has-text("İşlem")')).toBeVisible();

      // Check for the "Add Video" button
      await expect(page.getByRole('button', { name: '+ Video Ekle' })).toBeVisible();

      // Check if there are rows in the table (should be at least 1 from seeded data)
      const rows = await videosTable.locator('tbody tr').count();
      expect(rows).toBeGreaterThan(0);
    });
  });

  test.describe('Navigation', () => {
    test('should navigate back to admin panel', async ({ page }) => {
      // Log in as admin
      await page.goto('/login');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'testpass123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');

      // Navigate to content management
      await page.goto('/admin/content');
      await expect(page.getByRole('heading', { name: 'İçerik Yönetimi' })).toBeVisible();

      // Click back to admin panel
      await page.click('text=← Admin Panel');
      await page.waitForURL('/admin/dashboard');
      await expect(page.getByRole('heading', { name: 'Yönetici Paneli' })).toBeVisible();
    });
  });
});
