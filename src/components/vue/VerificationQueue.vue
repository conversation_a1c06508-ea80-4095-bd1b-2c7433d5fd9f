<template>
	<div class="space-y-6">
		<!-- Header with Statistics -->
		<div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
			<h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
				Prediction Verification Queue
			</h2>

			<!-- Loading State -->
			<div v-if="isLoading" class="text-center py-8">
				<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
				<p class="text-gray-500 dark:text-gray-400 mt-2">Loading predictions...</p>
			</div>

			<!-- Error State -->
			<div v-else-if="error" class="text-center py-8">
				<div class="text-red-600 dark:text-red-400 mb-4">
					<svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<p class="text-lg font-medium">Failed to load predictions</p>
				</div>
				<button
					class="px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-md font-medium"
					@click="() => fetchPredictions()"
				>
					Try Again
				</button>
			</div>

			<!-- Statistics Dashboard -->
			<div v-else-if="verificationStats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
				<div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-red-100 dark:bg-red-800 rounded-md flex items-center justify-center">
								<span class="text-red-600 dark:text-red-400 font-semibold text-sm">!</span>
							</div>
						</div>
						<div class="ml-3">
							<p class="text-sm font-medium text-red-800 dark:text-red-200">High Priority</p>
							<p class="text-2xl font-semibold text-red-900 dark:text-red-100">{{ verificationStats.highPriority }}</p>
						</div>
					</div>
				</div>

				<div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-800 rounded-md flex items-center justify-center">
								<span class="text-yellow-600 dark:text-yellow-400 font-semibold text-sm">⚠</span>
							</div>
						</div>
						<div class="ml-3">
							<p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Medium Priority</p>
							<p class="text-2xl font-semibold text-yellow-900 dark:text-yellow-100">{{ verificationStats.mediumPriority }}</p>
						</div>
					</div>
				</div>

				<div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-green-100 dark:bg-green-800 rounded-md flex items-center justify-center">
								<span class="text-green-600 dark:text-green-400 font-semibold text-sm">✓</span>
							</div>
						</div>
						<div class="ml-3">
							<p class="text-sm font-medium text-green-800 dark:text-green-200">Low Priority</p>
							<p class="text-2xl font-semibold text-green-900 dark:text-green-100">{{ verificationStats.lowPriority }}</p>
						</div>
					</div>
				</div>

				<div class="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4">
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<div class="w-8 h-8 bg-amber-100 dark:bg-amber-800 rounded-md flex items-center justify-center">
								<span class="text-amber-600 dark:text-amber-400 font-semibold text-sm">#</span>
							</div>
						</div>
						<div class="ml-3">
							<p class="text-sm font-medium text-amber-800 dark:text-amber-200">Total Queue</p>
							<p class="text-2xl font-semibold text-amber-900 dark:text-amber-100">{{ verificationStats.totalNeedingVerification }}</p>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Empty State -->
		<div v-if="!isLoading && !error && predictions.length === 0" class="text-center py-12">
			<div class="text-gray-400 dark:text-gray-500 mb-4">
				<svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
				<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No predictions need verification</h3>
				<p class="text-gray-500 dark:text-gray-400">All predictions are up to date!</p>
			</div>
		</div>

		<!-- Verification Cards -->
		<div v-else class="space-y-6">
			<VerificationCard
				v-for="prediction in predictions"
				:key="prediction.id"
				:prediction="prediction"
				@verified="handleVerification"
				@skipped="handleSkip"
			/>
		</div>

		<!-- Pagination -->
		<div
			v-if="pagination && pagination.totalPages > 1"
			class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
			<div class="flex items-center justify-between">
				<div class="text-sm text-gray-700 dark:text-gray-300">
					Showing {{ ((pagination.page - 1) * pagination.limit) + 1 }} to
					{{ Math.min(pagination.page * pagination.limit, pagination.totalCount) }} of
					{{ pagination.totalCount }} predictions
				</div>

				<div class="flex items-center gap-2">
					<button
						:disabled="!pagination.hasPreviousPage"
						class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm
							   bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300
							   hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
						@click="goToPage(pagination.page - 1)"
					>
						Previous
					</button>

					<span class="px-3 py-1 text-sm text-gray-700 dark:text-gray-300">
						Page {{ pagination.page }} of {{ pagination.totalPages }}
					</span>

					<button
						:disabled="!pagination.hasNextPage"
						class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm
							   bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300
							   hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
						@click="goToPage(pagination.page + 1)"
					>
						Next
					</button>
				</div>
			</div>
		</div>

		<!-- Bulk Actions -->
		<div
			v-if="!isLoading && predictions.length > 0"
			class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
			<h3 class="font-medium text-gray-900 dark:text-white mb-3">Bulk Actions</h3>
			<div class="flex flex-col sm:flex-row gap-3">
				<button
					class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md font-medium"
					@click="markAllExpired"
				>
					Mark All as Expired
				</button>
				<button
					class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium"
					@click="exportToCSV"
				>
					Export to CSV
				</button>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import VerificationCard from './VerificationCard.vue'
import { apiEndpoints, buildApiUrl } from '@/lib/api-config'

// Types
interface Prediction {
	id: number
	title: string
	description: string
	category: string
	predictionType: string
	targetValue: any
	targetDate: string
	confidenceLevel: number | null
	daysSinceTarget: number
	verificationPriority: 'high' | 'medium' | 'low'
	economist: {
		id: number
		name: string
		isVerified: boolean
		trustScore: number | null
	}
	video: {
		youtubeUrl: string
	}
}

interface VerificationStats {
	totalNeedingVerification: number
	highPriority: number
	mediumPriority: number
	lowPriority: number
	categoryBreakdown: Record<string, number>
}

interface Pagination {
	page: number
	limit: number
	totalCount: number
	totalPages: number
	hasNextPage: boolean
	hasPreviousPage: boolean
}

// Reactive state
const predictions = ref<Prediction[]>([])
const verificationStats = ref<VerificationStats | null>(null)
const pagination = ref<Pagination | null>(null)
const isLoading = ref(false)
const error = ref<string | null>(null)
const currentPage = ref(1)

// Methods
const fetchPredictions = async (page = 1) => {
	isLoading.value = true
	error.value = null

	try {
		const response = await fetch(buildApiUrl(apiEndpoints.predictions.verificationQueue, { page: page.toString(), limit: '5' }))

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`)
		}

		const data = await response.json() as any

		if (data.success && data.data) {
			predictions.value = data.data
			verificationStats.value = data.verificationStats
			pagination.value = data.pagination
			currentPage.value = page
		} else {
			error.value = data.message || data.error || 'Failed to fetch predictions'
		}
	} catch (err) {
		error.value = 'Network error occurred'
		console.error('Error fetching predictions:', err)
	} finally {
		isLoading.value = false
	}
}

const handleVerification = async (predictionId: number, verificationData: any) => {
	try {
		const response = await fetch(apiEndpoints.predictions.verify(predictionId), {
			method: 'PATCH',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(verificationData),
		})

		const result = await response.json() as any

		if (result.success) {
			// Remove the verified prediction from the list
			predictions.value = predictions.value.filter(p => p.id !== predictionId)

			// Update statistics
			if (verificationStats.value) {
				verificationStats.value.totalNeedingVerification -= 1

				// Find the prediction and update priority counts
				const prediction = predictions.value.find(p => p.id === predictionId)
				if (prediction) {
					verificationStats.value[`${prediction.verificationPriority}Priority`] -= 1
				}
			}

			// Show success message
			console.log('Verification submitted successfully:', result.data)
		} else {
			console.error('Verification failed:', result.error || result.message)
		}
	} catch (error) {
		console.error('Error submitting verification:', error)
	}
}

const handleSkip = (predictionId: number) => {
	// Remove from current view
	predictions.value = predictions.value.filter(p => p.id !== predictionId)
}

const goToPage = (page: number) => {
	if (page >= 1 && pagination.value && page <= pagination.value.totalPages) {
		fetchPredictions(page)
	}
}

const markAllExpired = async () => {
	if (confirm('Are you sure you want to mark all visible predictions as expired?')) {
		const bulkVerifications = predictions.value.map(p => ({
			id: p.id,
			status: 'expired',
			verificationMethod: 'bulk_expired',
			verificationNotes: 'Bulk marked as expired due to extended delay'
		}))

		try {
			const response = await fetch(apiEndpoints.predictions.verifyBulk, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ verifications: bulkVerifications }),
			})

			const result = await response.json() as any

			if (result.success) {
				console.log('Bulk verification completed:', result.data?.summary || result.data)
				fetchPredictions(currentPage.value) // Refresh the list
			} else {
				console.error('Bulk verification failed:', result.error || result.message)
			}
		} catch (error) {
			console.error('Error in bulk verification:', error)
		}
	}
}

const exportToCSV = () => {
	if (predictions.value.length === 0) return

	const headers = ['ID', 'Title', 'Economist', 'Category', 'Type', 'Target Date', 'Priority', 'Days Overdue']
	const rows = predictions.value.map(p => [
		p.id,
		p.title,
		p.economist.name,
		p.category,
		p.predictionType,
		new Date(p.targetDate).toLocaleDateString('tr-TR'),
		p.verificationPriority,
		p.daysSinceTarget
	])

	const csvContent = [headers, ...rows]
		.map(row => row.map(field => `"${field}"`).join(','))
		.join('\n')

	const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
	const link = document.createElement('a')
	const url = URL.createObjectURL(blob)
	link.setAttribute('href', url)
	link.setAttribute('download', `verification-queue-${new Date().toISOString().split('T')[0]}.csv`)
	link.style.visibility = 'hidden'
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)
}

// Initialize
onMounted(() => {
	fetchPredictions()
})
</script>
