import { describe, it, expect } from 'vitest';

describe('MonthlyRankings Vue to Astro Migration', () => {
  it('should have removed the Vue MonthlyRankings component', async () => {
    const fs = await import('fs');
    const path = await import('path');

    // Check that the Vue component was removed
    const vueComponentPath = path.resolve('./src/components/vue/MonthlyRankings.vue');
    expect(fs.existsSync(vueComponentPath)).toBe(false);
  });

  it('should have updated index.astro with the better design', async () => {
    const fs = await import('fs');
    const path = await import('path');

    // Check that index.astro exists
    const indexPath = path.resolve('./src/pages/index.astro');
    expect(fs.existsSync(indexPath)).toBe(true);

    // Check that it contains the new design elements
    const content = fs.readFileSync(indexPath, 'utf-8');

    // Should contain the improved section title
    expect(content).toContain('Ayın Ekonomistleri');
    expect(content).toContain('Son 30 Gün');

    // Should contain the rank badge styling
    expect(content).toContain('rounded-full flex items-center justify-center');

    // Should contain the trust score section
    expect(content).toContain('güven skoru');

    // Should contain the "Tüm Ekonomistleri Gör" button
    expect(content).toContain('Tüm Ekonomistleri Gör');
  });
});
