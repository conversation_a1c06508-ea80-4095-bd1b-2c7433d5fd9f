import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';

export const economists = sqliteTable('economists', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	name: text('name').notNull(),
	bio: text('bio'),
	youtubeChannelId: text('youtube_channel_id').notNull().unique(),
	youtubeChannelName: text('youtube_channel_name').notNull(),
	youtubeChannelUrl: text('youtube_channel_url').notNull(),
	avatarUrl: text('avatar_url'),
	subscriberCount: integer('subscriber_count'),
	totalVideos: integer('total_videos').default(0),
	totalPredictions: integer('total_predictions').default(0),
	correctPredictions: integer('correct_predictions').default(0),
	accuracyScore: real('accuracy_score').default(0),
	trustScore: real('trust_score').default(0),
	isVerified: integer('is_verified', { mode: 'boolean' }).default(false),
	isActive: integer('is_active', { mode: 'boolean' }).default(true),
	lastAnalyzedAt: integer('last_analyzed_at', { mode: 'timestamp' }),
	createdAt: integer('created_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
	updatedAt: integer('updated_at', { mode: 'timestamp' }).$defaultFn(() => new Date()),
});
